const prisma = require("../database/prisma/getPrismaClient");

/**
 * Seeder to fix removedHistory arrays for existing reviews
 * This script populates removedHistory with removedAt dates for reviews
 * that were processed before the removedHistory feature was implemented
 */
async function fixRemovedHistorySeeder() {
    console.log("🔄 Starting removedHistory seeder...");

    try {
        // Find all reviews that have removedAt dates but empty removedHistory arrays
        const reviewsToFix = await prisma.review.findMany({
            where: {
                removedAt: {
                    not: null
                },
                OR: [
                    {
                        removedHistory: {
                            equals: []
                        }
                    },
                    {
                        removedHistory: {
                            equals: "[]"
                        }
                    }
                ]
            },
            select: {
                id: true,
                reviewId: true,
                removedAt: true,
                removedHistory: true
            }
        });

        console.log(`📊 Found ${reviewsToFix.length} reviews with removedAt dates but empty removedHistory arrays`);

        if (reviewsToFix.length === 0) {
            console.log("✅ No reviews need fixing. All removedHistory arrays are properly populated.");
            return;
        }

        let updatedCount = 0;
        let errorCount = 0;

        // Process each review
        for (const review of reviewsToFix) {
            try {
                // Create the removedHistory array with the removedAt date
                const removedHistory = [review.removedAt];

                // Update the review with the populated removedHistory
                await prisma.review.update({
                    where: { id: review.id },
                    data: {
                        removedHistory: removedHistory
                    }
                });

                updatedCount++;

                if (updatedCount % 100 === 0) {
                    console.log(`✅ Updated ${updatedCount} reviews...`);
                }

            } catch (error) {
                console.error(`❌ Error updating review ${review.reviewId}:`, error.message);
                errorCount++;
            }
        }

        console.log(`\n🎉 Seeder completed!`);
        console.log(`✅ Successfully updated: ${updatedCount} reviews`);
        if (errorCount > 0) {
            console.log(`❌ Errors encountered: ${errorCount} reviews`);
        }

        // Verify the fix
        const remainingReviewsToFix = await prisma.review.count({
            where: {
                removedAt: {
                    not: null
                },
                OR: [
                    {
                        removedHistory: {
                            equals: []
                        }
                    },
                    {
                        removedHistory: {
                            equals: "[]"
                        }
                    }
                ]
            }
        });

        if (remainingReviewsToFix === 0) {
            console.log("✅ Verification successful: All removedHistory arrays are now properly populated!");
        } else {
            console.log(`⚠️  Warning: ${remainingReviewsToFix} reviews still have empty removedHistory arrays`);
        }

    } catch (error) {
        console.error("❌ Seeder failed:", error);
        throw error;
    }
}

/**
 * Run the seeder
 */
async function main() {
    try {
        await fixRemovedHistorySeeder();
    } catch (error) {
        console.error("❌ Main execution failed:", error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { fixRemovedHistorySeeder };
