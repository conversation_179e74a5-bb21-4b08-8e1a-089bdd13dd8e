require('dotenv').config();

const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

/**
 * Function to print all emails without campaignId
 */
async function printEmailsWithoutCampaignId() {
  try {
    console.log("🔍 Finding emails without campaignId...");
    
    const emailsWithoutCampaignId = await prisma.email.findMany({
      where: {
        OR: [
          { campaingId: null },
          { campaingId: undefined }
        ]
      },
      select: {
        id: true,
        matchId: true,
        leadId: true,
        messageId: true,
        subject: true,
        type: true,
        toEmailID: true,
        fromEmailID: true,
        time: true,
        campaingId: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📧 Found ${emailsWithoutCampaignId.length} emails without campaignId`);
    
    // if (emailsWithoutCampaignId.length > 0) {
    //   console.log("\n📋 All emails without campaignId:");
    //   emailsWithoutCampaignId.forEach((email, index) => {
    //     console.log(`${index + 1}. ID: ${email.id}, LeadID: ${email.leadId}, Subject: "${email.subject}", Time: ${email.time}, Type: ${email.type}`);
    //   });
    // }

    return emailsWithoutCampaignId;
  } catch (error) {
    console.error("❌ Error finding emails without campaignId:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
printEmailsWithoutCampaignId(); 