const fs = require("fs");
const path = require("path");
const csvtojson = require("csvtojson");
const prisma = require("../database/prisma/getPrismaClient");

/**
 * CSV Queue Manager - Handles CSV file uploads and queue management
 */
class CsvQueueManager {
  constructor() {
    this.maxRetries = 3;
  }

  /**
   * Add CSV file to processing queue
   * @param {Object} fileInfo - File information
   * @param {string} fileInfo.filePath - Path to the uploaded file
   * @param {string} fileInfo.originalName - Original filename
   * @param {string} processType - Type of processing (prospect, amazon_seller, etc.)
   * @param {string} uploadedBy - User who uploaded the file
   * @param {string} source - Source of the upload
   * @param {Object} options - Processing options (skipErrorRows, allowBlankRows, operation, etc.)
   * @returns {Object} Created CSV data record
   */
  async addToQueue(fileInfo, processType, uploadedBy = null, source = null, options = {}) {
    try {
      // Get file stats
      const stats = fs.statSync(fileInfo.filePath);
      const fileSize = stats.size;

      // Read CSV headers and count rows
      const jsonArray = await csvtojson().fromFile(fileInfo.filePath);
      const headers = jsonArray.length > 0 ? Object.keys(jsonArray[0]) : [];
      const totalRows = jsonArray.length;

      // Create CSV data record
      const csvData = await prisma.csvData.create({
        data: {
          file_name: fileInfo.originalName,
          file_path: fileInfo.filePath,
          file_size: fileSize,
          file_type: path.extname(fileInfo.originalName).toLowerCase(),
          process_type: processType,
          original_headers: headers,
          total_rows: totalRows,
          options: options,
          header_mappings: {},
          status: "PENDING",
          source: source,
          uploaded_by: uploadedBy,
          max_retries: this.maxRetries,
        },
      });

      // Create CSV data log entries for each row
      await this.createRowEntries(csvData.id, jsonArray, headers);

      console.log(`Added CSV to queue: ${fileInfo.originalName} (ID: ${csvData.id})`);
      return csvData;
    } catch (error) {
      console.error("Error adding CSV to queue:", error);
      throw error;
    }
  }

  /**
   * Create CSV data log entries for each row
   * @param {number} csvDataId - CSV data record ID
   * @param {Array} jsonArray - Parsed CSV data
   * @param {Array} headers - CSV headers
   */
  async createRowEntries(csvDataId, jsonArray, headers) {
    const batchSize = 100;
    const totalRows = jsonArray.length;

    for (let i = 0; i < totalRows; i += batchSize) {
      const batch = jsonArray.slice(i, i + batchSize);
      const rowEntries = batch.map((row, index) => {
        const rowNumber = i + index + 1;
        const rowData = this.mapRowToKeyColumns(row, headers);

        return {
          data_id: csvDataId,
          row_number: rowNumber,
          headers_map: headers.reduce((acc, header, idx) => {
            acc[`key${idx + 1}`] = header;
            return acc;
          }, {}),
          ...rowData,
        };
      });

      await prisma.csvDataLog.createMany({
        data: rowEntries,
      });
    }

    console.log(`Created ${totalRows} row entries for CSV data ID: ${csvDataId}`);
  }

  /**
   * Map CSV row data to key columns (key1-key50)
   * @param {Object} row - CSV row data
   * @param {Array} headers - CSV headers
   * @returns {Object} Mapped data for key columns
   */
  mapRowToKeyColumns(row, headers) {
    const mappedData = {};
    
    headers.forEach((header, index) => {
      if (index < 50) { // Only map up to key50
        const keyColumn = `key${index + 1}`;
        mappedData[keyColumn] = row[header] || "";
      }
    });

    return mappedData;
  }

  /**
   * Get next pending CSV from queue
   * @returns {Object|null} Next CSV to process or null if none available
   */
  async getNextPending() {
    try {
      const csvData = await prisma.csvData.findFirst({
        where: {
          status: "PENDING",
          retry_count: {
            lt: prisma.csvData.fields.max_retries,
          },
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      return csvData;
    } catch (error) {
      console.error("Error getting next pending CSV:", error);
      throw error;
    }
  }

  /**
   * Update CSV processing status
   * @param {number} csvDataId - CSV data ID
   * @param {string} status - New status
   * @param {string} errorMessage - Error message if failed
   */
  async updateStatus(csvDataId, status, errorMessage = null) {
    try {
      const updateData = {
        status: status,
        updatedAt: new Date(),
      };

      if (status === "COMPLETED") {
        updateData.is_completed = true;
        updateData.processed_until = await this.getTotalRows(csvDataId);
      }

      if (status === "FAILED") {
        updateData.error_status = true;
        updateData.error_message = errorMessage;
        updateData.retry_count = {
          increment: 1,
        };
        updateData.last_retry_at = new Date();
      }

      if (status === "PROCESSING") {
        updateData.error_status = false;
        updateData.error_message = null;
      }

      await prisma.csvData.update({
        where: { id: csvDataId },
        data: updateData,
      });

      console.log(`Updated CSV ${csvDataId} status to: ${status}`);
    } catch (error) {
      console.error("Error updating CSV status:", error);
      throw error;
    }
  }

  /**
   * Update processing progress
   * @param {number} csvDataId - CSV data ID
   * @param {number} processedCount - Number of rows processed
   */
  async updateProgress(csvDataId, processedCount) {
    try {
      await prisma.csvData.update({
        where: { id: csvDataId },
        data: {
          processed_until: processedCount,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error("Error updating CSV progress:", error);
      throw error;
    }
  }

  /**
   * Mark a specific row as processed
   * @param {number} csvDataId - CSV data ID
   * @param {number} rowNumber - Row number
   * @param {boolean} success - Whether processing was successful
   * @param {string} errorMessage - Error message if failed
   */
  async markRowProcessed(csvDataId, rowNumber, success = true, errorMessage = null) {
    try {
      await prisma.csvDataLog.updateMany({
        where: {
          data_id: csvDataId,
          row_number: rowNumber,
        },
        data: {
          processed: success,
          error_status: !success,
          error_message: errorMessage,
          retry_count: success ? 0 : { increment: 1 },
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error("Error marking row as processed:", error);
      throw error;
    }
  }

  /**
   * Get unprocessed rows for a CSV
   * @param {number} csvDataId - CSV data ID
   * @param {number} limit - Number of rows to fetch
   * @returns {Array} Unprocessed rows
   */
  async getUnprocessedRows(csvDataId, limit = 10) {
    try {
      const rows = await prisma.csvDataLog.findMany({
        where: {
          data_id: csvDataId,
          processed: false,
          retry_count: {
            lt: 3, // Max retries per row
          },
        },
        take: limit,
        orderBy: {
          row_number: "asc",
        },
      });

      return rows;
    } catch (error) {
      console.error("Error getting unprocessed rows:", error);
      throw error;
    }
  }

  /**
   * Get total rows count for a CSV
   * @param {number} csvDataId - CSV data ID
   * @returns {number} Total rows count
   */
  async getTotalRows(csvDataId) {
    try {
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
        select: { total_rows: true },
      });

      return csvData?.total_rows || 0;
    } catch (error) {
      console.error("Error getting total rows:", error);
      throw error;
    }
  }

  /**
   * Get processing statistics for a CSV using optimized database queries
   * @param {number} csvDataId - CSV data ID
   * @returns {Object} Processing statistics
   */
  async getProcessingStats(csvDataId) {
    try {
      // Get CSV data and statistics in parallel
      const [csvData, stats] = await Promise.all([
        prisma.csvData.findUnique({
          where: { id: csvDataId },
          select: {
            id: true,
            total_rows: true,
            status: true,
            is_completed: true,
            error_status: true,
            error_message: true,
            processed_until: true,
            retry_count: true,
            max_retries: true,
            last_retry_at: true,
            createdAt: true,
            updatedAt: true,
          },
        }),
        this.getSingleCsvStatistics(csvDataId)
      ]);

      if (!csvData) {
        return null;
      }

      const totalRows = csvData.total_rows;
      const processedRows = stats.processedRows;
      const errorRows = stats.errorRows;
      const pendingRows = totalRows - processedRows;

      return {
        totalRows,
        processedRows,
        errorRows,
        pendingRows,
        progressPercentage: totalRows > 0 ? (processedRows / totalRows) * 100 : 0,
        successRate: totalRows > 0 ? ((totalRows - errorRows) / totalRows) * 100 : 0,
        status: csvData.status,
        isCompleted: csvData.is_completed,
        errorStatus: csvData.error_status,
        errorMessage: csvData.error_message,
        processedUntil: csvData.processed_until,
        retryCount: csvData.retry_count,
        maxRetries: csvData.max_retries,
        lastRetryAt: csvData.last_retry_at,
        createdAt: csvData.createdAt,
        updatedAt: csvData.updatedAt,
      };
    } catch (error) {
      console.error("Error getting processing stats:", error);
      throw error;
    }
  }

  /**
   * Get statistics for a single CSV file using database aggregates
   * @param {number} csvDataId - CSV data ID
   * @returns {Object} Statistics object
   */
  async getSingleCsvStatistics(csvDataId) {
    try {
      const stats = await prisma.$queryRaw`
        SELECT 
          COUNT(*) FILTER (WHERE processed = true) as processed_rows,
          COUNT(*) FILTER (WHERE error_status = true) as error_rows,
          COUNT(*) FILTER (WHERE processed = false AND error_status = false) as pending_rows
        FROM "CsvDataLog"
        WHERE data_id = ${csvDataId}
      `;

      const result = stats[0];
      return {
        processedRows: parseInt(result.processed_rows) || 0,
        errorRows: parseInt(result.error_rows) || 0,
        pendingRows: parseInt(result.pending_rows) || 0,
      };
    } catch (error) {
      console.error("Error getting single CSV statistics:", error);
      return {
        processedRows: 0,
        errorRows: 0,
        pendingRows: 0,
      };
    }
  }

  /**
   * Reconstruct original CSV row from key columns
   * @param {Object} csvDataLog - CSV data log entry
   * @returns {Object} Original CSV row data
   */
  reconstructRowData(csvDataLog) {
    const rowData = {};
    const headersMap = csvDataLog.headers_map || {};

    // Map key columns back to original headers
    Object.entries(headersMap).forEach(([keyColumn, header]) => {
      const value = csvDataLog[keyColumn];
      if (value !== null && value !== undefined) {
        rowData[header] = value;
      }
    });

    return rowData;
  }

  /**
   * Get all CSV queue entries with pagination and enhanced statistics
   * @param {Object} options - Query options
   * @returns {Object} Paginated results with error information
   */
  async getQueueList(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        status = null,
        processType = null,
      } = options;

      const skip = (page - 1) * limit;
      const where = {};

      if (status) {
        where.status = status;
      }

      if (processType) {
        where.process_type = processType;
      }

      // Get CSV data with optimized query
      const [csvData, total] = await Promise.all([
        prisma.csvData.findMany({
          where,
          select: {
            id: true,
            file_name: true,
            file_path: true,
            file_size: true,
            file_type: true,
            process_type: true,
            original_headers: true,
            total_rows: true,
            options: true,
            header_mappings: true,
            status: true,
            processed_until: true,
            is_completed: true,
            error_status: true,
            error_message: true,
            retry_count: true,
            max_retries: true,
            last_retry_at: true,
            source: true,
            uploaded_by: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          skip,
          take: limit,
        }),
        prisma.csvData.count({ where }),
      ]);

      // Get aggregated statistics for all CSV files in one query
      const csvIds = csvData.map(csv => csv.id);
      const statistics = await this.getBulkStatistics(csvIds);

      // Get unique errors for all CSV files in one query
      const uniqueErrors = await this.getBulkUniqueErrors(csvIds);

      // Enhance each CSV data record with statistics and unique errors
      const enhancedData = csvData.map((csv) => {
        const csvStats = statistics.find(stat => stat.data_id === csv.id) || {
          processedRows: 0,
          errorRows: 0,
          uniqueErrorCount: 0
        };
        
        const csvErrors = uniqueErrors.filter(error => error.data_id === csv.id);
        
        const totalRows = csv.total_rows;
        const processedRows = csvStats.processedRows;
        const errorRows = csvStats.errorRows;
        const pendingRows = totalRows - processedRows;
        
        return {
          ...csv,
          statistics: {
            totalRows,
            processedRows,
            errorRows,
            pendingRows,
            progressPercentage: totalRows > 0 ? (processedRows / totalRows) * 100 : 0,
            successRate: totalRows > 0 ? ((totalRows - errorRows) / totalRows) * 100 : 0,
          },
          uniqueErrors: csvErrors.map(error => error.error_message).filter(Boolean),
          errorCount: csvStats.uniqueErrorCount,
        };
      });

      return {
        data: enhancedData,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
        summary: {
          totalFiles: total,
          totalRows: enhancedData.reduce((sum, csv) => sum + csv.total_rows, 0),
          totalProcessedRows: enhancedData.reduce((sum, csv) => sum + csv.statistics.processedRows, 0),
          totalErrorRows: enhancedData.reduce((sum, csv) => sum + csv.statistics.errorRows, 0),
          totalUniqueErrors: enhancedData.reduce((sum, csv) => sum + csv.errorCount, 0),
        },
      };
    } catch (error) {
      console.error("Error getting queue list:", error);
      throw error;
    }
  }

  /**
   * Get bulk statistics for multiple CSV files using database aggregates
   * @param {Array} csvIds - Array of CSV data IDs
   * @returns {Array} Array of statistics objects
   */
  async getBulkStatistics(csvIds) {
    if (csvIds.length === 0) return [];

    try {
      const stats = await prisma.$queryRaw`
        SELECT 
          data_id,
          COUNT(*) FILTER (WHERE processed = true) as processed_rows,
          COUNT(*) FILTER (WHERE error_status = true) as error_rows
        FROM "CsvDataLog"
        WHERE data_id = ANY(${csvIds})
        GROUP BY data_id
      `;

      return stats.map(stat => ({
        data_id: parseInt(stat.data_id),
        processedRows: parseInt(stat.processed_rows),
        errorRows: parseInt(stat.error_rows)
      }));
    } catch (error) {
      console.error("Error getting bulk statistics:", error);
      return [];
    }
  }

  /**
   * Get bulk unique errors for multiple CSV files
   * @param {Array} csvIds - Array of CSV data IDs
   * @returns {Array} Array of unique error objects
   */
  async getBulkUniqueErrors(csvIds) {
    if (csvIds.length === 0) return [];

    try {
      const errors = await prisma.csvDataLog.findMany({
        where: {
          data_id: { in: csvIds },
          error_status: true,
          error_message: { not: null }
        },
        select: {
          data_id: true,
          error_message: true
        },
        distinct: ['data_id', 'error_message']
      });

      // Count unique errors per CSV
      const errorCounts = {};
      errors.forEach(error => {
        if (!errorCounts[error.data_id]) {
          errorCounts[error.data_id] = 0;
        }
        errorCounts[error.data_id]++;
      });

      return errors.map(error => ({
        ...error,
        data_id: parseInt(error.data_id),
        uniqueErrorCount: errorCounts[error.data_id] || 0
      }));
    } catch (error) {
      console.error("Error getting bulk unique errors:", error);
      return [];
    }
  }

  /**
   * Retry failed rows for a CSV file
   * @param {number} csvDataId - CSV data ID
   * @param {number} maxRetries - Maximum number of retries
   * @returns {Object} Retry operation result
   */
  async retryFailedRows(csvDataId, maxRetries = 3) {
    try {
      // Check if CSV exists
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
        select: { id: true, file_name: true, status: true, max_retries: true }
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      // Get failed rows that haven't exceeded max retries
      const failedRows = await prisma.csvDataLog.findMany({
        where: {
          data_id: csvDataId,
          error_status: true,
          retry_count: { lt: maxRetries }
        },
        select: {
          id: true,
          row_number: true,
          retry_count: true,
          error_message: true
        },
        orderBy: { row_number: 'asc' }
      });

      if (failedRows.length === 0) {
        return {
          success: true,
          message: "No failed rows to retry",
          retriedRows: 0,
          totalFailedRows: 0
        };
      }

      // Reset failed rows for retry
      const rowIds = failedRows.map(row => row.id);
      await prisma.csvDataLog.updateMany({
        where: { id: { in: rowIds } },
        data: {
          processed: false,
          error_status: false,
          error_message: null,
          retry_count: { increment: 1 },
          updatedAt: new Date()
        }
      });

      // Update CSV status to PENDING if it was FAILED
      if (csvData.status === 'FAILED') {
        await prisma.csvData.update({
          where: { id: csvDataId },
          data: {
            status: 'PENDING',
            error_status: false,
            error_message: null,
            updatedAt: new Date()
          }
        });
      }

      console.log(`Reset ${failedRows.length} failed rows for retry in CSV ${csvData.file_name} (ID: ${csvDataId})`);

      return {
        success: true,
        message: `Reset ${failedRows.length} failed rows for retry`,
        retriedRows: failedRows.length,
        totalFailedRows: failedRows.length,
        csvInfo: {
          id: csvData.id,
          fileName: csvData.file_name,
          status: csvData.status === 'FAILED' ? 'PENDING' : csvData.status
        }
      };
    } catch (error) {
      console.error("Error retrying failed rows:", error);
      throw error;
    }
  }

  /**
   * Pause CSV processing
   * @param {number} csvDataId - CSV data ID
   */
  async pauseProcessing(csvDataId) {
    try {
      await this.updateStatus(csvDataId, "PAUSED");
    } catch (error) {
      console.error("Error pausing CSV processing:", error);
      throw error;
    }
  }

  /**
   * Resume CSV processing
   * @param {number} csvDataId - CSV data ID
   */
  async resumeProcessing(csvDataId) {
    try {
      await this.updateStatus(csvDataId, "PENDING");
    } catch (error) {
      console.error("Error resuming CSV processing:", error);
      throw error;
    }
  }

  /**
   * Get unique errors for a CSV file
   * @param {number} csvDataId - CSV data ID
   * @returns {Array} Array of unique error messages
   */
  async getUniqueErrors(csvDataId) {
    try {
      const errors = await prisma.csvDataLog.findMany({
        where: {
          data_id: csvDataId,
          error_status: true,
          error_message: {
            not: null
          }
        },
        select: {
          error_message: true
        },
        distinct: ['error_message']
      });

      return errors.map(error => error.error_message).filter(Boolean);
    } catch (error) {
      console.error("Error getting unique errors:", error);
      throw error;
    }
  }

  /**
   * Get paginated processed data for a CSV file
   * @param {number} csvDataId - CSV data ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Object} Paginated processed data
   */
  async getProcessedData(csvDataId, page = 1, limit = 100) {
    try {
      const skip = (page - 1) * limit;

      // Get CSV data
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      // Get processed rows with pagination
      const [processedRows, totalProcessedRows] = await Promise.all([
        prisma.csvDataLog.findMany({
          where: {
            data_id: csvDataId,
            processed: true,
            error_status: false,
          },
          select: {
            row_number: true,
            key1: true,
            key2: true,
            key3: true,
            key4: true,
            key5: true,
            key6: true,
            key7: true,
            key8: true,
            key9: true,
            key10: true,
            headers_map: true,
          },
          orderBy: {
            row_number: "asc",
          },
          skip,
          take: limit,
        }),
        prisma.csvDataLog.count({
          where: {
            data_id: csvDataId,
            processed: true,
            error_status: false,
          },
        }),
      ]);

      // Transform the data
      const transformedData = processedRows.map(row => {
        const originalData = {};
        const headersMap = row.headers_map || {};
        
        Object.entries(headersMap).forEach(([keyColumn, header]) => {
          const value = row[keyColumn];
          if (value !== null && value !== undefined) {
            originalData[header] = value;
          }
        });

        return {
          rowNumber: row.row_number,
          data: originalData,
        };
      });

      // Transform to match frontend expectations
      const csvDataArray = transformedData.map(row => ({
        rowNumber: row.rowNumber,
        processed: true,
        errorStatus: false,
        errorMessage: null,
        ...row.data, // Spread the data as direct properties
      }));

      // Extract headers from the first row if available
      const headers = csvDataArray.length > 0 ? Object.keys(csvDataArray[0]).filter(key => 
        key !== 'rowNumber' && key !== 'processed' && key !== 'errorStatus' && key !== 'errorMessage'
      ) : [];

      return {
        success: true,
        data: csvDataArray, // Frontend expects data.data
        headers: headers, // Frontend expects data.headers
        total: totalProcessedRows, // Frontend expects data.total
        csvInfo: {
          id: csvData.id,
          fileName: csvData.file_name,
          processType: csvData.process_type,
          status: csvData.status,
          totalRows: csvData.total_rows,
        },
        processedData: transformedData, // Keep the detailed structure
        pagination: {
          total: totalProcessedRows,
          page,
          limit,
          totalPages: Math.ceil(totalProcessedRows / limit),
        },
      };
    } catch (error) {
      console.error("Error getting processed data:", error);
      throw error;
    }
  }

  /**
   * Get paginated error rows for a CSV file
   * @param {number} csvDataId - CSV data ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Object} Paginated error rows with statistics
   */
  async getErrorRows(csvDataId, page = 1, limit = 100) {
    try {
      const skip = (page - 1) * limit;

      // Get CSV data
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      // Get error rows with pagination
      const [errorRows, totalErrorRows] = await Promise.all([
        prisma.csvDataLog.findMany({
          where: {
            data_id: csvDataId,
            error_status: true,
          },
          select: {
            row_number: true,
            error_message: true,
            key1: true,
            key2: true,
            key3: true,
            key4: true,
            key5: true,
            key6: true,
            key7: true,
            key8: true,
            key9: true,
            key10: true,
            // Add more key columns as needed
          },
          orderBy: {
            row_number: "asc",
          },
          skip,
          take: limit,
        }),
        prisma.csvDataLog.count({
          where: {
            data_id: csvDataId,
            error_status: true,
          },
        }),
      ]);

      // Get unique errors
      const uniqueErrors = await this.getUniqueErrors(csvDataId);

      // Group errors by message for the current page
      const errorGroups = {};
      errorRows.forEach((row) => {
        const errorMsg = row.error_message || "Unknown error";
        if (!errorGroups[errorMsg]) {
          errorGroups[errorMsg] = [];
        }
        
        // Reconstruct original row data
        const originalData = {};
        for (let i = 1; i <= 10; i++) {
          const key = `key${i}`;
          if (row[key] !== null && row[key] !== undefined) {
            originalData[key] = row[key];
          }
        }
        
        errorGroups[errorMsg].push({
          rowNumber: row.row_number,
          data: originalData,
        });
      });

      // Transform errorRows to match frontend expectations
      const csvDataArray = errorRows.map(row => {
        const originalData = {};
        for (let i = 1; i <= 10; i++) {
          const key = `key${i}`;
          if (row[key] !== null && row[key] !== undefined) {
            originalData[key] = row[key];
          }
        }
        
        return {
          rowNumber: row.row_number,
          processed: false,
          errorStatus: true,
          errorMessage: row.error_message,
          ...originalData, // Spread the original data as direct properties
        };
      });

      return {
        success: true,
        errors: csvDataArray, // Frontend expects errors.errors
        csvInfo: {
          id: csvData.id,
          fileName: csvData.file_name,
          processType: csvData.process_type,
          status: csvData.status,
          totalRows: csvData.total_rows,
        },
        errorSummary: {
          totalErrorRows,
          uniqueErrorTypes: uniqueErrors.length,
          uniqueErrors,
        },
        errorGroups,
        errorRows, // Keep the detailed structure
        pagination: {
          total: totalErrorRows,
          page,
          limit,
          totalPages: Math.ceil(totalErrorRows / limit),
        },
      };
    } catch (error) {
      console.error("Error getting error rows:", error);
      throw error;
    }
  }

  /**
   * Mark all rows as processed for a CSV
   * @param {number} csvDataId - CSV data ID
   * @param {boolean} success - Whether processing was successful
   */
  async markAllRowsProcessed(csvDataId, success = true) {
    try {
      await prisma.csvDataLog.updateMany({
        where: { data_id: csvDataId },
        data: {
          processed: true,
          error_status: !success,
        },
      });

      // Update the main CSV data record
      await prisma.csvData.update({
        where: { id: csvDataId },
        data: {
          processed_until: await prisma.csvDataLog.count({
            where: { data_id: csvDataId },
          }),
          is_completed: true,
        },
      });

      console.log(`Marked all rows as processed for CSV ID: ${csvDataId}`);
    } catch (error) {
      console.error("Error marking all rows as processed:", error);
      throw error;
    }
  }

  /**
   * Get paginated table data for a CSV with transformed data and errors
   * @param {number} csvDataId - CSV data ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {string} search - Search term
   * @param {string} type - Type of data to show (all, processed, errors)
   * @returns {Object} Paginated table data
   */
  async getTableData(csvDataId, page = 1, limit = 10, search = "", type = "all") {
    try {
      const skip = (page - 1) * limit;

      // Get CSV data
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      // Build where clause based on type filter
      let where = { data_id: csvDataId };
      
      if (type === "processed") {
        where.processed = true;
        where.error_status = false;
      } else if (type === "errors") {
        where.error_status = true;
      }
      // For "all" type, no additional filters

      // Add search filter if provided
      if (search) {
        where.OR = [
          { key1: { contains: search, mode: 'insensitive' } },
          { key2: { contains: search, mode: 'insensitive' } },
          { key3: { contains: search, mode: 'insensitive' } },
          { key4: { contains: search, mode: 'insensitive' } },
          { key5: { contains: search, mode: 'insensitive' } },
          { key6: { contains: search, mode: 'insensitive' } },
          { key7: { contains: search, mode: 'insensitive' } },
          { key8: { contains: search, mode: 'insensitive' } },
          { key9: { contains: search, mode: 'insensitive' } },
          { key10: { contains: search, mode: 'insensitive' } },
          { error_message: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Get paginated data
      const [rows, totalRows] = await Promise.all([
        prisma.csvDataLog.findMany({
          where,
          select: {
            id: true,
            row_number: true,
            processed: true,
            error_status: true,
            error_message: true,
            key1: true,
            key2: true,
            key3: true,
            key4: true,
            key5: true,
            key6: true,
            key7: true,
            key8: true,
            key9: true,
            key10: true,
            headers_map: true,
          },
          orderBy: {
            row_number: "asc",
          },
          skip,
          take: limit,
        }),
        prisma.csvDataLog.count({ where }),
      ]);

      // Transform the data to include original and transformed data
      const tableData = rows.map(row => {
        // Reconstruct original data from key columns
        const originalData = {};
        const headersMap = row.headers_map || {};
        
        Object.entries(headersMap).forEach(([keyColumn, header]) => {
          const value = row[keyColumn];
          if (value !== null && value !== undefined) {
            originalData[header] = value;
          }
        });

        // For now, transformed data is the same as original data
        // In the future, this could be enhanced to show the actual transformed data
        const transformedData = { ...originalData };

        return {
          rowNumber: row.row_number,
          processed: row.processed,
          errorStatus: row.error_status,
          errorMessage: row.error_message,
          originalData,
          transformedData,
        };
      });

      // Get summary statistics
      const [processedCount, errorCount] = await Promise.all([
        prisma.csvDataLog.count({
          where: { 
            data_id: csvDataId,
            processed: true,
            error_status: false
          }
        }),
        prisma.csvDataLog.count({
          where: { 
            data_id: csvDataId,
            error_status: true
          }
        })
      ]);

      // Transform tableData to match frontend expectations
      const csvDataArray = tableData.map(row => ({
        rowNumber: row.rowNumber,
        processed: row.processed,
        errorStatus: row.errorStatus,
        errorMessage: row.errorMessage,
        ...row.originalData, // Spread the original data as direct properties
      }));

      // Extract headers from the first row if available
      const headers = csvDataArray.length > 0 ? Object.keys(csvDataArray[0]).filter(key => 
        key !== 'rowNumber' && key !== 'processed' && key !== 'errorStatus' && key !== 'errorMessage'
      ) : [];

      return {
        success: true,
        data: csvDataArray, // Frontend expects data.data
        headers: headers, // Frontend expects data.headers
        total: totalRows, // Frontend expects data.total
        csvInfo: {
          id: csvData.id,
          fileName: csvData.file_name,
          processType: csvData.process_type,
          status: csvData.status,
          totalRows: csvData.total_rows,
        },
        tableData, // Keep the detailed structure for advanced use
        pagination: {
          total: totalRows,
          page,
          limit,
          totalPages: Math.ceil(totalRows / limit),
        },
        summary: {
          totalRows: csvData.total_rows,
          processedRows: processedCount,
          errorRows: errorCount,
          processedCount,
          errorCount,
        },
      };
    } catch (error) {
      console.error("Error getting table data:", error);
      throw error;
    }
  }

  /**
   * Export data for a CSV file
   * @param {number} csvDataId - CSV data ID
   * @param {string} format - Export format (csv, json)
   * @param {string} type - Type of data to export (processed, errors)
   * @returns {string|Object} Exported data
   */
  async exportData(csvDataId, format = 'csv', type = 'processed') {
    try {
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      let data;
      if (type === 'errors') {
        // Export error rows
        data = await prisma.csvDataLog.findMany({
          where: {
            data_id: csvDataId,
            error_status: true,
          },
          select: {
            row_number: true,
            error_message: true,
            key1: true,
            key2: true,
            key3: true,
            key4: true,
            key5: true,
            key6: true,
            key7: true,
            key8: true,
            key9: true,
            key10: true,
          },
          orderBy: {
            row_number: "asc",
          },
        });
      } else {
        // Export processed rows
        data = await prisma.csvDataLog.findMany({
          where: {
            data_id: csvDataId,
            processed: true,
          },
          select: {
            row_number: true,
            key1: true,
            key2: true,
            key3: true,
            key4: true,
            key5: true,
            key6: true,
            key7: true,
            key8: true,
            key9: true,
            key10: true,
          },
          orderBy: {
            row_number: "asc",
          },
        });
      }

      if (format === 'json') {
        return data;
      } else {
        // Convert to CSV
        const converter = require('json-2-csv');
        const csvString = await converter.json2csv(data);
        return csvString;
      }
    } catch (error) {
      console.error("Error exporting data:", error);
      throw error;
    }
  }

  /**
   * Delete CSV from queue and cleanup files
   * @param {number} csvDataId - CSV data ID
   */
  async deleteFromQueue(csvDataId) {
    try {
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      // First, delete all CSV data logs for this CSV
      const deletedLogsCount = await prisma.csvDataLog.deleteMany({
        where: { data_id: csvDataId },
      });

      console.log(`Deleted ${deletedLogsCount.count} data log rows for CSV ID: ${csvDataId}`);

      // Delete file if it exists
      if (fs.existsSync(csvData.file_path)) {
        fs.unlinkSync(csvData.file_path);
        console.log(`Deleted file: ${csvData.file_path}`);
      }

      // Finally, delete the CSV data record
      await prisma.csvData.delete({
        where: { id: csvDataId },
      });

      console.log(`Deleted CSV from queue: ${csvData.file_name} (ID: ${csvDataId})`);
      
      return {
        success: true,
        message: `Successfully deleted CSV and ${deletedLogsCount.count} data log rows`,
        deletedLogsCount: deletedLogsCount.count,
        csvInfo: {
          id: csvData.id,
          fileName: csvData.file_name,
          totalRows: csvData.total_rows
        }
      };
    } catch (error) {
      console.error("Error deleting CSV from queue:", error);
      throw error;
    }
  }

  /**
   * Reset CSV data and data logs for reprocessing
   * @param {number} csvDataId - CSV data ID
   * @returns {Object} Reset operation result
   */
  async resetCsvForReprocessing(csvDataId) {
    try {
      // Check if CSV exists
      const csvData = await prisma.csvData.findUnique({
        where: { id: csvDataId },
        select: { 
          id: true, 
          file_name: true, 
          status: true, 
          total_rows: true,
          processed_until: true,
          is_completed: true,
          error_status: true,
          error_message: true,
          retry_count: true,
          max_retries: true
        }
      });

      if (!csvData) {
        throw new Error("CSV data not found");
      }

      // Reset all data logs for this CSV
      const resetLogsResult = await prisma.csvDataLog.updateMany({
        where: { data_id: csvDataId },
        data: {
          processed: false,
          error_status: false,
          error_message: null,
          retry_count: 0,
          updatedAt: new Date()
        }
      });

      // Reset CSV data record
      const resetCsvResult = await prisma.csvData.update({
        where: { id: csvDataId },
        data: {
          status: 'PENDING',
          processed_until: 0,
          is_completed: false,
          error_status: false,
          error_message: null,
          retry_count: 0,
          last_retry_at: null,
          updatedAt: new Date()
        }
      });

      console.log(`Reset CSV ${csvData.file_name} (ID: ${csvDataId}) for reprocessing`);
      console.log(`- Reset ${resetLogsResult.count} data log rows`);
      console.log(`- CSV status reset to PENDING`);

      return {
        success: true,
        message: `Successfully reset CSV for reprocessing`,
        data: {
          csvId: csvDataId,
          fileName: csvData.file_name,
          resetLogsCount: resetLogsResult.count,
          previousStatus: csvData.status,
          newStatus: 'PENDING',
          totalRows: csvData.total_rows
        }
      };
    } catch (error) {
      console.error("Error resetting CSV for reprocessing:", error);
      throw error;
    }
  }
}

module.exports = new CsvQueueManager();
