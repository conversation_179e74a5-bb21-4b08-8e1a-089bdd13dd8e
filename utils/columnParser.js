function convertEstimateSales(salesString) {
  // Remove dollar signs, commas, and spaces
  let cleanedString = salesString.replace(/[$,\s]/g, "");

  // Check if the string contains 'M' for millions
  if (cleanedString.includes("M")) {
    // Remove 'M' and convert to float, then multiply by 1,000,000
    cleanedString = cleanedString.replace("M", "");
    return parseFloat(cleanedString) * 1_000_000;
  }
  // Check if the string contains 'K' for thousands
  if (cleanedString.includes("K")) {
    // Remove 'K' and convert to float, then multiply by 1,000
    cleanedString = cleanedString.replace("K", "");
    return parseFloat(cleanedString) * 1_000;
  }

  // Convert the cleaned string to a float
  return parseFloat(cleanedString);
}

// console.log(convertEstimateSales("$10,000.50")); // 10000.00
// console.log(convertEstimateSales("10,000.00")); // 10000.00
// console.log(convertEstimateSales("$1.5M"));
// console.log(convertEstimateSales("$10,159")); // 10159.00
module.exports = { convertEstimateSales };
