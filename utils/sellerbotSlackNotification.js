require("dotenv").config();
const axios = require("axios");

async function sellerbotxSlackNotification(job, metrics = null, sheetUrl = null, success = true, errorMessage = null) {
  const webhookUrl = process.env.SELLERBOT_NOTI_SLACKURL;
  const TAGGED_USERS = ["<@U076TFCNDTK>", "<@U08P41YT35Y>"];

  if (!webhookUrl) {
    console.warn("Slack webhook URL not set");
    return;
  }

  const statusEmoji = success ? ":white_check_mark:" : ":x:";
  
  // Start building the message
  let messageText = `${statusEmoji} *Lead Generation Job ${success ? 'Completed' : 'Failed'}* ${TAGGED_USERS?.join(" ")}\n\n`;

  // Add job details
  messageText += `*Job Details*\n`;
  messageText += `• Job Name: ${job.name || 'Not specified'}\n`;
  messageText += `• Job ID: ${job.id}\n`;
  messageText += `• Status: ${success ? 'Completed Successfully' : 'Failed'}\n`;

  // Add metrics if available
  if (metrics && metrics.totalRecords && metrics.totalRecords > 0 && success) {
    messageText += `\n*Lead Processing Results*\n`;
    messageText += `• Total Records: ${metrics.totalRecords || 0}\n`;
    messageText += `• Matched Records: ${metrics.matchedRecords || 0}\n`;
    messageText += `• Completion: ${metrics.completionPercentage || 0}%\n`;

     // Add URL-related metrics
     if (metrics.totalUrls) messageText += `• Total URLs: ${metrics.totalUrls}\n`;
     if (metrics.validatedUrls) messageText += `• Validated URLs: ${metrics.validatedUrls}\n`;
     if (metrics.confidenceFilteredUrls) messageText += `• Confidence-Filtered URLs: ${metrics.confidenceFilteredUrls}\n`;
     
     if (metrics.totalRecords && metrics.totalRecords > 0) {
      // Add status breakdown header
      messageText += `\n*Status Breakdown*\n`;
      // Add status breakdown
      if (metrics.completed) messageText += `• Completed: ${metrics.completed}\n`;
      if (metrics.srp_failed) messageText += `• SRP Failed: ${metrics.srp_failed}\n`;
      if (metrics.pending) messageText += `• Pending: ${metrics.pending}\n`;
      if (metrics.processing) messageText += `• Processing: ${metrics.processing}\n`;
      if (metrics.srp_requested) messageText += `• SRP Requested: ${metrics.srp_requested}\n`;
      if (metrics.srp_scraped) messageText += `• SRP Scraped: ${metrics.srp_scraped}\n`;
      if (metrics.scoring) messageText += `• Scoring: ${metrics.scoring}\n`;
      if (metrics.validating) messageText += `• Validating: ${metrics.validating}\n`;
     }
  }

  // Add Google Sheet link if available
  if (sheetUrl) {
    messageText += `\n*Export Results*\n`;
    messageText += `• <${sheetUrl}|View Google Sheet Export>\n`;
  }

  // Add error message if available
  if (errorMessage) {
    messageText += `\n*Error Details*\n`;
    messageText += `\`\`\`${errorMessage}\`\`\`\n`;
  }

  const message = { text: messageText };

  // Send the message to Slack
  try {
    await axios.post(webhookUrl, message);
    console.log(`Slack notification sent for job ${job.id}`);
    return true;
  } catch (error) {
    console.error('Error sending Slack notification:', error);
    return false;
  }
}

module.exports = { sellerbotxSlackNotification };
