const prisma = require("../database/prisma/getPrismaClient");
const { convertEstimateSales } = require("./columnParser");

async function updateProspectSources() {
  try {
    // Fetch all prospects from the database
    const prospects = await prisma.prospect.findMany();

    let count = 1;
    let totalCount = prospects.length;
    console.log(`Total prospects: ${totalCount}`);
    // Iterate over each prospect and update the sources field
    for (const prospect of prospects) {
      console.log(`Updating prospect sources: ${count++} / ${totalCount}`);
      // Parse the sources field if it's not an array
      prospect.sources = prospect.source ? [prospect.source] : [];

      // Update the prospect record with the new sources array
      await prisma.prospect.update({
        where: { prospect_id: prospect.prospect_id },
        data: prospect,
      });
    }

    console.log("Prospect sources updated successfully.");
  } catch (error) {
    console.error("Error updating prospect sources:", error);
  } finally {
    await prisma.$disconnect();
  }
}

async function updateAmazonSellerIds() {
  try {
    // Fetch all companies from the database
    const matchings = await prisma.clientsProspectsMatching.findMany();

    let count = 1;
    let totalCount = matchings.length;
    console.log(`Total matchings: ${totalCount}`);
    // Iterate over each matching and update the amazon_seller_id field
    for (const matching of matchings) {
      console.log(`Updating amazon_seller_id: ${count++} / ${totalCount}`);
      // Parse the amazon_seller_id field if it's not an array
      const prospect = await prisma.prospect.findUnique({
        where: { prospect_id: matching.prospect_id },
        select: { amazon_seller_id: true },
      });

      if (prospect) {
        matching.amazon_seller_id = prospect.amazon_seller_id;
        // Update the matching record with the new amazon_seller_id
        await prisma.clientsProspectsMatching.update({
          where: { match_id: matching.match_id },
          data: matching,
        });
      }
    }
  } catch (error) {
    console.error("Error updating amazon_seller_id:", error);
  }
}

async function fillCountrySeller() {
  try {
    const companies = await prisma.company.findMany({
      select: {
        amazon_seller_id: true,
        smartscout_country: true,
        seller_url: true,
      },
    });

    let count = 1;
    let totalCount = companies.length;
    console.log(`Total companies: ${totalCount}`);
    for (const data of companies) {
      console.log(`Updating country seller: ${count++} / ${totalCount}`);
      // Upload data into the Seller Country Matching Table
      if (data.smartscout_country && data.amazon_seller_id) {
        const result = await prisma.sellerCountryMatching.findFirst({
          where: {
            amazon_seller_id: data.amazon_seller_id,
            smartscout_country: data.smartscout_country,
          },
        });

        if (!result) {
          console.log("Creating New Seller Country Matching row:");
          await prisma.sellerCountryMatching.create({
            data: {
              amazon_seller_id: data.amazon_seller_id,
              smartscout_country: data.smartscout_country,
              seller_url: data.seller_url,
            },
          });
        }
      }
    }
  } catch (error) {
    console.error("Error updating country seller:", error);
  }
}

async function updateDerivedSalesEstimates() {
  try {
    // Fetch all companies from the database
    const companies = await prisma.company.findMany({
      where: {
        derived_estimate_sales: { not: 0 },
      },
    });

    let count = 1;
    let totalCount = companies.length;
    console.log(`Total companies: ${totalCount}`);
    // Iterate over each company and update the derived_sales_estimate field
    for (const company of companies) {
      console.log(
        `Updating derived_sales_estimate: ${count++} / ${totalCount}`,
      );
      // Calculate the derived sales estimate based on the estimate_sales field
      company.derived_estimate_sales = convertEstimateSales(
        company.estimate_sales,
      );

      // Update the company record with the new derived_sales_estimate
      await prisma.company.update({
        where: { id: company.id },
        data: company,
      });
    }

    console.log("Derived sales estimates updated successfully.");
  } catch (error) {
    console.error("Error updating derived sales estimates:", error);
  } finally {
    await prisma.$disconnect();
  }
}

async function imposeUniquenessOnCompany() {
  try {
    const counts = await prisma.company.groupBy({
      by: ["amazon_seller_id"],
      _count: {
        id: true,
      },
      having: {
        id: {
          _count: {
            gt: 1,
          },
        },
      },
    });

    let count = 1;
    let totalCount = counts.length;
    const country_order = ["US", "UK", "CA", "CD"];
    console.log(`Total companies: ${totalCount}`);

    for (const data of counts) {
      console.log(`Imposing uniqueness on company: ${count++} / ${totalCount}`);
      const companies = await prisma.company.findMany({
        where: {
          amazon_seller_id: data.amazon_seller_id,
        },
        include: {
          prospects: true,
        },
      });

      console.log(`Companies: ${JSON.stringify(companies, null, 2)}`);

      // Sort the companies by country in country_order
      companies.sort((a, b) => {
        return (
          country_order.indexOf(a.smartscout_country) -
          country_order.indexOf(b.smartscout_country)
        );
      });

      // Keep the first company record and delete the rest
      const [firstCompany, ...restCompanies] = companies;
      for (const company of restCompanies) {
        console.log(`Deleting company: ${company.id}`);
        if (company.prospects.length > 0) {
          const prospects = company.prospects;
          for (const prospect of prospects) {
            console.log(
              `Updating prospect: ${prospect.prospect_id} with company: ${firstCompany.id} from company: ${company.id}`,
            );
            await prisma.prospect.update({
              where: { prospect_id: prospect.prospect_id },
              data: {
                company_id: firstCompany.id,
              },
            });
          }
        }
        await prisma.company.delete({
          where: { id: company.id },
        });
      }
    }
  } catch (error) {
    console.error("Error imposing uniqueness on company:", error);
  }
}

async function updateDuplicateProspects() {
  try {
    // Find all prospects with duplicate emails
    const counts = await prisma.prospect.groupBy({
      by: ["email"],
      _count: {
        prospect_id: true,
      },
      having: {
        prospect_id: {
          _count: {
            gt: 1,
          },
        },
      },
    });

    let count = 1;
    let totalCount = counts.length;
    console.log(`Total duplicate email groups: ${totalCount}`);

    for (const data of counts) {
      console.log({ data });
      if (data.email === null || data.email === "") {
        continue;
      }
      console.log("---------------------------------------");
      console.log(`Processing duplicate group: ${count++} / ${totalCount}`);

      // Get all prospects with the same email, including their matching records
      const prospects = await prisma.prospect.findMany({
        where: {
          email: data.email,
        },
        include: {
          clientsProspects: true,
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      if (prospects.length > 1 && prospects.length < 10) {
        for (let i = 1; i < prospects.length; i++) {
          const prospect = prospects[i];
          console.log(`Deleting prospect: ${prospect.prospect_id}`);
          const clientsProspects = prospect.clientsProspects;
          for (const clientProspect of clientsProspects) {
            console.log(`Update client prospect: ${clientProspect.match_id}`);
            await prisma.clientsProspectsMatching.update({
              where: { match_id: clientProspect.match_id },
              data: {
                prospect_id: prospects[0].prospect_id,
              },
            });
          }
          await prisma.prospect.delete({
            where: { prospect_id: prospect.prospect_id },
          });
        }
      }
    }
    console.log("Successfully cleaned up duplicate prospects");
  } catch (error) {
    console.error("Error updating duplicate prospects:", error);
    throw error; // Re-throw to handle in calling function
  }
}
async function CreateCompanyCsv() {
  try {
    const companies = await prisma.company.findMany({
      select: {
        amazon_seller_id: true,
        website_status: true,
      },
    });
    console.log("Companies:", companies.length);
    // SAVE CSV FILE WITH ALL DATA
    const createCsvWriter = require("csv-writer").createObjectCsvWriter;
    const csvWriter = createCsvWriter({
      path: "/Users/<USER>/personal/SellerBot/backups/company_status.csv",
      header: [
        { id: "amazon_seller_id", title: "seller_id" },
        { id: "website_status", title: "status" },
      ],
    });
    // const sanitizedCompanies = companies.map(company => ({
    //   seller_id: String(company.seller_id || ''),
    //   status: String(company.status || '')
    // }));

    await csvWriter.writeRecords(companies);
    console.log("CSV file has been created successfully");
  } catch (error) {
    console.error("Error creating CSV file:", error);
  }
}

async function main() {
  // await updateDuplicateProspects();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
