/**
 * AI Metadata Helper for SellerBot
 * 
 * This utility helps generate consistent metadata for AI requests with the standardized
 * tagging format: [sellerbot, service_name, function_name]
 * 
 * All AI calls in SellerBot should use LiteLLM as the base URL with this tagging system.
 */

const os = require('os');
const path = require('path');

class AIMetadataHelper {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
    }

    /**
     * Generate a unique session ID for this application instance
     */
    generateSessionId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `sellerbot_${timestamp}_${random}`;
    }

    /**
     * Generate a unique request ID
     */
    generateRequestId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `req_${timestamp}_${random}`;
    }

    /**
     * Get calling function information from stack trace
     */
    getCallingFunction() {
        const stack = new Error().stack;
        const stackLines = stack.split('\n');
        
        // Skip the first few lines (Error, this function, and immediate caller)
        for (let i = 3; i < stackLines.length; i++) {
            const line = stackLines[i];
            if (line.includes('at ') && !line.includes('node_modules')) {
                // Extract function name and file
                const match = line.match(/at\s+([^\s]+)\s+\(([^)]+)\)/);
                if (match) {
                    const functionName = match[1];
                    const filePath = match[2];
                    const fileName = path.basename(filePath);
                    return {
                        function: functionName,
                        file: fileName,
                        path: filePath
                    };
                }
                
                // Handle anonymous functions
                const anonymousMatch = line.match(/at\s+([^(]+)/);
                if (anonymousMatch) {
                    const location = anonymousMatch[1].trim();
                    return {
                        function: 'anonymous',
                        file: path.basename(location),
                        path: location
                    };
                }
            }
        }
        
        return {
            function: 'unknown',
            file: 'unknown',
            path: 'unknown'
        };
    }

    /**
     * Generate standardized tags in the format: [sellerbot, service_name, function_name]
     * Additional tags follow the pattern: key:value
     */
    generateStandardizedTags(serviceName, functionName, options = {}) {
        const caller = this.getCallingFunction();
        const requestId = this.generateRequestId();

        // Core standardized tags: [sellerbot, service_name, function_name]
        const tags = [
            'sellerbot',
            serviceName || 'unknown_service',
            functionName || caller.function || 'unknown_function'
        ];

        // Additional metadata tags in key:value format
        const metadataTags = [
            `requestId:${requestId}`,
            `sessionId:${this.sessionId}`,
            `file:${caller.file}`,
            `environment:${process.env.NODE_ENV || 'development'}`,
            `timestamp:${new Date().toISOString()}`
        ];

        // Add business context tags
        if (options.useCase) metadataTags.push(`useCase:${options.useCase}`);
        if (options.operationType) metadataTags.push(`operationType:${options.operationType}`);
        if (options.feature) metadataTags.push(`feature:${options.feature}`);
        if (options.model) metadataTags.push(`modelRequested:${options.model}`);
        if (options.temperature) metadataTags.push(`temperature:${options.temperature}`);
        if (options.priority) metadataTags.push(`priority:${options.priority}`);
        if (options.batchId) metadataTags.push(`batchId:${options.batchId}`);
        if (options.campaignId) metadataTags.push(`campaignId:${options.campaignId}`);
        if (options.domain) metadataTags.push(`domain:${options.domain}`);
        if (options.assistantId) metadataTags.push(`assistantId:${options.assistantId}`);

        // Add custom tags
        if (options.customTags && Array.isArray(options.customTags)) {
            metadataTags.push(...options.customTags);
        }

        return [...tags, ...metadataTags];
    }

    /**
     * Generate LiteLLM compatible request body with standardized metadata
     */
    generateLiteLLMRequestBody(messages, serviceName, functionName, options = {}) {
        const tags = this.generateStandardizedTags(serviceName, functionName, options);
        const requestId = this.generateRequestId();

        // Only include valid OpenAI API parameters
        const requestBody = {
            model: options.model || 'gpt-4o',
            messages: messages,
            user: options.userId || options.user || 'system',
            metadata: {
                tags: tags,
                request_id: requestId,
                session_id: this.sessionId,
                timestamp: new Date().toISOString(),
                service: serviceName,
                function: functionName
            }
        };

        // Add optional parameters only if they are provided
        if (options.temperature !== undefined) requestBody.temperature = options.temperature;
        if (options.max_tokens !== undefined) requestBody.max_tokens = options.max_tokens;
        if (options.top_p !== undefined) requestBody.top_p = options.top_p;
        if (options.frequency_penalty !== undefined) requestBody.frequency_penalty = options.frequency_penalty;
        if (options.presence_penalty !== undefined) requestBody.presence_penalty = options.presence_penalty;
        if (options.stop !== undefined) requestBody.stop = options.stop;
        if (options.stream !== undefined) requestBody.stream = options.stream;
        if (options.seed !== undefined) requestBody.seed = options.seed;
        if (options.tools !== undefined) requestBody.tools = options.tools;
        if (options.tool_choice !== undefined) requestBody.tool_choice = options.tool_choice;
        if (options.response_format !== undefined) requestBody.response_format = options.response_format;

        return requestBody;
    }

    /**
     * Generate headers for HTTP requests with metadata
     */
    generateHeaders(serviceName, functionName, options = {}) {
        const requestId = this.generateRequestId();
        
        return {
            'X-LiteLLM-Service': serviceName,
            'X-LiteLLM-Function': functionName,
            'X-Request-ID': requestId,
            'X-Session-ID': this.sessionId,
            'X-Service': 'SellerBot',
            'X-Environment': process.env.NODE_ENV || 'development',
            'X-Use-Case': options.useCase || 'general',
            'X-Operation-Type': options.operationType || 'chat_completion',
            'X-User-ID': options.userId || 'system'
        };
    }

    /**
     * Log metadata for debugging purposes
     */
    logMetadata(metadata, level = 'info') {
        if (process.env.AI_METADATA_LOGGING === 'true') {
            console.log(`[AI_METADATA_${level.toUpperCase()}]`, JSON.stringify(metadata, null, 2));
        }
    }

    /**
     * Create a scoped metadata generator for a specific service
     */
    createServiceGenerator(serviceName) {
        return {
            generateTags: (functionName, options = {}) => {
                return this.generateStandardizedTags(serviceName, functionName, options);
            },
            generateRequestBody: (messages, functionName, options = {}) => {
                return this.generateLiteLLMRequestBody(messages, serviceName, functionName, options);
            },
            generateHeaders: (functionName, options = {}) => {
                return this.generateHeaders(serviceName, functionName, options);
            }
        };
    }
}

// Create singleton instance
const aiMetadataHelper = new AIMetadataHelper();

// Pre-configured generators for SellerBot services
const serviceGenerators = {
    scrapeGPT: aiMetadataHelper.createServiceGenerator('scrapeGPT'),
    leadGeneration: aiMetadataHelper.createServiceGenerator('leadGeneration'),
    emailAnalysis: aiMetadataHelper.createServiceGenerator('emailAnalysis'),
    assistant: aiMetadataHelper.createServiceGenerator('assistant'),
    centralizedAI: aiMetadataHelper.createServiceGenerator('centralizedAI'),
    portkeyWrapper: aiMetadataHelper.createServiceGenerator('portkeyWrapper'),
    csvProcessor: aiMetadataHelper.createServiceGenerator('csvProcessor'),
    dataAnalysis: aiMetadataHelper.createServiceGenerator('dataAnalysis')
};

module.exports = {
    AIMetadataHelper,
    aiMetadataHelper,
    serviceGenerators
};
