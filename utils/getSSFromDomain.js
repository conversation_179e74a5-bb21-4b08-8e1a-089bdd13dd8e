const puppeteerInstance = require("./puppeteer");

async function getScreenshotFromDomain(url) {
  const page = await puppeteerInstance.getNewPage();

  try {
    await page.goto(url, { waitUntil: "networkidle0", timeout: 60000 });

    const screenshot = await page.screenshot({
      fullPage: true,
      encoding: "base64",
      type: "jpeg",
    });

    return screenshot;
  } catch (error) {
    console.error("Error taking screenshot:", error);
    console.error(url);
    return null;
  } finally {
    await page.close();
  }
}

module.exports = { getScreenshotFromDomain };
