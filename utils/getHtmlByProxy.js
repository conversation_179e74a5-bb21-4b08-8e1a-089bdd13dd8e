const axios = require("axios");
const dotenv = require("dotenv");
dotenv.config();
const fs = require("fs");
const { S3Service, sanitizeFilename, createKeyFromAmazonUrl } = require("./s3");

async function getHtmlByProxy(
  url,
  clientId,
  retries,
  useS3Cache = true,
  ttlHours = process.env.S3_CACHE_TTL_HOURS,
  cacheKey = null
) {
  console.log("Fetching data from url:------------------", url);

  const SCRAPER_API_MAX_RETRIES = process.env.SCRAPER_API_MAX_RETRIES;
  const SCRAPER_API_HOST = process.env.SCRAPER_API_HOST;
  const SCRAPER_API_PORT = process.env.SCRAPER_API_PORT;
  const SCRAPER_API_USERNAME = process.env.SCRAPER_API_USERNAME;
  const SCRAPER_API_KEY = process.env.SCRAPER_API_KEY;
  ttlHours = parseInt(ttlHours || "24");

  // Set default retries if not provided
  if (retries === undefined) {
    retries = SCRAPER_API_MAX_RETRIES;
  }

  // Generate a consistent key for the URL
  cacheKey =
    typeof cacheKey === "string"
      ? sanitizeFilename(cacheKey)
      : sanitizeFilename(url);

  try {
    // Check if we have this URL cached in S3
    if (useS3Cache) {
      try {
        // Check if we have a valid (non-expired) cached version
        const hasValidCache = await S3Service.objectIsValid(cacheKey, ttlHours);

        if (hasValidCache) {
          console.log("Found valid cached HTML for URL in S3:", url);
          console.log("with cacheKey", cacheKey);
          // Get the cached HTML with metadata
          const result = await S3Service.getFromS3(cacheKey, false, true);

          if (result && result.data) {
            console.log(
              "Using cached HTML from S3 (cached at:",
              result.metadata["scraped-timestamp"] || "unknown time",
              ")"
            );
            return result.data;
          }
        } else {
          // We might have the HTML but it's expired
          const hasExpiredCache = await S3Service.objectExists(cacheKey);
          if (hasExpiredCache) {
            console.log(
              "Found expired cached HTML for URL in S3, fetching fresh data:",
              url
            );
          }
        }
      } catch (cacheError) {
        console.error("Error checking S3 cache:", cacheError);
        // Continue with the API request if there's an error with the cache
      }
    }

    // If we don't have cached data or caching is disabled, make the API request
    const response = await axios.get(url, {
      proxy: {
        host: SCRAPER_API_HOST,
        port: SCRAPER_API_PORT,
        auth: {
          username: SCRAPER_API_USERNAME,
          password: SCRAPER_API_KEY,
        },
        protocol: "http",
      },
    });

    console.log("Fetching URL Completed---------------", { url });
    const data = response.data;

    // Cache the HTML in S3 if caching is enabled
    if (useS3Cache && data) {
      try {
        const metadata = {
          "original-url": url,
          "scraped-timestamp": new Date().toISOString(),
        };
        await S3Service.uploadToS3(data, cacheKey, false, metadata);
        console.log("Stored HTML in S3 cache for URL:", url);
        console.log("with cacheKey", cacheKey);
      } catch (cacheError) {
        console.error("Error storing HTML in S3 cache:", cacheError);
        // Continue even if caching fails
      }
    }

    return data;
  } catch (error) {
    console.error("Error Stack:", error.stack);

    if (error.response && error.response.status === 403 ) {
      // sendErrorEmail("proxyError")
      console.log("[Error in proxyCall]", error.response.data);
      throw new Error("403 You have used up all your API credits.");
    }
    if (error.response && error.response.status === 404) {
      console.log("PAGE NOT FOUND!");

      // Cache the 404 response in S3 to avoid future requests to non-existent pages
      if (useS3Cache) {
        try {
          // Store an empty object or a marker to indicate this URL returns 404
          const metadata = {
            "original-url": url,
            "scraped-timestamp": new Date().toISOString(),
            status: "404",
          };
          await S3Service.uploadToS3(
            "<!-- 404 PAGE NOT FOUND -->",
            cacheKey,
            false,
            metadata
          );
          console.log("Stored 404 marker in S3 cache for URL:", url);
        } catch (cacheError) {
          console.error("Error storing 404 marker in S3 cache:", cacheError);
        }
      }

      return {};
    }

    if (error.response && error.response.status >= 500) {
      console.log("[Error in proxyCall]", error.response.data);
      throw new Error("500 Internal Server Error. Please try again later.");
    }

    if (retries > 0) {
      console.log(`[getHtmlByProxy]: Retrying... (${retries} retries left)`);
      return await getHtmlByProxy(url, clientId, retries - 1, useS3Cache, ttlHours, cacheKey);
    } else {
      console.log("[Error in proxyCall]", error);
      throw new Error("Max retries exceeded. Unable to fetch data.");
    }
  }
}
module.exports = { getHtmlByProxy };

// async function main() {
//   const url = "https://www.amazon.com/sp?seller=ANCOXLL4WLFL6";
//   const res = await getHtmlByProxy(
//     url,
//     1,
//     2,
//     true,
//     24,
//     createKeyFromAmazonUrl(url)
//   );
// }
// main();
