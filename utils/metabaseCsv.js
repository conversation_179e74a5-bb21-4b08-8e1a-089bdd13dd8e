const prisma = require("../database/prisma/getPrismaClient");
require('dotenv').config();
const schemaName = 'public2';

async function deleteAllUploadCsvTables() {
  try {
    console.log('Connected to PostgreSQL database');

    // Find all tables with prefix "uploadcsv_public_"
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ${schemaName}
      AND table_name LIKE 'uploadcsv_public_%'
      ORDER BY table_name;
    `;

    console.log(`Found ${tables.length} tables with prefix "uploadcsv_public_" in schema "${schemaName}"`);

    if (tables.length === 0) {
      console.log('No tables found with the specified prefix');
      return {
        deletedCount: 0,
        deletedTables: [],
        errors: []
      };
    }

    // Display all found tables
    console.log('Tables found:');
    tables.forEach((table, index) => {
      console.log(`${index + 1}. ${table.table_name}`);
    });

    const deletedTables = [];
    const errors = [];
    // Delete ALL tables with the prefix
    console.log('\n--- Deleting ALL tables with prefix "uploadcsv_public_" ---');
    
    for (const table of tables) {
      const tableName = table.table_name;
      
      try {
        await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS "${schemaName}"."${tableName}" CASCADE;`);
        console.log(`✅ Deleted table: ${tableName}`);
        deletedTables.push(tableName);
      } catch (error) {
        console.error(`❌ Error deleting table ${tableName}:`, error.message);
        errors.push({
          table: tableName,
          error: error.message
        });
    }
    }
    
    console.log(`\n🎉 Finished deleting ${tables.length} tables with prefix "uploadcsv_public_"`);

    return {
      deletedCount: deletedTables.length,
      deletedTables,
      errors
    };

  } catch (error) {
    console.error('Error executing database operations:', error);
  } finally {
    await prisma.$disconnect();
    console.log('Database connection closed');
  }
}


// Function to delete individual table by name
async function deleteIndividualUploadCsvTable(tableName) {
  try {
    console.log(`Connected to PostgreSQL database via Prisma`);
    console.log(`Attempting to delete table: "${tableName}" from schema: "${schemaName}"`);

    // First, check if the table exists in the specified schema
    const tableExists = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ${schemaName}
      AND table_name = ${tableName};
    `;

    if (tableExists.length === 0) {
      console.log(`❌ Table "${tableName}" not found in schema "${schemaName}"`);
      return {
        success: false,
        message: `Table "${tableName}" not found in schema "${schemaName}"`,
        deletedTable: null,
        error: null
      };
    }

    console.log(`✅ Table "${tableName}" found in schema "${schemaName}"`);

    // Delete the specific table
    try {
      await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS "${schemaName}"."${tableName}" CASCADE;`);
      console.log(`✅ Successfully deleted table: ${tableName}`);
      
      return {
        success: true,
        message: `Successfully deleted table "${tableName}" from schema "${schemaName}"`,
        deletedTable: tableName,
        error: null
      };
    } catch (deleteError) {
      console.error(`❌ Error deleting table ${tableName}:`, deleteError.message);
      return {
        success: false,
        message: `Failed to delete table "${tableName}"`,
        deletedTable: null,
        error: deleteError.message
      };
    }

  } catch (error) {
    console.error('Error executing database operations:', error);
    return {
      success: false,
      message: 'Database operation failed',
      deletedTable: null,
      error: error.message
    };
  } finally {
    await prisma.$disconnect();
    console.log('Database connection closed');
  }
}

// Function to list all tables with the prefix without deleting (for verification)
async function listUploadCsvTables() {
  try {
    console.log(`Listing tables with prefix "uploadcsv_public_" in schema: ${schemaName}`);

    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ${schemaName}
      AND table_name LIKE 'uploadcsv_public_%'
      ORDER BY table_name;
    `;

    console.log(`Found ${tables.length} tables with prefix "uploadcsv_public_" in schema "${schemaName}"`);

    if (tables.length === 0) {
      console.log('No tables found with the specified prefix');
    } else {
      console.log('Tables found:');
      tables.forEach((table, index) => {
        console.log(`${index + 1}. ${table.table_name}`);
      });
    }

    return tables;
  } catch (error) {
    console.error('Error listing tables:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Export functions for use in other modules
module.exports = {
  deleteAllUploadCsvTables,
  deleteIndividualUploadCsvTable,
  listUploadCsvTables,
};

// Run the script if called directly
// if (require.main === module) {
//   console.log('Starting table deletion script...');
//   // For public2 schema:
//   deleteAllUploadCsvTables();

  
//   // To just list tables without deleting:
//   listUploadCsvTables();
// }