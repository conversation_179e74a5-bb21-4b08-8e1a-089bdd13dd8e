const { processCSV, processJsonData } = require("./csvParser");
const { convertEstimateSales } = require("./columnParser");
const { extractDomain } = require("./domainHelper");
const { getHtmlByProxy } = require("./getHtmlByProxy");
const { processSellerPage } = require("./processSellerPage");
const { appendLeadsEmails } = require("./appendLeadsEmails");

const { getSellerImage } = require("./getSellerImage");
const { puppeteer } = require("./puppeteer");
const { removeSpecialCharacters } = require("./removeSpecialCharacters");
const { generateMetaData } = require("./generateMetaData");

module.export = {
  processCSV,
  processJsonData,
  convertEstimateSales,
  extractDomain,
  getHtmlByProxy,
  processSellerPage,
  appendLeadsEmails,

  getSellerImage,
  puppeteer,
  removeSpecialCharacters,
  generateMetaData,
};
