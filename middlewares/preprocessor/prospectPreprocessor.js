const BasePreprocessor = require("./basePreprocessor");
const { extractDomain } = require("../../utils/domainHelper");
const prisma = require("../../database/prisma/getPrismaClient");
/**
 * Prospect-specific preprocessor
 */
class ProspectPreprocessor extends BasePreprocessor {
  async after(data) {
    // console.log("Before Prospect Preprocessor", data);
    if (
      (data.website && typeof data.domain == "string" && !data.domain.trim()) ||
      (!data.domain && data.website)
    ) {
      data.domain = data.website;
    }

    if (data.domain) {
      data.domain = extractDomain(data.domain);
    }

    if (data.email_status) {
      data.email_status = data.email_status.toUpperCase();
    }
  
    return data;
  }
}

module.exports = ProspectPreprocessor;
