/**
 * Base Preprocessor class that defines the interface for all preprocessors
 */
class BasePreprocessor {
  /**
   * Runs before schema initialization
   * @param {Object} data - Raw data from CSV
   * @returns {Object} - Processed data
   */
  async before(data) {
    return data;
  }

  /**
   * Runs after schema initialization but before validation
   * @param {Object} transformedData - Data after schema transformation
   * @returns {Object} - Processed data
   */
  async after(transformedData) {
    return transformedData;
  }


  /**
   * Runs after Prisma TypeCast operations
   * @param {Object} transformedData - Data after validation
   * @returns {Object} - Final processed data
   */
  async afterPrismaTypeCast(transformedData) {
    return transformedData;
  }
  
}

module.exports = BasePreprocessor;
