const BasePreprocessor = require("./basePreprocessor");
const { extractDomain } = require("../../utils/domainHelper");
const { convertEstimateSales } = require("../../utils/columnParser");

/**
 * Company-specific preprocessor
 */
class CompanyPreprocessor extends BasePreprocessor {
  async afterPrismaTypeCast(transformedData) {
    if (transformedData.website) {
      transformedData.domain = extractDomain(transformedData.website);
    }

    if (transformedData.estimate_sales) {
      transformedData.derived_estimate_sales = convertEstimateSales(
        transformedData.estimate_sales
      );
    }

    if (typeof transformedData.website_status !== "string") {
      return transformedData;
    }

    if (
      [
        "final correct",
        "maybe",
        "final correct - unverified",
        "maybe also",
      ].some((status) => {
        return status === transformedData.website_status.toLowerCase().trim();
      })
    ) {
      return transformedData;
    }

    transformedData.website = "";

    return transformedData;
  }
}

module.exports = CompanyPreprocessor;
