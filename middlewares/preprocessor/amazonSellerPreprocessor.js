const BasePreprocessor = require("./basePreprocessor");
const { extractDomain } = require("../../utils/domainHelper");

/**
 * Amazon Seller-specific preprocessor
 */
class AmazonSellerPreprocessor extends BasePreprocessor {
  async afterPrismaTypeCast(transformedData) {
    if (transformedData.domain) {
      const originalDomain = transformedData.domain;
      
      // Handle case where domain is the literal string "null" or "undefined"
      if (originalDomain === "null" || originalDomain === "undefined" || originalDomain === "") {
        console.log(`[Domain] Empty/null domain detected: ${originalDomain}`);
        transformedData.domain = null;
        return transformedData;
      }
      
      const extractedDomain = extractDomain(transformedData.domain);
      
      // If domain extraction failed (returned null/empty), just set domain to null
      // Don't add validation errors, just silently ignore invalid domains
      if (originalDomain && (extractedDomain === null || extractedDomain === "" || extractedDomain === undefined)) {
        console.log(`[Domain] Invalid domain ignored: ${originalDomain}`);
        transformedData.domain = null;
      } else {
        transformedData.domain = extractedDomain;
        console.log(`[Domain] Valid domain processed: ${originalDomain} -> ${extractedDomain}`);
      }
    }

    return transformedData;
  }
}

module.exports = AmazonSellerPreprocessor;
