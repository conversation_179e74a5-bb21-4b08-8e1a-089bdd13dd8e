const jwt = require("jsonwebtoken");
const JWT_SECRET = process.env.JWT_SECRET;

function userAuth(req, res, next) {
  try {
    // Get token from Authorization header or cookie
    const authHeader = req.headers["authorization"];
    const tokenFromHeader = authHeader && authHeader.split(" ")[1];
    const tokenFromCookie = req.cookies?.token;
    const token = tokenFromHeader || tokenFromCookie;

    if (!token) {
      return res
        .status(401)
        .json({ error: "Unauthorized - No token provided" });
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Add decoded token to request object
    req.user = decoded;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(403).json({ error: "Invalid token" });
    }
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(403).json({ error: "Token expired" });
    }
    return res.status(500).json({ error: "Internal server error" });
  }
}

function adminAuth(req, res, next) {
  try {
    // Get token from Authorization header or cookie
    const authHeader = req.headers["authorization"];
    const tokenFromHeader = authHeader && authHeader.split(" ")[1];
    const tokenFromCookie = req.cookies?.token;
    const token = tokenFromHeader || tokenFromCookie;
    // console.log(token);
    if (!token) {
      return res
        .status(401)
        .json({ error: "Unauthorized - No token provided" });
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check for admin role
    if (decoded.userType !== "admin") {
      return res
        .status(403)
        .json({ error: "Forbidden - Admin access required" });
    }

    // Add decoded token to request object
    req.user = decoded;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      console.log(JWT_SECRET);
      return res.status(403).json({ error: "Invalid token" });
    }
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(403).json({ error: "Token expired" });
    }
    return res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = { userAuth, adminAuth };
