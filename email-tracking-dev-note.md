# Development Note: SmartLead Email Integration

## Overview
This update implements a new data model and integration with SmartLead's API to fetch and store client, campaign, lead, and email data. The implementation enables the system to track email communications with leads across different campaigns and clients.

## Schema Changes
Added five new models to the Prisma schema:

1. **Client**
   - Represents SmartLead clients
   - Contains client ID and name
   - One-to-many relationship with Campaigns

2. **Campaign**
   - Represents marketing campaigns in SmartLead
   - Contains campaign details (ID, name, parent campaign ID)
   - Many-to-one relationship with Client
   - One-to-many relationship with SmartLead_Lead

3. **SmartLead_Lead**
   - Represents leads associated with campaigns
   - Contains lead details (ID, email, website)
   - Many-to-one relationship with Campaign
   - One-to-many relationship with Email
   - Optional reference to lead category

4. **SmartLead_Lead_Category**
   - Represents categories for leads
   - Contains category ID, name, and creation timestamp

5. **Email**
   - Represents email communications with leads
   - Contains email details (subject, body, message ID, etc.)
   - Tracks email type (sent, reply, forward)
   - Many-to-one relationship with SmartLead_Lead
   - Unique constraint on messageId and time to prevent duplicates

## Implementation Details

### API Integration
- Integrated with SmartLead API endpoints:
  - `/api/v1/campaigns` - Fetch all campaigns
  - `/api/v1/campaigns/{id}/leads` - Fetch leads for a campaign
  - `/api/v1/campaigns/{id}/leads/{leadId}/message-history` - Fetch email history
  - `/api/v1/leads/fetch-categories` - Fetch lead categories

### Data Flow
1. Import clients from CSV file
2. Fetch and store campaign data for all clients
3. Fetch and store lead categories
4. For each campaign:
   - Fetch and store leads data
   - Generate email threads
   - Fetch and store email communication history

### Key Features
- Pagination support for handling large datasets of leads
- Rate limiting to prevent API throttling
- Upsert operations to handle data updates
- Email thread generation for analyzing communication history

## Files Added/Modified
- `services/saveEmails/index.js` - Main orchestration script
- `services/saveEmails/getCampaignData.js` - Fetches campaign data from API
- `services/saveEmails/getLeadsData.js` - Fetches leads data for campaigns
- `services/saveEmails/getLeadCategory.js` - Fetches lead categories
- `services/saveEmails/generateThreads.js` - Generates email threads
- `services/saveEmails/getMessageData.js` - Fetches and stores email data
- `database/prisma/schema.prisma` - Added new models

