const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "API Documentation",
      version: "1.0.0",
      description: "API Documentation",
    },

    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
      responses: {
        UnauthorizedError: {
          description: "Unauthorized - No token provided",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                    example: "Unauthorized - No token provided",
                  },
                },
              },
            },
          },
        },
        ForbiddenError: {
          description: "Forbidden - Not an admin or invalid token",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                    example: "Forbidden - Admin access required",
                  },
                },
              },
            },
          },
        },
        InternalServerError: {
          description: "Internal server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                    example: "Internal server error",
                  },
                },
              },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./routes/*.js", "./routes.js", "./docs/swagger/*.js"], // Path to the API routes and Swagger docs
};

const specs = swaggerJsdoc(options);

// Custom Swagger UI options to add token input and server selection
const swaggerUiOptions = {
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    tryItOutEnabled: true,
    docExpansion: "none",
    tagsSorter: "alpha",
    operationsSorter: "alpha",
    withCredentials: true,
  },
  customCss: `
    .topbar-wrapper img { content: url('/logo.png'); }
    .swagger-ui .topbar { background-color: #ffffff; }
    .swagger-ui .info .title { font-size: 2.5em; }
    .swagger-ui .scheme-container { padding: 15px 0; }
    .swagger-ui .btn.authorize { background-color: #49cc90; }
    .swagger-ui .btn.authorize svg { fill: #fff; }

    /* Custom styles for token input and server selection */
    #custom-token-input {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      width: 100%;
      box-sizing: border-box;
    }
    #custom-server-input {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      width: 100%;
      box-sizing: border-box;
    }
    .custom-button {
      background-color: #4990e2;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    .custom-button:hover {
      background-color: #2d6cbc;
    }
    .custom-controls {
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    .custom-controls h3 {
      margin-top: 0;
      color: #3b4151;
    }
    .server-controls {
      margin-top: 15px;
    }
    .server-controls select {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
      margin-right: 10px;
      min-width: 250px;
    }
    .custom-url-input {
      display: flex;
      align-items: center;
      margin-top: 10px;
    }
    .custom-url-input input {
      flex-grow: 1;
      margin-right: 10px;
    }
  `,
  customSiteTitle: "API Documentation",
  customfavIcon: "/favicon.ico",
  customJs: ["https://code.jquery.com/jquery-3.6.0.min.js"],
  // Add custom HTML to inject token input and server selection
  customfavIcon: "/favicon.ico",
  customSiteTitle: "API Documentation",
  beforeSwaggerInit: function (req, res, next) {
    // Inject custom HTML for token input and server selection
    const customHtml = `
      <div class="custom-controls">
        <h3>API Configuration</h3>
        <div>
          <label for="custom-server-input">Base URL:</label>
          <div style="display: flex; margin-top: 5px;">
            <input type="text" id="custom-server-input" placeholder="Enter base URL (e.g., http://localhost:8000)" style="flex-grow: 1; margin-right: 10px;" />
            <button class="custom-button" id="update-server-btn">Update Server</button>
          </div>
        </div>
        <div style="margin-top: 15px;">
          <label for="custom-token-input">Bearer Token:</label>
          <div style="display: flex; margin-top: 5px;">
            <input type="text" id="custom-token-input" placeholder="Enter your JWT token" style="flex-grow: 1; margin-right: 10px;" />
            <button class="custom-button" id="update-token-btn">Update Token</button>
          </div>
        </div>
      </div>
      <script>
        // Function to initialize our custom controls
        function initCustomControls() {
          if (!window.ui) {
            // If Swagger UI isn't loaded yet, try again in 100ms
            setTimeout(initCustomControls, 100);
            return;
          }

          console.log("Initializing custom controls for Swagger UI");

          // Add event listener for token update button
          document.getElementById('update-token-btn').addEventListener('click', function() {
            const token = document.getElementById('custom-token-input').value;
            if (token) {
              console.log("Updating token");
              // Update the authorization in Swagger UI
              window.ui.preauthorizeApiKey('bearerAuth', token);
              localStorage.setItem('swagger_token', token);
              alert('Token updated successfully!');
            } else {
              alert('Please enter a token!');
            }
          });

          // Add event listener for custom server URL input
          document.getElementById('update-server-btn').addEventListener('click', function() {
            const serverUrl = document.getElementById('custom-server-input').value;
            if (serverUrl) {
              console.log("Updating server URL to:", serverUrl);
              // Update the server URL - this is the key part that needs to work
              window.ui.specActions.updateServerUrl(0, serverUrl);
              localStorage.setItem('swagger_server', serverUrl);
              alert('Server URL updated successfully to: ' + serverUrl);
            } else {
              alert('Please enter a server URL!');
            }
          });

          // Load saved token and server from localStorage if available
          const savedToken = localStorage.getItem('swagger_token');
          const savedServer = localStorage.getItem('swagger_server');

          if (savedToken) {
            console.log("Restoring saved token");
            document.getElementById('custom-token-input').value = savedToken;
            setTimeout(() => {
              window.ui.preauthorizeApiKey('bearerAuth', savedToken);
            }, 500);
          }

          if (savedServer) {
            console.log("Restoring saved server URL:", savedServer);
            document.getElementById('custom-server-input').value = savedServer;
            setTimeout(() => {
              window.ui.specActions.updateServerUrl(0, savedServer);
            }, 500);
          }

          // Add event listener for the login API response to capture the token
          const originalFetch = window.fetch;
          window.fetch = function(url, options) {
            return originalFetch(url, options).then(response => {
              const clonedResponse = response.clone();

              // Check if this is a login response
              if (url.includes('/api/login') && response.ok) {
                clonedResponse.json().then(data => {
                  // Extract token from response
                  const authHeader = response.headers.get('Authorization');
                  if (authHeader && authHeader.startsWith('Bearer ')) {
                    const token = authHeader.substring(7);
                    document.getElementById('custom-token-input').value = token;
                    localStorage.setItem('swagger_token', token);
                    window.ui.preauthorizeApiKey('bearerAuth', token);
                  }
                }).catch(err => {
                  console.error('Error processing login response:', err);
                });
              }

              return response;
            });
          };
        }

        // Wait for Swagger UI to load
        window.addEventListener('load', function() {
          console.log("Window loaded, initializing custom controls");
          // Initialize our custom controls
          initCustomControls();
        });
      </script>
    `;

    // Add the custom HTML to the response
    const originalSend = res.send;
    res.send = function (body) {
      if (body && body.includes('<div id="swagger-ui"></div>')) {
        body = body.replace(
          '<div id="swagger-ui"></div>',
          `<div id="swagger-ui"></div>${customHtml}`
        );
      }
      originalSend.call(this, body);
    };

    next();
  },
};

module.exports = { specs, swaggerUi, swaggerUiOptions };
