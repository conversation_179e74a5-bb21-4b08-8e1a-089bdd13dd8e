const fs = require("fs");
const { google } = require("googleapis");

const ERRORS_CSV_FILE = "errors.csv";
const ERRORS_SHEET_ID = "12gx3PjWpuV2KK-iGiy7xvE6hVtxuNX0rgbOaTyEcG8c";

// Function to sanitize strings for CSV
function sanitizeString(str) {
  if (str === null || str === undefined) return "";
  
  // Convert to string if it's not already
  str = String(str);
  
  // Replace line breaks, tabs and other problematic characters
  return str
    .replace(/\r?\n|\r/g, " ") // Replace line breaks with space
    .replace(/\t/g, " ")       // Replace tabs with space
    .replace(/\s+/g, " ")      // Replace multiple spaces with a single space
    .trim();                   // Trim extra spaces
}

// Function to sanitize an entire object's string values
function sanitizeObject(obj) {
  if (!obj) return {};
  
  const sanitized = {};
  Object.entries(obj).forEach(([key, value]) => {
    sanitized[key] = typeof value === 'string' ? sanitizeString(value) : value;
  });
  return sanitized;
}

const auth = new google.auth.GoogleAuth({
  keyFile: "config/GoogleServiceAccountCreds.json",
  scopes: [
    "https://www.googleapis.com/auth/spreadsheets.readonly",
    "https://www.googleapis.com/auth/spreadsheets",
  ],
});

async function uploadErrorsToGoogleSheet() {
  try {
    const sheets = google.sheets({ version: "v4", auth });

    const csvContent = fs.readFileSync(ERRORS_CSV_FILE, "utf8");
    const rows = csvContent
      .split("\n")
      .map((row) =>
        row
          .split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/)
          .map((cell) => cell.replace(/^"|"$/g, "")),
      );

    console.log("📤 Uploading errors to Google Sheets...");
    await sheets.spreadsheets.values.clear({
      spreadsheetId: ERRORS_SHEET_ID,
      range: "Sheet1",
    });

    await sheets.spreadsheets.values.update({
      spreadsheetId: ERRORS_SHEET_ID,
      range: "Sheet1",
      valueInputOption: "RAW",
      resource: {
        values: rows,
      },
    });

    console.log("✅ Errors uploaded to Google Sheets.");
  } catch (error) {
    console.error("❌ Error uploading errors to Google Sheets:", error);
  }
}

function removeNonFinalCorrectWebsites(data) {
  data.forEach(row => {
    if (row.website_status !== "Final Correct") {
      row.website = "";
    }
  });
  return data;
}

function filterOutErrorRows(data) {
  return data.filter(row => !row.errors || row.errors === "undefined");
}

module.exports = {
  removeNonFinalCorrectWebsites,
  filterOutErrorRows,
  uploadErrorsToGoogleSheet,
  uploadTransformedDataToAPI,
  sanitizeString,
  sanitizeObject
};
