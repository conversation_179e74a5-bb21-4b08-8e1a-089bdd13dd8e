function sanitizeString(value) {
  if (!value) return "";
  return String(value)
    .trim()
    .replace(/[\x00-\x1F\x7F-\x9F]/g, "") // Remove control characters
    // .replace(/['"]/g, "") // Remove quotes
    .replace(/\s+/g, " "); // Normalize whitespace
}

function sanitizeObject(obj) {
  if (!obj || typeof obj !== "object") return {};
  
  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === "string") {
      sanitized[key] = sanitizeString(value);
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === "string" ? sanitizeString(item) : item
      );
    } else if (typeof value === "object" && value !== null) {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  return sanitized;
}

module.exports = {
  sanitizeString,
  sanitizeObject
};
