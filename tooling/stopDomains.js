const fs = require("fs");
const path = require("path");
const prompts = require("prompts");

const filePath = path.join(__dirname, "stopDomains.txt");

(async () => {
  const response = await prompts({
    type: "select",
    name: "option",
    message: "Choose an option:",
    choices: [
      { title: "Update stopDomains.txt", value: "1" },
      { title: "Generate CSV", value: "2" },
    ],
  });

  if (response.option === "1") {
    fs.readFile(filePath, "utf8", (err, data) => {
      if (err) {
        console.error("Error reading file:", err);
        return;
      }

      const domains = data.split("\n").filter(Boolean);
      const formattedDomains = domains
        .map((domain) => `"${domain}",`)
        .join("\n");

      fs.writeFile(filePath, formattedDomains, "utf8", (err) => {
        if (err) {
          console.error("Error writing file:", err);
          return;
        }
        console.log("File has been updated.");
      });
    });
  } else if (response.option === "2") {
    fs.readFile(filePath, "utf8", (err, data) => {
      if (err) {
        console.error("Error reading file:", err);
        return;
      }

      const domains = data.split("\n").filter(Boolean);
      const csvContent = domains
        .map((domain) => domain.replace(/["|,]/g, ""))
        .join("\n");

      const csvFilePath = path.join(__dirname, "stopDomains.csv");
      fs.writeFile(csvFilePath, csvContent, "utf8", (err) => {
        if (err) {
          console.error("Error writing CSV file:", err);
          return;
        }
        console.log("CSV file has been generated.");
      });
    });
  } else {
    console.log("Invalid option.");
  }
})();
