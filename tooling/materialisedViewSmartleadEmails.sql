-- =============================================================================
-- Script to recreate mv_email_with_thread_start materialized view with indexes
-- =============================================================================

BEGIN;

-- Drop the existing materialized view if it exists
-- CASCADE will drop any dependent objects (views, etc.)
DROP MATERIALIZED VIEW IF EXISTS public.mv_email_with_thread_start CASCADE;

-- Create the new materialized view with thread start logic
CREATE MATERIALIZED VIEW public.mv_email_with_thread_start AS
SELECT 
    id,
    "leadId",
    subject,
    body,
    type,
    "toEmailID",
    "fromEmailID",
    "time",
    "messageId",
    "campaingId",
    email_seq_number,
    open_count,
    -- Calculate thread start email ID using window function
    first_value("fromEmailID") OVER (
        PARTITION BY "leadId" 
        ORDER BY
            CASE
                WHEN ("fromEmailID" = '<EMAIL>'::text) THEN 2
                WHEN ("fromEmailID" ~~ '%.onmicrosoft.com'::text) THEN 2
                ELSE 1
            END, 
            "time",
            CASE
                WHEN (type = 'SENT'::text) THEN 1
                WHEN (type = 'FORWARD'::text) THEN 2
                WHEN (type = 'REPLY'::text) THEN 3
                ELSE 4
            END 
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) AS "threadStartEmailId",
    -- Calculate thread destination email ID using same logic
    first_value("toEmailID") OVER (
        PARTITION BY "leadId" 
        ORDER BY
            CASE
                WHEN ("fromEmailID" = '<EMAIL>'::text) THEN 2
                WHEN ("fromEmailID" ~~ '%.onmicrosoft.com'::text) THEN 2
                ELSE 1
            END, 
            "time",
            CASE
                WHEN (type = 'SENT'::text) THEN 1
                WHEN (type = 'FORWARD'::text) THEN 2
                WHEN (type = 'REPLY'::text) THEN 3
                ELSE 4
            END 
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) AS "threadToEmailId"
FROM "Email";

-- =============================================================================
-- Create indexes for optimal query performance
-- =============================================================================

-- Index on leadId for lead-based filtering and partitioning
CREATE INDEX idx_mv_email_leadid 
ON public.mv_email_with_thread_start USING btree ("leadId");

-- Composite index on type and toEmailID for filtering by email type and recipient
CREATE INDEX idx_mv_email_type_toemailid 
ON public.mv_email_with_thread_start USING btree (type, "toEmailID");

-- Index on campaignId for campaign-based queries
CREATE INDEX idx_mv_email_campaingid 
ON public.mv_email_with_thread_start USING btree ("campaingId");

-- Index on time for temporal queries and ordering
CREATE INDEX idx_mv_email_time 
ON public.mv_email_with_thread_start USING btree ("time");

-- Index on fromEmailID for sender-based filtering
CREATE INDEX idx_mv_email_fromemail 
ON public.mv_email_with_thread_start USING btree ("fromEmailID");

-- Covering index with toEmailID as leading column, includes frequently accessed columns
CREATE INDEX idx_mv_email_toemailid_covering 
ON public.mv_email_with_thread_start USING btree ("toEmailID", "campaingId", type, id, "threadStartEmailId");

-- Covering index with campaignId as leading column for campaign analysis
CREATE INDEX idx_mv_email_campaingid_covering 
ON public.mv_email_with_thread_start USING btree ("campaingId", "toEmailID", type);

-- Index on threadToEmailId for thread destination queries
CREATE INDEX idx_mv_email_threadtoemailid 
ON public.mv_email_with_thread_start USING btree ("threadToEmailId");

-- =============================================================================
-- Refresh the materialized view with current data
-- =============================================================================

-- Populate the materialized view with data
REFRESH MATERIALIZED VIEW public.mv_email_with_thread_start;

COMMIT;

-- =============================================================================
-- Verification queries (optional - uncomment to run)
-- =============================================================================

-- Check that the materialized view was created successfully
-- SELECT COUNT(*) as total_rows FROM public.mv_email_with_thread_start;

-- View all indexes on the materialized view
-- \d+ public.mv_email_with_thread_start

-- Or using SQL:
-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'mv_email_with_thread_start' 
-- AND schemaname = 'public'
-- ORDER BY indexname;