const fs = require('fs/promises');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');

const CACHE_DIR = path.join(__dirname, '..', '.cache');
const EMAIL_CACHE_DIR = path.join(CACHE_DIR, 'email');
const DOMAIN_CACHE_DIR = path.join(CACHE_DIR, 'domain');
const SMARTLEAD_CACHE_DIR = path.join(CACHE_DIR, 'smartlead');

const API_CONFIG = {
    EMAIL_ENDPOINT: 'https://metabase.equalcollective.com/api/card/136/query/json',
    DOMAIN_ENDPOINT: 'https://metabase.equalcollective.com/api/card/137/query/json',
    SELLER_PRIORITY_ENDPOINT: 'https://metabase.equalcollective.com/api/card/140/query/json',
    SMARTLEAD_ENDPOINT: 'https://server.smartlead.ai/api/v1/leads/',
    SMARTLEAD_API_KEY: '58b3787d-d0f1-4ee2-a50d-88748716d947_b7p5qdq',
    headers: {
        'X-API-KEY': 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': 'metabase.DEVICE=32cf5983-e659-4b94-bc57-1895c6a4e15e; metabase.TIMEOUT=alive'
    }
};

function extractDomain(url) {
    if (!url || url.toLowerCase() === 'none' || url.toLowerCase() === 'na' || url.toLowerCase() === 'n a') {
        return null;
    }

    // Remove any query parameters and trailing slashes
    url = url.split('?')[0].replace(/\/+$/, '');

    // If it starts with http/https, extract domain
    if (url.startsWith('http')) {
        try {
            const domain = new URL(url).hostname.replace(/^www\./, '');
            return domain;
        } catch (e) {
            return url;
        }
    }

    // If it's just a domain, clean it up
    return url.replace(/^www\./, '');
}

async function ensureCacheDir() {
    await fs.mkdir(EMAIL_CACHE_DIR, { recursive: true });
    await fs.mkdir(DOMAIN_CACHE_DIR, { recursive: true });
    await fs.mkdir(SMARTLEAD_CACHE_DIR, { recursive: true });
}

function sanitizeKey(key) {
    // Hash the key if it contains invalid filesystem characters
    if (/[<>:"/\\|?*]/.test(key)) {
        return crypto.createHash('md5').update(key).digest('hex');
    }
    return key;
}

async function getCachedData(key, type = 'email') {
    const cacheDir = type === 'email' ? EMAIL_CACHE_DIR : type === 'domain' ? DOMAIN_CACHE_DIR : SMARTLEAD_CACHE_DIR;
    const safeKey = sanitizeKey(key);
    const cacheFile = path.join(cacheDir, `${safeKey}.json`);
    
    try {
        const data = await fs.readFile(cacheFile, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return null;
    }
}

async function cacheData(key, data, type = 'email') {
    const cacheDir = type === 'email' ? EMAIL_CACHE_DIR : type === 'domain' ? DOMAIN_CACHE_DIR : SMARTLEAD_CACHE_DIR;
    const safeKey = sanitizeKey(key);
    const cacheFile = path.join(cacheDir, `${safeKey}.json`);
    await fs.writeFile(cacheFile, JSON.stringify(data, null, 2));
}

async function fetchEnrichmentData(email, domain = null) {
    const type = domain ? 'domain' : 'email';
    const key = domain || email;
    const cached = await getCachedData(key, type);
    
    if (cached) {
        console.log(`📦 Using cached ${type} data for: ${key}`);
        if (cached.failed) {
            console.log(`⚠️ Previous attempt failed: ${cached.reason}`);
            return null;
        }
        return cached;
    }

    try {
        const endpoint = domain ? API_CONFIG.DOMAIN_ENDPOINT : API_CONFIG.EMAIL_ENDPOINT;
        const parameters = domain
            ? [{"id":"c72c9ce7-0fc2-4062-b5d5-e52022daac07","type":"category","value":domain,"target":["variable",["template-tag","domain"]]}]
            : [{"id":"7c9b6d93-f4b7-4c49-a35c-14e6332f159c","type":"category","value":email,"target":["variable",["template-tag","email"]]}];

        const response = await axios.post(endpoint, new URLSearchParams({
            parameters: JSON.stringify(parameters)
        }), {
            headers: API_CONFIG.headers
        });

        if (response.data && response.data.length > 0) {
            const enrichmentData = response.data[0];
            await cacheData(key, enrichmentData, type);
            console.log(`✅ Successfully enriched ${type}: ${key}`);
            return enrichmentData;
        } else {
            console.log(`ℹ️ No enrichment data found for ${type}: ${key}`);
            await cacheData(key, {}, type);
            return {};
        }
    } catch (error) {
        console.error(`❌ Error fetching enrichment data for ${key}:`, error.message);
        
        // Only cache API errors (when we have a response), not network errors
        if (error.response) {
            await cacheData(key, { 
                failed: true, 
                reason: `api_error_${error.response.status}`,
                error: error.message
            }, type);
        }
        
        return null;
    }
}

async function fetchSellerPriority(amazon_seller_id) {
    try {
        const parameters = [{
            "id": "c72c9ce7-0fc2-4062-b5d5-e52022daac07",
            "type": "category",
            "value": amazon_seller_id,
            "target": ["variable", ["template-tag", "amazon_seller_id"]]
        }];

        const response = await axios.post(API_CONFIG.SELLER_PRIORITY_ENDPOINT, 
            new URLSearchParams({ parameters: JSON.stringify(parameters) }), 
            { headers: API_CONFIG.headers }
        );

        if (response.data && response.data.length > 0) {
            return response.data[0];
        }
        return null;
    } catch (error) {
        console.error(`❌ Error fetching seller priority for ${amazon_seller_id}:`, error.message);
        return null;
    }
}

async function fetchSmartLeadData(email) {
    const cached = await getCachedData(email, 'smartlead');
    
    if (cached) {
        console.log(`📦 Using cached SmartLead data for: ${email}`);
        if (cached.failed) {
            console.log(`⚠️ Previous SmartLead attempt failed: ${cached.reason}`);
            return null;
        }
        return cached;
    }

    try {
        const response = await axios.get(`${API_CONFIG.SMARTLEAD_ENDPOINT}?api_key=${API_CONFIG.SMARTLEAD_API_KEY}&email=${encodeURIComponent(email)}`);
        
        if (response.data && Object.keys(response.data).length > 0 && response.data.custom_fields) {
            const storefrontUrl = response.data.custom_fields.Storefront_URL;
            if (storefrontUrl) {
                // Extract seller ID from storefront URL
                const sellerIdMatch = storefrontUrl.match(/seller=([A-Z0-9]+)/i);
                if (sellerIdMatch) {
                    const amazon_seller_id = sellerIdMatch[1];
                    const enrichmentData = {
                        amazon_seller_id
                    };

                    // Try to get seller priority data
                    console.log(`🔍 Fetching priority data for seller: ${amazon_seller_id}`);
                    const priorityData = await fetchSellerPriority(amazon_seller_id);
                    if (priorityData && priorityData.jeff_search_priority) {
                        enrichmentData.jeff_search_priority = priorityData.jeff_search_priority;
                        console.log(`✅ Found priority data for seller: ${amazon_seller_id}`);
                    }

                    await cacheData(email, enrichmentData, 'smartlead');
                    console.log(`✅ Successfully enriched from SmartLead: ${email}`);
                    return enrichmentData;
                }
            }
        }
        
        console.log(`ℹ️ No SmartLead enrichment data found for: ${email}`);
        await cacheData(email, {}, 'smartlead');
        return {};
    } catch (error) {
        console.error(`❌ Error fetching SmartLead data for ${email}:`, error.message);
        
        await cacheData(email, { 
            failed: true, 
            reason: error.response ? `api_error_${error.response.status}` : 'network_error',
            error: error.message
        }, 'smartlead');
        
        return null;
    }
}

function mergeEnrichmentData(existingData, newData, source) {
    if (!newData || Object.keys(newData).length === 0) {
        return existingData || {};
    }

    if (!existingData || Object.keys(existingData).length === 0) {
        return { ...newData, _enriched_by: [source] };
    }

    const merged = { ...existingData };
    let enrichedBy = merged._enriched_by || [];
    let hasChanges = false;

    // Only merge if the data is from a different source
    if (!enrichedBy.includes(source)) {
        // For each field in newData
        Object.entries(newData).forEach(([key, value]) => {
            // If the field doesn't exist in existing data or is empty
            if (!merged[key] || merged[key] === '') {
                merged[key] = value;
                hasChanges = true;
            }
            // If both have values but they're different, prefer certain sources
            else if (merged[key] !== value) {
                if (source === 'domain' && key === 'website') {
                    // Prefer domain-based website
                    merged[key] = value;
                    hasChanges = true;
                } else if (source === 'email' && key === 'amazon_seller_id' && !merged.jeff_search_priority) {
                    // Prefer email-based seller ID if we don't have a search priority
                    merged[key] = value;
                    hasChanges = true;
                }
            }
        });

        if (hasChanges) {
            enrichedBy.push(source);
            merged._enriched_by = enrichedBy;
        }
    }

    return merged;
}

async function enrichMeetingsCRMData(data) {
    console.log('\n🔄 Starting data enrichment process...');
    await ensureCacheDir();

    const enrichedData = [];
    let emailSuccessCount = 0;
    let domainSuccessCount = 0;
    let smartleadSuccessCount = 0;
    let failureCount = 0;
    
    for (const record of data) {
        let enrichmentData = {};
        
        // Step 1: Try email-based enrichment first
        if (record.email_1) {
            console.log(`\n🔍 Processing email: ${record.email_1}`);
            const emailData = await fetchEnrichmentData(record.email_1);
            if (emailData && Object.keys(emailData).length > 0) {
                enrichmentData = mergeEnrichmentData(enrichmentData, emailData, 'email');
                emailSuccessCount++;
            }
        }

        // Step 2: Only try domain-based enrichment if email enrichment failed or wasn't attempted
        if (Object.keys(enrichmentData).length === 0 && record.website) {
            const domain = extractDomain(record.website);
            if (domain) {
                console.log(`\n🌐 Processing domain: ${domain}`);
                const domainData = await fetchEnrichmentData(null, domain);
                if (domainData && Object.keys(domainData).length > 0) {
                    enrichmentData = mergeEnrichmentData(enrichmentData, domainData, 'domain');
                    domainSuccessCount++;
                }
            }
        }

        // Step 3: Try SmartLead API if other methods failed
        if (Object.keys(enrichmentData).length === 0 && record.email_1) {
            console.log(`\n🔍 Checking SmartLead for: ${record.email_1}`);
            const smartleadData = await fetchSmartLeadData(record.email_1);
            if (smartleadData && Object.keys(smartleadData).length > 0) {
                enrichmentData = mergeEnrichmentData(enrichmentData, smartleadData, 'smartlead');
                smartleadSuccessCount++;
            }
        }

        if (Object.keys(enrichmentData).length === 0) {
            failureCount++;
        }

        record.SellerBotEnrichedData = enrichmentData;
        enrichedData.push(record);
    }

    console.log(`\n📊 Enrichment Summary:`);
    console.log(`✅ Email successes: ${emailSuccessCount}`);
    console.log(`✅ Domain successes: ${domainSuccessCount}`);
    console.log(`✅ SmartLead successes: ${smartleadSuccessCount}`);
    console.log(`❌ Failures: ${failureCount}`);
    console.log(`📝 Total records: ${data.length}`);

    return enrichedData;
}

module.exports = {
    enrichMeetingsCRMData
};
