# CSV Queue Processing System

This document describes the new CSV queue processing system that allows for automated, batch processing of CSV files with comprehensive logging and error tracking.

## Overview

The CSV queue system consists of three main components:

1. **csvData Table** - Tracks CSV file uploads and overall processing status
2. **csvDataLog Table** - Tracks individual row processing with flexible key columns (key1-key50)
3. **CSV Processing Scheduler** - Automatically processes queued CSV files

## File Structure

- `services/csvProcessor/csvFileProcessor.js` - Handles processing of queued CSV files
- `services/csvProcessor/csvProcessingScheduler.js` - Manages automatic CSV processing
- `utils/csvQueueManagement.js` - Handles CSV queue operations and database management
- `middlewares/csvDataProcessor.js` - Core CSV data processing logic (renamed from processCsv.js)

## Features

- ✅ Queue management for CSV files
- ✅ Row-by-row processing with error tracking
- ✅ Automatic retry mechanisms
- ✅ Progress tracking and statistics
- ✅ Pause/resume functionality
- ✅ Manual processing triggers
- ✅ Comprehensive API endpoints
- ✅ Support for all existing CSV types (prospect, amazon_seller, company, etc.)

## Database Schema

### CsvData Table
- `id` - Primary key
- `file_name` - Original filename
- `file_path` - Server file path
- `file_size` - File size in bytes
- `file_type` - File extension
- `process_type` - Processing type (prospect, amazon_seller, etc.)
- `original_headers` - CSV headers as JSON array
- `total_rows` - Total number of rows
- `header_mappings` - Header to key column mappings
- `status` - Processing status (PENDING, PROCESSING, COMPLETED, FAILED, PAUSED)
- `processed_until` - Number of rows processed
- `is_completed` - Boolean completion flag
- `error_status` - Boolean error flag
- `error_message` - Error details
- `retry_count` - Number of retries attempted
- `max_retries` - Maximum retries allowed
- `last_retry_at` - Last retry timestamp
- `source` - Data source identifier
- `uploaded_by` - User who uploaded the file
- Timestamps

### CsvDataLog Table
- `id` - Primary key
- `data_id` - Foreign key to CsvData
- `upload_log_id` - Optional external log ID
- `row_number` - Row number in CSV
- `headers_map` - Header to key mapping for this row
- `processed` - Boolean processing flag
- `error_status` - Boolean error flag
- `error_message` - Error details for this row
- `retry_count` - Row-specific retry count
- `key1` to `key50` - Flexible data columns
- Timestamps

## API Endpoints

### Upload CSV to Queue
```
POST /api/leadqueue/upload
```
Upload a CSV file to the processing queue.

**Body:**
- `csvFile` (file) - CSV file to upload
- `processType` (string) - Type: prospect, amazon_seller, company, etc.
- `source` (string, optional) - Data source

### Get Queue List
```
GET /api/leadqueue?page=1&limit=20&status=PENDING&processType=prospect
```
Get paginated list of CSV files in queue with optional filtering.

### Get CSV Status
```
GET /api/leadqueue/{csvDataId}/status
```
Get detailed processing status and statistics for a specific CSV.

### Manual Processing
```
POST /api/leadqueue/{csvDataId}/process
```
Manually trigger processing of a specific CSV file.

### Retry Failed Rows
```
POST /api/leadqueue/{csvDataId}/retry?maxRetries=3
```
Retry processing of failed rows for a CSV.

### Queue Management
```
POST /api/leadqueue/{csvDataId}/pause    # Pause processing
POST /api/leadqueue/{csvDataId}/resume   # Resume processing
DELETE /api/leadqueue/{csvDataId}        # Delete from queue
```

### Scheduler Control
```
GET /api/leadqueue/scheduler/status      # Get scheduler status
POST /api/leadqueue/scheduler/start      # Start scheduler
POST /api/leadqueue/scheduler/stop       # Stop scheduler
```

## Using the System

### 1. Upload CSV File
```javascript
const formData = new FormData();
formData.append('csvFile', file);
formData.append('processType', 'prospect');
formData.append('source', 'manual_upload');

fetch('/api/leadqueue/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('CSV queued:', data.csvDataId);
});
```

### 2. Monitor Processing
```javascript
// Get queue status
fetch('/api/leadqueue?status=PROCESSING')
.then(response => response.json())
.then(data => {
  console.log('Processing CSVs:', data.data);
});

// Get specific CSV status
fetch(`/api/leadqueue/${csvDataId}/status`)
.then(response => response.json())
.then(data => {
  console.log('Progress:', data.data.progressPercentage + '%');
  console.log('Errors:', data.data.errorRows);
});
```

### 3. Start Scheduler (if not auto-started)
```javascript
fetch('/api/leadqueue/scheduler/start', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  console.log('Scheduler started');
});
```

## Environment Variables

Set these environment variables for configuration:

```bash
# Auto-start scheduler on server startup
AUTO_START_CSV_SCHEDULER=true

# Database configuration (existing)
DATABASE_URL=postgresql://...
```

## Scheduler Service

The scheduler automatically:
- Checks for pending CSV files every 30 seconds
- Processes one CSV at a time
- Handles errors and retries
- Updates progress and status
- Supports manual intervention

### Scheduler Control
```javascript
const csvScheduler = require('./services/csvProcessor/scheduler');

// Start scheduler
csvScheduler.start();

// Stop scheduler
csvScheduler.stop();

// Check status
const status = csvScheduler.getStatus();
console.log('Running:', status.isRunning);
console.log('Current job:', status.currentJob);

// Process specific CSV manually
const result = await csvScheduler.processSpecificCSV(csvDataId);
```

## Error Handling

The system provides comprehensive error handling:

1. **File-level errors** - Tracked in `csvData.error_message`
2. **Row-level errors** - Tracked in `csvDataLog.error_message`
3. **Automatic retries** - Configurable retry limits
4. **Error reporting** - Detailed error information in API responses

### Common Error Scenarios
- Missing required fields
- Invalid data formats
- Database constraint violations
- File system errors
- Processing timeouts

## Migration from Existing System

The new system is backward compatible:

1. **Existing endpoints** - All current CSV endpoints still work
2. **Gradual adoption** - Use new queue system for new uploads
3. **Data preservation** - Existing data remains unchanged

### Migration Steps
1. Deploy new system (already backward compatible)
2. Start using `/api/leadqueue/upload` for new CSV uploads
3. Monitor processing via new queue endpoints
4. Gradually phase out direct processing endpoints

## Performance Considerations

- **Batch processing** - Processes rows in configurable batches
- **Memory optimization** - Streams large files instead of loading entirely
- **Database indexing** - Optimized queries for queue operations
- **Concurrent safety** - Prevents multiple processors on same CSV

## Monitoring and Maintenance

### Key Metrics to Monitor
- Queue size (`PENDING` status count)
- Processing rate (rows/minute)
- Error rates (failed rows percentage)
- Scheduler uptime

### Maintenance Tasks
- Clean up old completed CSVs
- Monitor disk space for uploaded files
- Review error patterns for system improvements
- Update retry limits based on error analysis

## Troubleshooting

### Scheduler Not Processing
1. Check scheduler status: `GET /api/leadqueue/scheduler/status`
2. Start if stopped: `POST /api/leadqueue/scheduler/start`
3. Check server logs for errors

### High Error Rates
1. Check error details: `GET /api/leadqueue/{csvDataId}/status`
2. Review error patterns in `csvDataLog` table
3. Retry failed rows: `POST /api/leadqueue/{csvDataId}/retry`

### Performance Issues
1. Monitor queue size and processing rate
2. Check database performance
3. Consider increasing batch sizes
4. Review scheduler interval settings

## Example Usage Scenarios

### Scenario 1: Bulk Prospect Upload
```javascript
// Upload large prospect CSV file
const response = await uploadCsv(file, 'prospect', 'bulk_import_2024');

// Monitor progress
setInterval(async () => {
  const status = await getCsvStatus(response.csvDataId);
  console.log(`Progress: ${status.progressPercentage}%`);
  
  if (status.isCompleted) {
    console.log('Upload completed!');
    console.log(`Processed: ${status.processedRows}, Errors: ${status.errorRows}`);
  }
}, 5000);
```

### Scenario 2: Error Recovery
```javascript
// Check for failed CSVs
const queue = await getQueue({ status: 'FAILED' });

for (const csv of queue.data) {
  console.log(`Retrying failed CSV: ${csv.file_name}`);
  await retryFailedRows(csv.id);
}
```

### Scenario 3: Manual Processing
```javascript
// Upload and immediately process
const response = await uploadCsv(file, 'amazon_seller');
await processSpecificCsv(response.csvDataId);
```

This system provides a robust, scalable solution for CSV processing with comprehensive tracking, error handling, and queue management capabilities.
