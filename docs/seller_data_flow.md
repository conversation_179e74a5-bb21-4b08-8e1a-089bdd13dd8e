# Seller Data Flow Documentation

This document outlines the new data flow for inserting and updating seller data in the SellerBot application after the schema changes implemented for issue #163.

## Schema Overview

The new schema introduces two main tables:

1. **AmazonSeller**: Replaces the functionality of the `Company` table, with the key difference that each seller is uniquely identified by the combination of `amazon_seller_id` and `marketplace` (country code). The schema has been updated to use appropriate data types (Float, Int, Boolean) for numeric and boolean fields.

2. **SellerGroup**: Groups related sellers together, allowing for better organization of sellers that represent the same company across different marketplaces. This table also has a direct relationship with Prospects.

3. **Prospect**: Updated to have a relationship with SellerGroup, allowing prospects to be associated with a seller group rather than just a specific company.

## Data Constraints

The following constraints are enforced in the new schema:

1. Each seller must have a unique combination of `amazon_seller_id` and `marketplace`.
2. The `marketplace` field is required and represents the country/marketplace where the seller operates (e.g., "US", "CA", "UK").
3. Sellers with the same domain and "Final Correct" website status should belong to the same seller group.
4. Prospects can be associated with a seller group, allowing them to be linked to multiple sellers across different marketplaces.

## Insert Flow

When inserting new seller data:

1. Check if a seller with the same `amazon_seller_id` and `marketplace` already exists.
   - If yes, follow the update flow.
   - If no, continue with the insert flow.

2. Check if the seller has a domain with "Final Correct" status.
   - If yes, check if a seller group with this domain exists.
     - If a seller group exists, associate the new seller with this group.
     - If no seller group exists, create a new seller group and associate the seller with it.
   - If no domain or not "Final Correct", create the seller without a group association.

3. Insert the seller data into the `AmazonSeller` table with the appropriate seller group association.

4. If there are prospects associated with this seller, update them to link to the seller group as well.

## Update Flow

When updating existing seller data:

1. Locate the seller by `amazon_seller_id` and `marketplace`.

2. If the update includes a domain change and the website status is "Final Correct":
   - Check if the new domain is associated with an existing seller group.
   - If yes, update the seller's group association to the found group.
   - If no, create a new seller group for this domain and update the seller's association.

3. If the update includes a website status change to "Final Correct" and the seller has a domain:
   - Follow the same domain-based group association logic as above.

4. Update the seller record with the new data.

5. If the seller's group association has changed, update any associated prospects to link to the new group.

## Group Management

Seller groups are managed automatically based on domain relationships:

1. When a seller's domain is set to "Final Correct", it is automatically associated with the appropriate group.

2. If multiple sellers have the same domain with "Final Correct" status, they will be grouped together regardless of their `amazon_seller_id` or `marketplace`.

3. If a seller's domain changes or website status changes from "Final Correct", its group association may be updated accordingly.

4. Prospects are linked to seller groups rather than individual sellers, allowing them to be associated with multiple sellers across different marketplaces.

## Migration

A migration script (`migrateCompanyToSeller.js`) has been provided to:

1. Transfer existing company data to the new `AmazonSeller` table with appropriate type conversions.
2. Create appropriate seller groups based on domains.
3. Establish the correct relationships between sellers and groups.
4. Update prospects to link to seller groups.

## API Changes

The API endpoints for inserting and updating seller data have been updated to handle the new schema:

- `/api/lead/insert/amazon_seller` - Inserts into the `AmazonSeller` table
- `/api/lead/update/amazon_seller` - Updates the `AmazonSeller` table

The existing company endpoints will continue to work for backward compatibility:

- `/api/lead/insert/company` - Now inserts into the `AmazonSeller` table
- `/api/lead/update/company` - Now updates the `AmazonSeller` table

## Data Validation

Validation rules have been updated to:

1. Ensure the `marketplace` field is provided and valid.
2. Validate domain uniqueness within the context of a marketplace.
3. Prevent domain conflicts across different seller groups.
4. Convert string values to appropriate types (Float, Int, Boolean) for numeric and boolean fields.

## Querying Recommendations

When querying seller data:

1. To get all sellers across all marketplaces, query the `AmazonSeller` table directly.
2. To get all sellers for a specific marketplace, filter by the `marketplace` field.
3. To get all sellers belonging to the same company (group), query through the `SellerGroup` relationship.
4. To find a specific seller, use both `amazon_seller_id` and `marketplace` for the most precise results.
5. To find all prospects associated with a seller group, query through the `SellerGroup.prospect` relationship.

## Data Type Conversions

The following data type conversions are applied when migrating or inserting data:

1. Numeric string fields are converted to appropriate numeric types:
   - `estimate_sales`, `percent_fba`, `mom_growth` -> Float
   - `number_winning_brands`, `number_asins`, `number_top_asins`, `number_brands_1000`, `number_reviews_lifetime`, `number_reviews_30days` -> Int

2. Address fields are mapped to more specific fields:
   - `company_address` -> `street`
   - `company_location` -> `city`
   - `company_pincode` -> `zipcode`

3. Boolean fields are properly typed:
   - `is_suspended` -> Boolean
