/**
 * Swagger Documentation
 * 
 * This folder contains Swagger JSDoc documentation for the API endpoints.
 * Each file corresponds to a specific set of endpoints or functionality.
 * 
 * The documentation is automatically loaded by the Swagger configuration
 * in the root swagger.js file.
 * 
 * To add new documentation:
 * 1. Create a new .js file in this folder
 * 2. Add JSDoc comments with @swagger annotations
 * 3. The documentation will be automatically included in the Swagger UI
 * 
 * Example:
 * 
 * ```javascript
 * /**
 *  * @swagger
 *  * /api/example:
 *  *   get:
 *  *     summary: Example endpoint
 *  *     description: This is an example endpoint
 *  *     tags: [Example]
 *  *     responses:
 *  *       200:
 *  *         description: Success
 *  *\/
 * ```
 */
