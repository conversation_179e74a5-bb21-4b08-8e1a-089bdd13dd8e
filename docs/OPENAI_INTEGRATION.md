# AI Website Finding Integration

This document describes the AI-based website finding functionality that has been integrated into SellerBot.

## Overview

The AI integration allows SellerBot to find company websites for Amazon sellers using various AI models with web search capabilities. Currently supports OpenAI's GPT-4o-mini model, with architecture designed to support multiple AI providers in the future (<PERSON>, <PERSON>, etc.). This provides an alternative to SERP-based website discovery and can often find websites that traditional search engines might miss.

## Features

- **Intelligent Website Discovery**: Uses OpenAI's reasoning capabilities to find relevant company websites
- **Web Search Integration**: Leverages OpenAI's web search tool for real-time information
- **Cost Tracking**: Detailed tracking of token usage and costs per request
- **Batch Processing**: Efficient processing of multiple sellers in batches
- **Custom Prompts**: Ability to customize the AI prompt for specific use cases
- **Caching**: Intelligent caching to avoid duplicate requests and reduce costs
- **Progress Tracking**: Real-time progress monitoring for large jobs
- **CSV Export**: Export results in CSV format with detailed cost information

## Input Requirements

The system expects CSV files with the following columns:

- **Seller Name** (required): The name of the Amazon seller
- **Business Name** (optional): The business/company name if different from seller name
- **Address** (optional): Physical address of the business
- **Seller ID** (optional): Amazon seller identifier
- **Seller URL** (required): Amazon seller profile URL
- **Search Priority** (optional): Priority level (SP1, SP2, etc.)

## Output Format

The system generates CSV files with the following columns:

- All input columns (preserved)
- **Website 1, 2, 3**: Up to 3 websites found by the AI
- **Success/Failure**: Whether the search was successful
- **Input Tokens**: Number of input tokens used
- **Output Tokens**: Number of output tokens generated
- **Total Cost**: Cost in USD for the request
- **Reasoning**: AI's explanation of the findings

## API Endpoints

### Create AI Job
```
POST /api/ai/lead-generate
```
Upload a CSV file and optionally provide a custom prompt and AI model to create an AI processing job.

**Parameters:**
- `csvFile` (file): CSV file with seller data
- `customPrompt` (string, optional): Custom prompt for AI model
- `model` (string, optional): AI model to use (default: "openai")

### Get Job Progress
```
GET /api/ai/job/{jobId}/progress
```
Get real-time progress information for a specific job.

### Download Results
```
GET /api/ai/job/{jobId}/results
```
Download the results as a CSV file.

### Get Statistics
```
GET /api/ai/stats?model={model}
```
Get overall usage statistics including total costs and success rates. Filter by model (optional).

### Get Default Prompt
```
GET /api/ai/default-prompt?model={model}
```
Retrieve the default prompt template for a specific AI model.

### Manual Processing
```
POST /api/ai/job/{jobId}/process
```
Manually trigger processing of a specific job.

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
OPENAI_API_KEY=your_openai_api_key_here
```

### Database Migration

Run the following to update your database schema:

```bash
npx prisma migrate dev
```

## Usage Examples

### 1. Basic CSV Upload

```javascript
const formData = new FormData();
formData.append('csvFile', file);
formData.append('model', 'openai'); // Optional, defaults to 'openai'

const response = await fetch('/api/ai/lead-generate', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token'
  },
  body: formData
});

const result = await response.json();
console.log('Job ID:', result.jobId);
console.log('Model:', result.model);
```

### 2. Custom Prompt with Model Selection

```javascript
const formData = new FormData();
formData.append('csvFile', file);
formData.append('model', 'openai'); // Future: 'claude', 'gemini'
formData.append('customPrompt', `
Find the official website for this company:
- Company: {{sellerName}}
- Business: {{businessName}}
- Location: {{address}}

Return JSON: {"websites": ["site.com"], "success": true, "reasoning": "explanation"}
`);

const response = await fetch('/api/ai/lead-generate', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token'
  },
  body: formData
});
```

### 3. Monitor Progress

```javascript
const jobId = 123;
const response = await fetch(`/api/ai/job/${jobId}/progress`, {
  headers: {
    'Authorization': 'Bearer your_token'
  }
});

const progress = await response.json();
console.log(`Progress: ${progress.progress}%`);
console.log(`Cost so far: $${progress.totalCost}`);
console.log(`Model: ${progress.model}`);
```

## Cost Management

### Pricing (GPT-4o-mini)
- **Input tokens**: $0.25 per million tokens
- **Output tokens**: $0.75 per million tokens

### Cost Optimization Tips
1. Use specific, concise prompts to reduce token usage
2. Enable caching to avoid duplicate requests
3. Monitor costs regularly using the statistics endpoint
4. Consider batch processing for large datasets

## Worker Configuration

### Starting Workers

```bash
# Start OpenAI worker
npm run start-openai-worker

# Or use PM2 ecosystem
pm2 start ecosystem.config.js
```

### Worker Settings
- **Batch Size**: 10 leads per batch
- **Rate Limiting**: 5 concurrent requests, 100ms minimum between requests
- **Processing Interval**: Every 15 seconds
- **Memory Limit**: 2GB

## Monitoring and Debugging

### Logs
Workers log their activity to the console. Key log messages include:
- Job processing start/completion
- Batch processing results
- Error messages and stack traces
- Cost and token usage information

### Database Tables
- `OpenAI_Cache`: Stores cached responses to avoid duplicate requests
- `OpenAI_Usage`: Detailed usage tracking per lead
- `Lead`: Updated with OpenAI results
- `LeadJob`: Job status and metadata

### Common Issues

1. **API Key Issues**: Ensure `OPENAI_API_KEY` is set correctly
2. **Rate Limiting**: Adjust `minTime` and `maxConcurrent` in the rate limiter
3. **Memory Issues**: Increase worker memory limits if processing large batches
4. **Parsing Errors**: AI responses that don't match expected JSON format

## Testing

Run the integration test suite:

```bash
node test/openai-integration-test.js
```

This will test:
- Basic OpenAI requests
- CSV processing
- Worker functionality
- Statistics generation
- Custom prompts

## Security Considerations

1. **API Key Protection**: Store OpenAI API key securely in environment variables
2. **Rate Limiting**: Implement proper rate limiting to avoid API abuse
3. **Input Validation**: Validate all CSV inputs before processing
4. **Cost Monitoring**: Set up alerts for unusual cost spikes
5. **Access Control**: Ensure only authorized users can create OpenAI jobs

## Future Enhancements

- Integration with multiple AI providers
- Advanced prompt templates
- Real-time cost alerts
- Automated quality scoring of found websites
- Integration with website validation services
