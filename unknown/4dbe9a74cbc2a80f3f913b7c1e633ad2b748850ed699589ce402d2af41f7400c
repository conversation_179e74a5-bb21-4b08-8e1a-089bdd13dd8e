/**
 * Worker that runs hourly to check incomplete jobs and auto-complete them if needed
 * Also generates export sheets for completed jobs
 */

const prisma = require("../../../database/prisma/getPrismaClient");
const { generateJobExport } = require("../../../utils/leadExportUtils");
const cron = require('node-cron');
const { sellerbotxSlackNotification } = require("../../../utils/sellerbotSlackNotification");
const { JobStatus, LeadStatus } = require("@prisma/client");


// Configuration constants
const CONFIG = {
  BATCH_SIZE: 100, // Number of jobs to fetch per batch
  CONCURRENT_JOBS: 5, // Number of jobs to process concurrently
  MAX_RETRIES: 3, // Maximum number of retries for failed operations
  RETRY_DELAY_MS: 5000, // Base delay between retries in milliseconds
  JOB_TIMEOUT_MS: 1200000, // Job timeout in milliseconds (20 minutes)
  MAX_AGE_DAYS: 14, // Maximum age of jobs to process in days
  MIN_RETRY_DELAY_MS: 1000, // Minimum retry delay
  MAX_RETRY_DELAY_MS: 30000, // Maximum retry delay
  WORKER_INTERVAL: 3600000, // 1 hour in milliseconds
  CRON_EXPRESSION: "0 * * * *", // Runs every hour
};

// Track if worker is already running
let isWorkerRunning = false;

/**
 * Implements exponential backoff with jitter for retries
 * @param {number} retryCount - Current retry attempt
 * @returns {number} - Delay in milliseconds
 */
const getRetryDelay = (retryCount) => {
  const baseDelay = Math.min(
    CONFIG.MAX_RETRY_DELAY_MS,
    Math.max(
      CONFIG.MIN_RETRY_DELAY_MS,
      Math.pow(2, retryCount) * CONFIG.RETRY_DELAY_MS
    )
  );
  // Add jitter to prevent thundering herd
  return baseDelay + Math.random() * 1000;
};

/**
 * Creates a promise that rejects after a specified timeout
 * @param {Promise} promise - Promise to wrap with timeout
 * @param {number} timeoutMs - Timeout in milliseconds
 * @param {string} operationName - Name of the operation for error message
 * @returns {Promise}
 */
const withTimeout = async (promise, timeoutMs, operationName) => {
  const timeoutPromise = new Promise((_, reject) => 
    setTimeout(() => reject(new Error(`${operationName} timed out after ${timeoutMs}ms`)), timeoutMs)
  );
  return Promise.race([promise, timeoutPromise]);
};

/**
 * Checks if a job is ready to be marked as completed
 * @param {Number} jobId - The job ID to check
 * @returns {Promise<Boolean>} - True if the job should be completed
 */
const isJobReadyToComplete = async (jobId) => {
  try {
    const pendingLeads = await withTimeout(
      prisma.lead.count({
        where: {
          jobId,
          status: {
            notIn: [LeadStatus.completed, LeadStatus.srp_failed, LeadStatus.failed]
          }
        }
      }),
      CONFIG.JOB_TIMEOUT_MS,
      `Job completion check ${jobId}`
    );

    return pendingLeads === 0;
  } catch (error) {
    console.error(`Error checking completion status for job ${jobId}:`, error);
    throw error;
  }
};

/**
 * Updates job status with retry mechanism
 */
const updateJobWithRetry = async (job, updateData) => {
  let lastError = null;
  
  for (let retryCount = 0; retryCount < CONFIG.MAX_RETRIES; retryCount++) {
    try {
      return await prisma.leadJob.update({
        where: { id: job.id },
        data: updateData
      });
    } catch (error) {
      lastError = error;
      const delay = getRetryDelay(retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

/**
 * Marks a job as completed and generates export
 */
const completeJob = async (job, sendSheet = false) => {
  try {
    // 1. Mark as completed in DB
    const completedAt = new Date().toISOString();
    await updateJobWithRetry(job, {
      status: JobStatus.completed,
      resultJson: {
        exportCompleted: false,
        completedAt,
      }
    });

    // 2. Send first Slack notification (job marked as completed)
    await sellerbotxSlackNotification(
      job,
      { completedAt },
      null,
      true, // success
      null
    );

    // 3. Call Google Sheets export
    const exportResult = await withTimeout(
      generateJobExport([job.id], sendSheet),
      CONFIG.JOB_TIMEOUT_MS,
      `Export generation for job ${job.id}`
    );

    // 4. Gather analytics
    const statusCounts = await prisma.lead.groupBy({
      by: ['status'],
      where: { jobId: job.id },
      _count: true
    });
    const statusMap = statusCounts.reduce((acc, item) => {
      acc[item.status] = item._count;
      return acc;
    }, {});
    const totalLeads = exportResult.stats.totalLeads;
    const completedLeads = statusMap['completed'] || 0;
    const completionPercentage = totalLeads > 0 ? Math.round((completedLeads / totalLeads) * 100) : 100;

    // 5. Update job with analytics
    await updateJobWithRetry(job, {
      resultJson: {
        exportCompleted: true,
        completedAt,
        googleSheet: exportResult.googleSheet,
        stats: exportResult.stats,
        statusMap,
        totalLeads,
        completionPercentage
      }
    });

    // 6. Send analytics to Slack (second ping)
    const metrics = {
      totalRecords: totalLeads,
      matchedRecords: completedLeads,
      completionPercentage,
      totalUrls: exportResult.stats.totalUrls,
      validatedUrls: exportResult.stats.validatedUrls,
      confidenceFilteredUrls: exportResult.stats.confidenceFilteredUrls,
      ...statusMap
    };
    await sellerbotxSlackNotification(
      job,
      metrics,
      exportResult?.googleSheet?.url || null,
      true, // success
      null
    );

    return true;
  } catch (error) {
    console.error(`Failed to complete job ${job.id}:`, error);

    // Mark job as failed
    return updateJobWithRetry(job, {
      status: JobStatus.failed,
      resultJson: {
        exportCompleted: false,
        completedAt: new Date().toISOString(),
        error: error.message
      }
    });
  }
};

/**
 * Gets jobs that need processing with efficient querying
 */
const getRecentPendingJobs = async () => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - CONFIG.MAX_AGE_DAYS);
    console.log("Cutoff Date", cutoffDate);
    
    return prisma.leadJob.findMany({
      where: {
        AND: [
          {
            status: {
              notIn: [JobStatus.completed, JobStatus.in_progress]
            }
          },
          {
            createdAt: {
              gte: cutoffDate
            }
          }
        ]
      },
      orderBy: [
        { createdAt: 'desc' }
      ],
      take: CONFIG.BATCH_SIZE,
      select: {
        id: true,
        name: true,
        status: true,
        resultJson: true
      }
    });
  } catch (error) {
    console.error("Error fetching pending jobs:", error);
    throw error;
  }
};

/**
 * Process jobs in batches with controlled concurrency
 */
const processBatch = async (jobs, sendSheet) => {
  console.log('Starting processBatch:');
  console.log('\tTotal jobs to process:', jobs.length);
  
  const chunks = [];
  for (let i = 0; i < jobs.length; i += CONFIG.CONCURRENT_JOBS) {
    chunks.push(jobs.slice(i, i + CONFIG.CONCURRENT_JOBS));
  }
  console.log('\tDivided into', chunks.length, 'chunks');

  for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
    const chunk = chunks[chunkIndex];
    console.log(`\tProcessing chunk ${chunkIndex + 1}/${chunks.length}`);

    for (const job of chunk) {
      console.log(`\t\tProcessing job ${job.id}:`);
      try {
        console.log(`\t\tChecking completion status for job ${job.id}`);
        const shouldComplete = await isJobReadyToComplete(job.id);
        
        if (shouldComplete) {
          console.log(`\t\tJob ${job.id} is ready for completion`);
          console.log(`\t\tStarting completion process for job ${job.id}`);
          await completeJob(job, sendSheet);
          console.log(`\t\tSuccessfully completed job ${job.id}`);
        } else {
          console.log(`\t\tJob ${job.id} is not ready for completion yet`);
        }
      } catch (error) {
        console.error(`\t\tError processing job ${job.id}:`, error);
      }
      console.log(`\t\tFinished processing job ${job.id}`);
    }
    console.log(`\t\tFinished processing chunk ${chunkIndex + 1}`);
  }
  console.log('Completed processBatch');
};

/**
 * Main worker function that runs the job update process
 */
const runJobUpdateWorker = async () => {
  // Prevent multiple concurrent runs
  console.log("JOB STATUS", JobStatus);
  console.log("LEAD STATUS", LeadStatus);
  if (isWorkerRunning) {
    console.log('Worker is already running, skipping this cycle');
    return;
  }

  try {
    isWorkerRunning = true;
    console.log('Starting job update worker:', new Date().toISOString());

    const pendingJobs = await getRecentPendingJobs();
    
    if (pendingJobs.length === 0) {
      console.log('No pending jobs found');
      return;
    }

    console.log(`Processing ${pendingJobs.length} pending jobs`);
    const sendSheet = process.env.SEND_SHEET === "true";
    await processBatch(pendingJobs, sendSheet);
    
    console.log('Job update worker completed:', new Date().toISOString());
  } catch (error) {
    console.error("Critical error in job update worker:", error);
    
    // If there's a Prisma error, we might want to reconnect
    if (error.code?.startsWith('P')) {
      try {
        await prisma.$disconnect();
        await prisma.$connect();
      } catch (reconnectError) {
        console.error("Failed to reconnect to database:", reconnectError);
      }
    }
  } finally {
    isWorkerRunning = false;
  }
};

/**
 * Start the worker using node-cron (recommended for production)
 * Runs every hour at minute 0
 */
const startWorkerWithCron = () => {
  // Schedule the job to run every hour at minute 0
  const job = cron.schedule(CONFIG.CRON_EXPRESSION, async () => {
    try {
      await runJobUpdateWorker();
    } catch (error) {
      console.error('Cron job execution failed:', error);
    }
  });

  // Handle process termination
  process.on('SIGTERM', () => {
    job.stop();
    prisma.$disconnect();
  });

  console.log('Job update worker scheduled with cron');
  return job;
};

/**
 * Start the worker using setInterval (alternative approach)
 * Note: May drift over time due to execution time and system load
 */
const startWorkerWithInterval = () => {
  // Run immediately on start
  runJobUpdateWorker().catch(error => {
    console.error('Initial worker run failed:', error);
  });
  
  // Then schedule for every hour
  const interval = setInterval(async () => {
    try {
      await runJobUpdateWorker();
    } catch (error) {
      console.error('Interval worker execution failed:', error);
    }
  }, CONFIG.WORKER_INTERVAL);

  // Handle process termination
  process.on('SIGTERM', () => {
    clearInterval(interval);
    prisma.$disconnect();
  });
  
  console.log('Job update worker scheduled with setInterval');
  return interval;
};

if (require.main === module) {
  runJobUpdateWorker();
}

module.exports = {
  runJobUpdateWorker,
  startWorkerWithCron,    // Recommended for production
  startWorkerWithInterval, // Alternative approach
  CONFIG,
  JobStatus
}; 