const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

// Function to read CSV file and return data as array of objects
function readCSV(filePath) {
    return new Promise((resolve, reject) => {
        const results = [];
        fs.createReadStream(filePath)
            .pipe(csv())
            .on('data', (data) => results.push(data))
            .on('end', () => resolve(results))
            .on('error', reject);
    });
}

// Function to get all CSV files from data folder
function getDataFolderCSVs() {
    const dataFolderPath = './data';
    const files = fs.readdirSync(dataFolderPath);
    return files
        .filter(file => file.endsWith('.csv'))
        .map(file => path.join(dataFolderPath, file));
}

// Main function
async function findMissingSellers() {
    try {
        console.log('Starting analysis...\n');

        // Read Final processed list
        console.log('Reading Final processed list...');
        const finalProcessedData = await readCSV('./Final 23May prcessed List.csv');
        
        // Create a Set of Amazon seller IDs from final processed list for fast lookup
        const finalProcessedSellerIds = new Set(
            finalProcessedData.map(row => row.amazon_seller_id?.trim()).filter(Boolean)
        );
        
        console.log(`Final processed list contains ${finalProcessedSellerIds.size} unique Amazon seller IDs\n`);

        // Read all data folder CSV files
        const dataFiles = getDataFolderCSVs();
        console.log(`Found ${dataFiles.length} CSV files in data folder:`);
        dataFiles.forEach(file => console.log(`  - ${path.basename(file)}`));
        console.log();

        // Collect all rows from data folder CSVs with their Amazon seller IDs
        const allDataRows = [];
        const seenSellerIds = new Set(); // To avoid duplicates

        for (const file of dataFiles) {
            console.log(`Reading ${path.basename(file)}...`);
            const data = await readCSV(file);
            
            for (const row of data) {
                const sellerId = row.amazon_seller_id?.trim();
                if (sellerId && !seenSellerIds.has(sellerId)) {
                    allDataRows.push(row);
                    seenSellerIds.add(sellerId);
                }
            }
        }

        console.log(`\nTotal unique Amazon seller IDs from data folder: ${seenSellerIds.size}`);

        // Find missing seller IDs (present in data folder but not in final processed list)
        const missingRows = allDataRows.filter(row => {
            const sellerId = row.amazon_seller_id?.trim();
            return sellerId && !finalProcessedSellerIds.has(sellerId);
        });

        console.log(`Missing seller IDs found: ${missingRows.length}\n`);

        if (missingRows.length === 0) {
            console.log('No missing seller IDs found. All data folder entries are present in the final processed list.');
            return;
        }

        // Prepare data for output with only required headers
        const requiredHeaders = [
            'company_name',
            'amazon_seller_id', 
            'website',
            'website_status',
            'status',
            'jeff_search_priority'
        ];

        const outputData = missingRows.map(row => {
            const outputRow = {};
            requiredHeaders.forEach(header => {
                outputRow[header] = row[header] || '';
            });
            return outputRow;
        });

        // Write to new CSV file
        const outputFileName = 'missing_amazon_sellers.csv';
        const csvWriter = createCsvWriter({
            path: outputFileName,
            header: requiredHeaders.map(header => ({ id: header, title: header }))
        });

        await csvWriter.writeRecords(outputData);
        
        console.log(`✅ Successfully created ${outputFileName} with ${outputData.length} missing seller records`);
        console.log('\nSample of missing sellers:');
        console.log('----------------------------');
        
        // Show first 5 missing entries as sample
        outputData.slice(0, 5).forEach((row, index) => {
            console.log(`${index + 1}. ${row.company_name} (${row.amazon_seller_id})`);
        });
        
        if (outputData.length > 5) {
            console.log(`... and ${outputData.length - 5} more entries`);
        }

    } catch (error) {
        console.error('Error:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the program
findMissingSellers(); 