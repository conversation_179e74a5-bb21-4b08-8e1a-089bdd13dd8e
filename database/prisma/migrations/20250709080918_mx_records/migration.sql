-- AlterTable
ALTER TABLE "AmazonProspect" ADD COLUMN     "emailProvider" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "lastProcessAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "mxRecords" JSONB NOT NULL DEFAULT '{}',
ADD COLUMN     "nextProcessAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "Prospect" ADD COLUMN     "emailProvider" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "lastProcessAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "mxRecords" JSONB NOT NULL DEFAULT '{}',
ADD COLUMN     "nextProcessAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- CreateIndex
CREATE INDEX "AmazonProspect_nextProcessAt_idx" ON "AmazonProspect"("nextProcessAt");

-- <PERSON><PERSON>Index
CREATE INDEX "Prospect_nextProcessAt_idx" ON "Prospect"("nextProcessAt");
