/*
  Warnings:

  - A unique constraint covering the columns `[messageId,time]` on the table `Email` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `messageId` to the `Email` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Email" ADD COLUMN     "messageId" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Email_messageId_time_key" ON "Email"("messageId", "time");
