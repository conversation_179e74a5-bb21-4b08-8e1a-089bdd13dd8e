-- CreateTable
CREATE TABLE "ReviewOutputData" (
    "id" SERIAL NOT NULL,
    "revId" INTEGER NOT NULL,
    "reviewJobId" INTEGER NOT NULL,
    "status" "ReviewStatus" NOT NULL DEFAULT 'PENDING',
    "reviewUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReviewOutputData_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ReviewOutputData" ADD CONSTRAINT "ReviewOutputData_revId_fkey" FOREIGN KEY ("revId") REFERENCES "Review"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReviewOutputData" ADD CONSTRAINT "ReviewOutputData_reviewJobId_fkey" FOREIGN KEY ("reviewJobId") REFERENCES "ReviewJob"("id") ON DELETE CASCADE ON UPDATE CASCADE;
