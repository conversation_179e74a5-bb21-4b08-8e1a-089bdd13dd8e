-- CreateE<PERSON>
CREATE TYPE "CsvProcessStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PAUSED');

-- CreateTable
CREATE TABLE "CsvData" (
    "id" SERIAL NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "file_type" TEXT NOT NULL,
    "process_type" TEXT NOT NULL,
    "original_headers" JSONB NOT NULL DEFAULT '[]',
    "total_rows" INTEGER NOT NULL DEFAULT 0,
    "options" JSONB NOT NULL DEFAULT '{}',
    "header_mappings" JSONB NOT NULL DEFAULT '{}',
    "status" "CsvProcessStatus" NOT NULL DEFAULT 'PENDING',
    "processed_until" INTEGER NOT NULL DEFAULT 0,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "error_status" BOOLEAN NOT NULL DEFAULT false,
    "error_message" TEXT,
    "retry_count" INTEGER NOT NULL DEFAULT 0,
    "max_retries" INTEGER NOT NULL DEFAULT 3,
    "last_retry_at" TIMESTAMP(3),
    "source" TEXT,
    "uploaded_by" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CsvData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CsvDataLog" (
    "id" SERIAL NOT NULL,
    "data_id" INTEGER NOT NULL,
    "upload_log_id" TEXT,
    "row_number" INTEGER NOT NULL,
    "headers_map" JSONB NOT NULL DEFAULT '{}',
    "processed" BOOLEAN NOT NULL DEFAULT false,
    "error_status" BOOLEAN NOT NULL DEFAULT false,
    "error_message" TEXT,
    "retry_count" INTEGER NOT NULL DEFAULT 0,
    "key1" TEXT,
    "key2" TEXT,
    "key3" TEXT,
    "key4" TEXT,
    "key5" TEXT,
    "key6" TEXT,
    "key7" TEXT,
    "key8" TEXT,
    "key9" TEXT,
    "key10" TEXT,
    "key11" TEXT,
    "key12" TEXT,
    "key13" TEXT,
    "key14" TEXT,
    "key15" TEXT,
    "key16" TEXT,
    "key17" TEXT,
    "key18" TEXT,
    "key19" TEXT,
    "key20" TEXT,
    "key21" TEXT,
    "key22" TEXT,
    "key23" TEXT,
    "key24" TEXT,
    "key25" TEXT,
    "key26" TEXT,
    "key27" TEXT,
    "key28" TEXT,
    "key29" TEXT,
    "key30" TEXT,
    "key31" TEXT,
    "key32" TEXT,
    "key33" TEXT,
    "key34" TEXT,
    "key35" TEXT,
    "key36" TEXT,
    "key37" TEXT,
    "key38" TEXT,
    "key39" TEXT,
    "key40" TEXT,
    "key41" TEXT,
    "key42" TEXT,
    "key43" TEXT,
    "key44" TEXT,
    "key45" TEXT,
    "key46" TEXT,
    "key47" TEXT,
    "key48" TEXT,
    "key49" TEXT,
    "key50" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CsvDataLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CsvData_status_idx" ON "CsvData"("status");

-- CreateIndex
CREATE INDEX "CsvData_process_type_idx" ON "CsvData"("process_type");

-- CreateIndex
CREATE INDEX "CsvData_is_completed_idx" ON "CsvData"("is_completed");

-- CreateIndex
CREATE INDEX "CsvDataLog_data_id_idx" ON "CsvDataLog"("data_id");

-- CreateIndex
CREATE INDEX "CsvDataLog_processed_idx" ON "CsvDataLog"("processed");

-- CreateIndex
CREATE INDEX "CsvDataLog_error_status_idx" ON "CsvDataLog"("error_status");

-- CreateIndex
CREATE INDEX "CsvDataLog_row_number_idx" ON "CsvDataLog"("row_number");

-- AddForeignKey
ALTER TABLE "CsvDataLog" ADD CONSTRAINT "CsvDataLog_data_id_fkey" FOREIGN KEY ("data_id") REFERENCES "CsvData"("id") ON DELETE CASCADE ON UPDATE CASCADE;
