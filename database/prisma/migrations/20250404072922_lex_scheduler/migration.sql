/*
  Warnings:

  - You are about to drop the column `error` on the `ReviewJob` table. All the data in the column will be lost.
  - You are about to drop the column `filename` on the `ReviewJob` table. All the data in the column will be lost.
  - You are about to drop the column `inputFilePath` on the `ReviewJob` table. All the data in the column will be lost.
  - You are about to drop the column `outputCSV` on the `ReviewJob` table. All the data in the column will be lost.
  - The `status` column on the `ReviewJob` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `name` to the `ReviewJob` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "ReviewJobStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED');

-- CreateEnum
CREATE TYPE "ReviewStatus" AS ENUM ('PENDING', 'PRESENT', 'REMOVED');

-- AlterTable
ALTER TABLE "ReviewJob" DROP COLUMN "error",
DROP COLUMN "filename",
DROP COLUMN "inputFilePath",
DROP COLUMN "outputCSV",
ADD COLUMN     "name" TEXT NOT NULL,
DROP COLUMN "status",
ADD COLUMN     "status" "ReviewJobStatus" NOT NULL DEFAULT 'PENDING';

-- CreateTable
CREATE TABLE "Review" (
    "id" SERIAL NOT NULL,
    "reviewJobId" INTEGER NOT NULL,
    "asin" TEXT NOT NULL,
    "brandName" TEXT NOT NULL,
    "reviewId" TEXT NOT NULL,
    "reviewUrl" TEXT NOT NULL,
    "reviewer" TEXT,
    "reviewerLink" TEXT,
    "inputData" JSONB NOT NULL DEFAULT '{}',
    "status" "ReviewStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Review_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Review_reviewId_key" ON "Review"("reviewId");

-- AddForeignKey
ALTER TABLE "Review" ADD CONSTRAINT "Review_reviewJobId_fkey" FOREIGN KEY ("reviewJobId") REFERENCES "ReviewJob"("id") ON DELETE CASCADE ON UPDATE CASCADE;
