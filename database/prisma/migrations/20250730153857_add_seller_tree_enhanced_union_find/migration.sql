-- CreateEnum
CREATE TYPE "EntityType" AS ENUM ('domain', 'seller_id');

-- AlterTable
ALTER TABLE "SellerGroup" ADD COLUMN     "domain_count" INTEGER DEFAULT 0,
ADD COLUMN     "domains" JSONB NOT NULL DEFAULT '[]',
ADD COLUMN     "rank" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "seller_count" INTEGER DEFAULT 0,
ADD COLUMN     "seller_ids" JSONB NOT NULL DEFAULT '[]';

-- CreateTable
CREATE TABLE "SellerTree" (
    "id" SERIAL NOT NULL,
    "entity_type" "EntityType" NOT NULL,
    "entity_value" TEXT NOT NULL,
    "parent_id" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SellerTree_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SellerTree_entity_type_idx" ON "SellerTree"("entity_type");

-- CreateIndex
CREATE INDEX "SellerTree_entity_value_idx" ON "SellerTree"("entity_value");

-- CreateIndex
CREATE INDEX "SellerTree_parent_id_idx" ON "SellerTree"("parent_id");

-- CreateIndex
CREATE INDEX "SellerTree_entity_type_entity_value_parent_id_idx" ON "SellerTree"("entity_type", "entity_value", "parent_id");

-- CreateIndex
CREATE UNIQUE INDEX "SellerTree_entity_type_entity_value_key" ON "SellerTree"("entity_type", "entity_value");
