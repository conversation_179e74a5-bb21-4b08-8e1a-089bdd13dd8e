-- Create<PERSON>num
CREATE TYPE "InputStatus" AS ENUM ('error', 'success', 'pending');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SubmitStatus" AS ENUM ('error', 'success', 'pending');

-- CreateEnum
CREATE TYPE "JobStatus" AS ENUM ('pending', 'in_progress', 'completed');

-- CreateTable
CREATE TABLE "User" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Job" (
    "id" SERIAL NOT NULL,
    "status" "JobStatus" NOT NULL,
    "name" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Job_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Seller" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "jobId" INTEGER NOT NULL,
    "htmlData" TEXT NOT NULL DEFAULT '',
    "status" "JobStatus" NOT NULL,

    CONSTRAINT "Seller_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OutputData" (
    "id" SERIAL NOT NULL,
    "sellerName" TEXT NOT NULL,
    "sellerId" INTEGER NOT NULL,
    "sellerUrl" TEXT NOT NULL DEFAULT '',
    "inputStatus" "InputStatus" NOT NULL,
    "contactNumber" TEXT NOT NULL DEFAULT '',
    "sellerEmail" TEXT NOT NULL DEFAULT '',
    "sellerAddress" TEXT NOT NULL DEFAULT '',
    "sellerPincode" TEXT NOT NULL DEFAULT '',
    "jobId" INTEGER NOT NULL,
    "scraped_html" TEXT,
    "inputData" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OutputData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Company" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL DEFAULT '',
    "amazon_seller_id" TEXT NOT NULL DEFAULT '',
    "seller_url" TEXT NOT NULL DEFAULT '',
    "primary_category_id" TEXT NOT NULL DEFAULT '',
    "primary_category" TEXT NOT NULL DEFAULT '',
    "primary_sub_category" TEXT NOT NULL DEFAULT '',
    "estimate_sales" TEXT NOT NULL DEFAULT '',
    "percent_fba" TEXT NOT NULL DEFAULT '',
    "number_winning_brands" TEXT NOT NULL DEFAULT '',
    "number_asins" TEXT NOT NULL DEFAULT '',
    "number_top_asins" TEXT NOT NULL DEFAULT '',
    "state" TEXT NOT NULL DEFAULT '',
    "country" TEXT NOT NULL DEFAULT '',
    "business_name" TEXT NOT NULL DEFAULT '',
    "number_brands_1000" TEXT NOT NULL DEFAULT '',
    "mom_growth" TEXT NOT NULL DEFAULT '',
    "mom_growth_count" TEXT NOT NULL DEFAULT '',
    "started_selling_date" TEXT NOT NULL DEFAULT '',
    "smartscout_country" TEXT NOT NULL DEFAULT '',
    "website" TEXT NOT NULL DEFAULT '',
    "domain" TEXT NOT NULL DEFAULT '',
    "website_status" TEXT NOT NULL DEFAULT '',
    "employee_count" TEXT NOT NULL DEFAULT '',
    "company_linkedin" TEXT NOT NULL DEFAULT '',
    "company_twitter" TEXT NOT NULL DEFAULT '',
    "company_fb" TEXT NOT NULL DEFAULT '',
    "company_location" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Company_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Prospect" (
    "prospect_id" SERIAL NOT NULL,
    "company_id" INTEGER NOT NULL,
    "amazon_seller_id" TEXT NOT NULL DEFAULT '',
    "website" TEXT NOT NULL DEFAULT '',
    "domain" TEXT NOT NULL DEFAULT '',
    "person_name" TEXT NOT NULL DEFAULT '',
    "person_linkedin" TEXT NOT NULL DEFAULT '',
    "phone" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',
    "job_title" TEXT NOT NULL DEFAULT '',
    "contact_location" TEXT NOT NULL DEFAULT '',
    "apollo_company_name" TEXT NOT NULL DEFAULT '',
    "apollo_website" TEXT NOT NULL DEFAULT '',
    "source" TEXT NOT NULL DEFAULT '',
    "email_status" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Prospect_pkey" PRIMARY KEY ("prospect_id")
);

-- CreateTable
CREATE TABLE "ClientsProspectsMatching" (
    "match_id" SERIAL NOT NULL,
    "client" TEXT NOT NULL,
    "prospect_id" INTEGER NOT NULL,
    "date_reached_out" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "jeff_output_status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClientsProspectsMatching_pkey" PRIMARY KEY ("match_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Seller_name_key" ON "Seller"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Company_amazon_seller_id_domain_key" ON "Company"("amazon_seller_id", "domain");

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Seller" ADD CONSTRAINT "Seller_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutputData" ADD CONSTRAINT "OutputData_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES "Seller"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutputData" ADD CONSTRAINT "OutputData_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Prospect" ADD CONSTRAINT "Prospect_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientsProspectsMatching" ADD CONSTRAINT "ClientsProspectsMatching_prospect_id_fkey" FOREIGN KEY ("prospect_id") REFERENCES "Prospect"("prospect_id") ON DELETE RESTRICT ON UPDATE CASCADE;
