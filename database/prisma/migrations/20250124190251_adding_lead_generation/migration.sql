-- CreateEnum
CREATE TYPE "LeadStatus" AS ENUM ('pending', 'srp_requested', 'srp_failed', 'srp_scraped', 'processing', 'scoring', 'validating', 'completed', 'failed');

-- AlterEnum
ALTER TYPE "JobStatus" ADD VALUE 'failed';

-- CreateTable
CREATE TABLE "LeadJob" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "inputCsvName" TEXT,
    "inputCsvPath" TEXT,
    "asyncSerp" BOOLEAN NOT NULL DEFAULT false,
    "status" "JobStatus" NOT NULL DEFAULT 'pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LeadJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Lead" (
    "id" SERIAL NOT NULL,
    "jobId" INTEGER NOT NULL,
    "sellerName" TEXT NOT NULL,
    "businessName" TEXT,
    "address" TEXT,
    "sellerUrl" TEXT,
    "screenshotUrl" TEXT,
    "srp_task_id" INTEGER,
    "country" TEXT,
    "searchString" TEXT NOT NULL DEFAULT '',
    "apiResponse" JSONB NOT NULL DEFAULT '{}',
    "status" "LeadStatus" NOT NULL DEFAULT 'pending',
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "processId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Lead_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LeadUrl" (
    "id" SERIAL NOT NULL,
    "leadId" INTEGER NOT NULL,
    "url" TEXT,
    "keywords" JSONB,
    "domain" TEXT NOT NULL,
    "googlePosition" INTEGER,
    "filterPosition" INTEGER,
    "confidence" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "htmlCheckResult" JSONB,
    "snippetCheckResult" JSONB,
    "fuzzyDomainResult" JSONB,
    "organicData" JSONB,
    "textValidation" JSONB DEFAULT '{}',
    "imageValidation" JSONB DEFAULT '{}',
    "addressValidation" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LeadUrl_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Serp_Cache" (
    "hash" TEXT NOT NULL,
    "apiResponse" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Serp_Cache_pkey" PRIMARY KEY ("hash")
);

-- CreateIndex
CREATE INDEX "Lead_status_idx" ON "Lead"("status");

-- CreateIndex
CREATE INDEX "LeadUrl_leadId_idx" ON "LeadUrl"("leadId");

-- CreateIndex
CREATE UNIQUE INDEX "LeadUrl_leadId_url_key" ON "LeadUrl"("leadId", "url");

-- CreateIndex
CREATE UNIQUE INDEX "Serp_Cache_hash_key" ON "Serp_Cache"("hash");

-- AddForeignKey
ALTER TABLE "Lead" ADD CONSTRAINT "Lead_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "LeadJob"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LeadUrl" ADD CONSTRAINT "LeadUrl_leadId_fkey" FOREIGN KEY ("leadId") REFERENCES "Lead"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
