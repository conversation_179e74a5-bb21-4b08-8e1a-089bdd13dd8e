#!/bin/bash

# Database Migration Script: Company/Prospect to AmazonSeller/AmazonProspect
# This script automates the migration process with error handling and logging

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/migration_$(date +%Y%m%d_%H%M%S).log"

# Database Configuration
# Production Database - Set these environment variables or update the defaults
PROD_HOST="${PROD_HOST:-your-production-host.com}"
PROD_USER="${PROD_USER:-your-production-user}"
PROD_PASSWORD="${PROD_PASSWORD:-your-production-password}"
PROD_DB="${PROD_DB:-your-production-database}"

# Local Database - Set these environment variables or update the defaults
LOCAL_HOST="${LOCAL_HOST:-localhost}"
LOCAL_PORT="${LOCAL_PORT:-5432}"
LOCAL_USER="${LOCAL_USER:-your-local-user}"
LOCAL_PASSWORD="${LOCAL_PASSWORD:-your-local-password}"
LOCAL_DB="${LOCAL_DB:-your-local-database}"

# File paths
PROD_DUMP_FILE="$PROJECT_ROOT/selected_tables_prod.dump"
LOCAL_DUMP_FILE="$PROJECT_ROOT/local_tables_dump.dump"
MATERIALIZED_VIEW_FILE="$PROJECT_ROOT/database/sql-files/m-view/amazon_seller_priority_expanded_mv.sql"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $timestamp: $message" | tee -a "$LOG_FILE"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $timestamp: $message" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $timestamp: $message" | tee -a "$LOG_FILE"
            ;;
        "STEP")
            echo -e "${BLUE}[STEP]${NC} $timestamp: $message" | tee -a "$LOG_FILE"
            ;;
    esac
}

# Error handling function
handle_error() {
    local exit_code=$?
    local line_number=$1
    log "ERROR" "Script failed at line $line_number with exit code $exit_code"
    log "ERROR" "Check the log file for details: $LOG_FILE"
    exit $exit_code
}

# Set up error handling
trap 'handle_error $LINENO' ERR

# Function to check prerequisites
check_prerequisites() {
    log "STEP" "Checking prerequisites..."
    
    # Check if required commands exist
    local required_commands=("pg_dump" "pg_restore" "psql" "node" "npx")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log "ERROR" "Required command '$cmd' not found. Please install it and try again."
            exit 1
        fi
    done
    
    # Check if required files exist
    if [[ ! -f "$MATERIALIZED_VIEW_FILE" ]]; then
        log "ERROR" "Materialized view file not found: $MATERIALIZED_VIEW_FILE"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/scripts/migrateCompanyToSeller.js" ]]; then
        log "ERROR" "Migration script not found: $PROJECT_ROOT/scripts/migrateCompanyToSeller.js"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/scripts/migrateProspectToAmazonProspect.js" ]]; then
        log "ERROR" "Migration script not found: $PROJECT_ROOT/scripts/migrateProspectToAmazonProspect.js"
        exit 1
    fi
    
    # Check if database credentials are properly configured
    check_database_credentials
    
    log "INFO" "All prerequisites satisfied"
}

# Function to validate database credentials
check_database_credentials() {
    log "INFO" "Validating database credentials..."
    
    # Check production database credentials
    if [[ "$PROD_HOST" == "your-production-host.com" || "$PROD_USER" == "your-production-user" || "$PROD_PASSWORD" == "your-production-password" || "$PROD_DB" == "your-production-database" ]]; then
        log "ERROR" "Production database credentials not configured. Please set the following environment variables:"
        log "ERROR" "  PROD_HOST, PROD_USER, PROD_PASSWORD, PROD_DB"
        log "ERROR" "Or update the default values in the script."
        exit 1
    fi
    
    # Check local database credentials
    if [[ "$LOCAL_USER" == "your-local-user" || "$LOCAL_PASSWORD" == "your-local-password" || "$LOCAL_DB" == "your-local-database" ]]; then
        log "ERROR" "Local database credentials not configured. Please set the following environment variables:"
        log "ERROR" "  LOCAL_USER, LOCAL_PASSWORD, LOCAL_DB"
        log "ERROR" "Or update the default values in the script."
        exit 1
    fi
    
    log "INFO" "Database credentials validation passed"
}

# Function to test database connections
test_connections() {
    log "STEP" "Testing database connections..."
    
    # Test production database connection
    log "INFO" "Testing production database connection..."
    if ! PGPASSWORD="$PROD_PASSWORD" psql -h "$PROD_HOST" -U "$PROD_USER" -d "$PROD_DB" -c "SELECT 1;" &> /dev/null; then
        log "ERROR" "Cannot connect to production database. Check credentials and network connectivity."
        exit 1
    fi
    
    # Test local database connection
    log "INFO" "Testing local database connection..."
    if ! PGPASSWORD="$LOCAL_PASSWORD" psql -h "$LOCAL_HOST" -p "$LOCAL_PORT" -U "$LOCAL_USER" -d "$LOCAL_DB" -c "SELECT 1;" &> /dev/null; then
        log "ERROR" "Cannot connect to local database. Check credentials and network connectivity."
        exit 1
    fi
    
    log "INFO" "Database connections successful"
}

# Function to extract production data
extract_production_data() {
    log "STEP" "Step 1: Extracting production data..."
    
    log "INFO" "Creating dump of selected tables from production database..."
    
    if PGPASSWORD="$PROD_PASSWORD" pg_dump \
        -h "$PROD_HOST" \
        -U "$PROD_USER" \
        -d "$PROD_DB" \
        --format=custom \
        --no-owner \
        --verbose \
        --table='public."Prospect"' \
        --table='public."Company"' \
        --table='public."SellerCountryMatching"' \
        -f "$PROD_DUMP_FILE"; then
        log "INFO" "Production data extracted successfully to $PROD_DUMP_FILE"
    else
        log "ERROR" "Failed to extract production data"
        exit 1
    fi
}

# Function to reset local database
reset_local_database() {
    log "STEP" "Step 2: Resetting local database..."
    
    log "INFO" "Resetting Prisma migrations..."
    if cd "$PROJECT_ROOT" && npx prisma migrate reset --force; then
        log "INFO" "Prisma migrations reset successfully"
    else
        log "ERROR" "Failed to reset Prisma migrations"
        exit 1
    fi
    
    log "INFO" "Importing production data to local database..."
    if PGPASSWORD="$LOCAL_PASSWORD" pg_restore \
        -h "$LOCAL_HOST" \
        -p "$LOCAL_PORT" \
        -U "$LOCAL_USER" \
        -d "$LOCAL_DB" \
        --clean \
        --if-exists \
        --no-owner \
        --role="$LOCAL_USER" \
        --verbose \
        "$PROD_DUMP_FILE"; then
        log "INFO" "Production data imported to local database successfully"
    else
        log "ERROR" "Failed to import production data to local database"
        exit 1
    fi
}

# Function to run migration scripts
run_migration_scripts() {
    log "STEP" "Step 3: Running migration scripts..."
    
    cd "$PROJECT_ROOT"
    
    log "INFO" "Running Company to Seller migration..."
    if node scripts/migrateCompanyToSeller.js; then
        log "INFO" "Company to Seller migration completed successfully"
    else
        log "ERROR" "Company to Seller migration failed"
        exit 1
    fi
    
    log "INFO" "Running Prospect to AmazonProspect migration..."
    if node scripts/migrateProspectToAmazonProspect.js; then
        log "INFO" "Prospect to AmazonProspect migration completed successfully"
    else
        log "ERROR" "Prospect to AmazonProspect migration failed"
        exit 1
    fi
}

# Function to export migrated data
export_migrated_data() {
    log "STEP" "Step 4: Exporting migrated data..."
    
    log "INFO" "Creating dump of migrated tables..."
    if PGPASSWORD="$LOCAL_PASSWORD" pg_dump \
        -h "$LOCAL_HOST" \
        -p "$LOCAL_PORT" \
        -U "$LOCAL_USER" \
        -d "$LOCAL_DB" \
        --format=custom \
        --no-owner \
        --verbose \
        --table='public."AmazonProspect"' \
        --table='public."AmazonSeller"' \
        --table='public."SellerGroup"' \
        -f "$LOCAL_DUMP_FILE"; then
        log "INFO" "Migrated data exported successfully to $LOCAL_DUMP_FILE"
    else
        log "ERROR" "Failed to export migrated data"
        exit 1
    fi
}

# Function to update production database
update_production_database() {
    log "STEP" "Step 5: Updating production database..."
    
    log "INFO" "Dropping old tables in production..."
    if PGPASSWORD="$PROD_PASSWORD" psql \
        -h "$PROD_HOST" \
        -U "$PROD_USER" \
        -d "$PROD_DB" \
        -c 'DROP TABLE IF EXISTS "AmazonProspect", "AmazonSeller", "SellerGroup" CASCADE;'; then
        log "INFO" "Old tables dropped successfully"
    else
        log "ERROR" "Failed to drop old tables"
        exit 1
    fi
    
    log "INFO" "Importing new tables to production..."
    if PGPASSWORD="$PROD_PASSWORD" pg_restore \
        -h "$PROD_HOST" \
        -U "$PROD_USER" \
        -d "$PROD_DB" \
        --clean \
        --if-exists \
        --no-owner \
        --role="$PROD_USER" \
        --verbose \
        "$LOCAL_DUMP_FILE"; then
        log "INFO" "New tables imported to production successfully"
    else
        log "ERROR" "Failed to import new tables to production"
        exit 1
    fi
}

# Function to rebuild materialized views
rebuild_materialized_views() {
    log "STEP" "Step 6: Rebuilding materialized views..."
    
    log "INFO" "Refreshing materialized view..."
    if PGPASSWORD="$PROD_PASSWORD" psql \
        -h "$PROD_HOST" \
        -U "$PROD_USER" \
        -d "$PROD_DB" \
        -f "$MATERIALIZED_VIEW_FILE"; then
        log "INFO" "Materialized view refreshed successfully"
    else
        log "ERROR" "Failed to refresh materialized view"
        exit 1
    fi
}

# Function to validate migration
validate_migration() {
    log "STEP" "Validating migration..."
    
    log "INFO" "Checking record counts in production..."
    
    # Check AmazonSeller count
    local seller_count=$(PGPASSWORD="$PROD_PASSWORD" psql -h "$PROD_HOST" -U "$PROD_USER" -d "$PROD_DB" -t -c "SELECT COUNT(*) FROM \"AmazonSeller\";" | xargs)
    log "INFO" "AmazonSeller records: $seller_count"
    
    # Check AmazonProspect count
    local prospect_count=$(PGPASSWORD="$PROD_PASSWORD" psql -h "$PROD_HOST" -U "$PROD_USER" -d "$PROD_DB" -t -c "SELECT COUNT(*) FROM \"AmazonProspect\";" | xargs)
    log "INFO" "AmazonProspect records: $prospect_count"
    
    # Check SellerGroup count
    local group_count=$(PGPASSWORD="$PROD_PASSWORD" psql -h "$PROD_HOST" -U "$PROD_USER" -d "$PROD_DB" -t -c "SELECT COUNT(*) FROM \"SellerGroup\";" | xargs)
    log "INFO" "SellerGroup records: $group_count"
    
    if [[ "$seller_count" -gt 0 && "$prospect_count" -gt 0 && "$group_count" -gt 0 ]]; then
        log "INFO" "Migration validation successful - all tables have data"
    else
        log "WARN" "Some tables appear to be empty. Please verify the migration was successful."
    fi
}

# Function to cleanup temporary files
cleanup() {
    log "STEP" "Cleaning up temporary files..."
    
    if [[ -f "$PROD_DUMP_FILE" ]]; then
        rm "$PROD_DUMP_FILE"
        log "INFO" "Removed production dump file"
    fi
    
    if [[ -f "$LOCAL_DUMP_FILE" ]]; then
        rm "$LOCAL_DUMP_FILE"
        log "INFO" "Removed local dump file"
    fi
}

# Main function
main() {
    log "INFO" "Starting database migration process..."
    log "INFO" "Log file: $LOG_FILE"
    
    # Check if running in dry-run mode
    if [[ "$1" == "--dry-run" ]]; then
        log "INFO" "Running in dry-run mode - no actual changes will be made"
        log "INFO" "This will only validate prerequisites and connections"
        check_prerequisites
        test_connections
        log "INFO" "Dry-run completed successfully"
        exit 0
    fi
    
    # Check if user wants to skip cleanup
    local skip_cleanup=false
    if [[ "$1" == "--skip-cleanup" ]]; then
        skip_cleanup=true
        log "INFO" "Cleanup will be skipped"
    fi
    
    # Run migration steps
    check_prerequisites
    test_connections
    extract_production_data
    reset_local_database
    run_migration_scripts
    export_migrated_data
    update_production_database
    rebuild_materialized_views
    validate_migration
    
    if [[ "$skip_cleanup" == false ]]; then
        cleanup
    fi
    
    log "INFO" "Database migration completed successfully!"
    log "INFO" "Migration log saved to: $LOG_FILE"
}

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --dry-run        Validate prerequisites and connections without making changes"
    echo "  --skip-cleanup   Skip cleanup of temporary files"
    echo "  --help           Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  PROD_HOST, PROD_USER, PROD_PASSWORD, PROD_DB    Production database settings"
    echo "  LOCAL_HOST, LOCAL_PORT, LOCAL_USER, LOCAL_PASSWORD, LOCAL_DB  Local database settings"
    echo ""
    echo "Example:"
    echo "  $0 --dry-run     # Test connections and prerequisites"
    echo "  $0               # Run full migration"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        show_usage
        exit 0
        ;;
    --dry-run)
        main "$@"
        ;;
    --skip-cleanup)
        main "$@"
        ;;
    "")
        main "$@"
        ;;
    *)
        log "ERROR" "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac 