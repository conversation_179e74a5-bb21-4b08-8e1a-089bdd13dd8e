-- Recreate the combined_counts_view
CREATE VIEW combined_counts_view AS
WITH seller_tree_counts AS (
    SELECT "SellerTree".entity_type::text AS label,
           count(*)::text AS count_val
    FROM "SellerTree"
    GROUP BY "SellerTree".entity_type
), 
amazon_seller_id_count AS (
    SELECT 'distinct_amazon_seller_id'::text AS label,
           count(DISTINCT "AmazonSeller".amazon_seller_id)::text AS count_val
    FROM "AmazonSeller"
), 
amazon_domain_count AS (
    SELECT 'filtered_domain_count'::text AS label,
           count(DISTINCT "AmazonSeller".domain)::text AS count_val
    FROM "AmazonSeller"
    WHERE "AmazonSeller".domain IS NULL 
       OR "AmazonSeller".domain = ''::text 
       OR ("AmazonSeller".domain IS NOT NULL 
           AND "AmazonSeller".domain <> ''::text 
           AND "AmazonSeller".website_status = 'Final Correct'::text)
)
SELECT seller_tree_counts.label,
       seller_tree_counts.count_val
FROM seller_tree_counts
UNION ALL
SELECT amazon_seller_id_count.label,
       amazon_seller_id_count.count_val
FROM amazon_seller_id_count
UNION ALL
SELECT amazon_domain_count.label,
       amazon_domain_count.count_val
FROM amazon_domain_count
ORDER BY 1;