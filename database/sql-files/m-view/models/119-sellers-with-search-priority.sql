| Feature / Gateway       | Kong AI Gateway                         | Gloo AI Gateway                        | TrueFoundry AI Gateway                 | Helicone AI Gateway                      | LiteLLM                              | Lunary AI Gateway                      |
|------------------------|---------------------------------------|--------------------------------------|---------------------------------------|-----------------------------------------|------------------------------------|--------------------------------------|
| **Semantic Caching**   | Yes, supports semantic caching        | Yes, semantic caching with Redis, Weaviate | Caching options available (less emphasis on semantic) | Intelligent caching to optimize repeated prompts | Redis semantic caching               | Supports semantic caching and caching policies |
| **Observability**      | Detailed logging, tracing, token usage, latency, errors | Cache hit rate, performance metrics, usage tracking | Detailed dashboards, token usage, latency, error tracking | Detailed observability, cost tracking, metrics | Observability for usage, tokens     | Full observability, monitoring, and alerts       |
| **Cost Tracking**      | Yes, with AI-specific analytics/dashboard | Tracks usage and costs for control   | Yes, with alerting and rate-limiting policies | Cost tracking and metrics built-in     | Usage metrics supported             | Cost monitoring and usage analytics  |
| **Open Source**        | Yes                                   | Yes                                  | No (commercial SaaS with some OSS tools) | Yes                                     | Yes                                | Yes                                  |
| **Self-Hosted**        | Yes                                   | Yes                                  | No (primarily SaaS/enterprise)         | Yes                                     | Yes                                | Yes                                  |
| **Routing & Load Balancing** | Smart routing, load balancing strategies | Load balancing for caching efficiency | Supports routing and rate-limiting    | Routing and caching capabilities         | Focused on caching and proxying    | Advanced routing and load balancing   |
| **Datastore Support**  | Configurable with various options     | Redis, Weaviate                      | Integrated with own infrastructure     | In-memory and others configurable        | Redis                             | Redis, others                       |
| **Ease of Setup**      | Moderate complexity, requires config  | Zero-code setup, easy out of the box | Enterprise-focused with onboarding     | Developer friendly with config files     | Lightweight and easy setup          | Moderate, dev-friendly               |
