# Database Migration Quick Reference

## Quick Setup

### 1. Configure Database Credentials

Set environment variables or update the script defaults:

```bash
# Production Database
export PROD_HOST="your-production-host.com"
export PROD_USER="your-production-user"
export PROD_PASSWORD="your-production-password"
export PROD_DB="your-production-database"

# Local Database
export LOCAL_HOST="localhost"
export LOCAL_PORT="5432"
export LOCAL_USER="your-local-user"
export LOCAL_PASSWORD="your-local-password"
export LOCAL_DB="your-local-database"
```

### 2. Make Script Executable

```bash
chmod +x scripts/database_migration.sh
```

### 3. Test Configuration

```bash
# Test prerequisites and connections without making changes
./scripts/database_migration.sh --dry-run
```

### 4. Run Migration

```bash
# Run full migration
./scripts/database_migration.sh
```

## Script Options

```bash
./scripts/database_migration.sh [OPTIONS]

Options:
  --dry-run        Validate prerequisites and connections without making changes
  --skip-cleanup   Skip cleanup of temporary files
  --help           Show help message
```

## Migration Steps Overview

1. **Extract** → Export `Prospect`, `Company`, `SellerCountryMatching` from production
2. **Reset** → Clear local DB and import production data
3. **Transform** → Run Node.js migration scripts
4. **Export** → Create dump of new tables (`AmazonProspect`, `AmazonSeller`, `SellerGroup`)
5. **Update** → Replace old tables in production with new structure
6. **Rebuild** → Refresh materialized views

## Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| Credentials not configured | Set environment variables or update script defaults |
| Connection failed | Verify database credentials and network connectivity |
| Permission denied | Ensure database user has required permissions |
| Script not found | Check file paths and ensure scripts exist |

### Validation Commands

```bash
# Check if credentials are set
echo "PROD_HOST: $PROD_HOST"
echo "LOCAL_USER: $LOCAL_USER"

# Test database connections
PGPASSWORD="$PROD_PASSWORD" psql -h "$PROD_HOST" -U "$PROD_USER" -d "$PROD_DB" -c "SELECT 1;"
PGPASSWORD="$LOCAL_PASSWORD" psql -h "$LOCAL_HOST" -p "$LOCAL_PORT" -U "$LOCAL_USER" -d "$LOCAL_DB" -c "SELECT 1;"
```

## Log Files

Migration logs are saved to: `migration_YYYYMMDD_HHMMSS.log`

## Rollback

If migration fails:

1. Check the log file for error details
2. Verify database connectivity
3. Restore from backup if necessary
4. Fix issues and re-run migration

## Support

- Check migration logs for detailed error information
- Verify all prerequisites are met
- Ensure database permissions are correct
- Contact development team for assistance 