# Database Migration Guide: Company/Prospect to AmazonSeller/AmazonProspect

## Overview

This guide documents the process of migrating data from legacy tables (`Company`, `Prospect`) to new normalized tables (`AmazonSeller`, `AmazonProspect`, `SellerGroup`) in the SellerBot database. This migration improves data structure and enables better relationship management.

## Migration Process Overview

The migration consists of the following steps:

1. **Extract Data**: Export selected tables from production database
2. **Reset Local Database**: Clear local database and import production data
3. **Run Migration Scripts**: Transform data using Node.js scripts
4. **Export Migrated Data**: Create dump of new tables
5. **Update Production**: Replace old tables with new structure
6. **Rebuild Views**: Refresh materialized views

## Prerequisites

### Required Tools
- PostgreSQL client tools (`pg_dump`, `pg_restore`, `psql`)
- Node.js and npm
- Access to both local and production databases

### Environment Variables
Set these environment variables or update the script with your values:

```bash
# Production Database
export PROD_HOST="your-production-host.com"
export PROD_USER="your-production-user"
export PROD_PASSWORD="your-production-password"
export PROD_DB="your-production-database"

# Local Database
export LOCAL_HOST="localhost"
export LOCAL_PORT="5432"
export LOCAL_USER="your-local-user"
export LOCAL_PASSWORD="your-local-password"
export LOCAL_DB="your-local-database"
```

## Detailed Steps

### Step 1: Extract Production Data

Extract the required tables from the production database:

```bash
PGPASSWORD='your-production-password' pg_dump \
  -h your-production-host.com \
  -U your-production-user \
  -d your-production-database \
  --format=custom \
  --no-owner \
  --verbose \
  --table='public."Prospect"' \
  --table='public."Company"' \
  --table='public."SellerCountryMatching"' \
  -f selected_tables_prod.dump
```

**What this does:**
- Creates a custom format dump of the three required tables
- Excludes ownership information (`--no-owner`)
- Provides verbose output for monitoring

### Step 2: Reset Local Database

Clear the local database and import the production data:

```bash
# Reset Prisma migrations
npx prisma migrate reset

# Import production data to local database
PGPASSWORD='your-local-password' pg_restore \
  -h localhost \
  -p 5432 \
  -U your-local-user \
  -d your-local-database \
  --clean \
  --if-exists \
  --no-owner \
  --role=your-local-user \
  --verbose \
  selected_tables_prod.dump
```

**What this does:**
- Resets Prisma migrations to ensure clean state
- Restores the production data to local database
- Uses `--clean` to drop existing tables before import
- Uses `--if-exists` to handle missing tables gracefully

### Step 3: Run Migration Scripts

Execute the Node.js migration scripts to transform the data:

```bash
# Migrate Company data to AmazonSeller and SellerGroup
node scripts/migrateCompanyToSeller.js

# Migrate Prospect data to AmazonProspect
node scripts/migrateProspectToAmazonProspect.js
```

**What these scripts do:**

#### `migrateCompanyToSeller.js`
- Creates `SellerGroup` records for each company
- Transforms `Company` records to `AmazonSeller` records
- Handles marketplace-specific seller creation
- Maps relationships between sellers and seller groups
- Processes data in batches for performance

#### `migrateProspectToAmazonProspect.js`
- Transforms `Prospect` records to `AmazonProspect` records
- Maintains relationships with `AmazonSeller`
- Updates `ClientsProspectsMatching` references
- Uses upsert logic based on email or LinkedIn
- Handles data validation and error cases

### Step 4: Export Migrated Data

Create a dump of the newly migrated tables:

```bash
PGPASSWORD='your-local-password' pg_dump \
  -h localhost \
  -p 5432 \
  -U your-local-user \
  -d your-local-database \
  --format=custom \
  --no-owner \
  --verbose \
  --table='public."AmazonProspect"' \
  --table='public."AmazonSeller"' \
  --table='public."SellerGroup"' \
  -f local_tables_dump.dump
```

### Step 5: Update Production Database

Replace the old tables with the new structure in production:

```bash
# Drop old tables in production
PGPASSWORD='your-production-password' psql \
  -h your-production-host.com \
  -U your-production-user \
  -d your-production-database \
  -c 'DROP TABLE IF EXISTS "AmazonProspect", "AmazonSeller", "SellerGroup" CASCADE;'

# Import new tables to production
PGPASSWORD='your-production-password' pg_restore \
  -h your-production-host.com \
  -U your-production-user \
  -d your-production-database \
  --clean \
  --if-exists \
  --no-owner \
  --role=your-production-user \
  --verbose \
  local_tables_dump.dump
```

### Step 6: Rebuild Materialized Views

Refresh the materialized view that depends on the new table structure:

```bash
PGPASSWORD='your-production-password' psql \
  -h your-production-host.com \
  -U your-production-user \
  -d your-production-database \
  -f database/sql-files/m-view/amazon_seller_priority_expanded_mv.sql
```

## Data Structure Changes

### Before Migration
- `Company` table: Contains seller information with embedded marketplace data
- `Prospect` table: Contains prospect information with direct seller references
- No normalized seller group structure

### After Migration
- `AmazonSeller` table: Normalized seller information with marketplace separation
- `AmazonProspect` table: Updated prospect information with proper seller relationships
- `SellerGroup` table: New table for grouping related sellers
- Improved data integrity and relationship management

## Validation Steps

After completing the migration, verify the data integrity:

1. **Check Record Counts**: Ensure all records were migrated
2. **Verify Relationships**: Confirm foreign key relationships are intact
3. **Test Application**: Run application tests to ensure functionality
4. **Monitor Performance**: Check query performance with new structure

## Rollback Plan

If issues occur during migration:

1. **Backup Current State**: Create backup of current production data
2. **Restore Previous Tables**: Restore the original table structure
3. **Investigate Issues**: Debug migration scripts and data issues
4. **Re-run Migration**: Fix issues and re-run the migration process

## Troubleshooting

### Common Issues

1. **Connection Errors**: Verify database credentials and network connectivity
2. **Permission Errors**: Ensure database user has required permissions
3. **Data Type Errors**: Check for incompatible data types in migration scripts
4. **Memory Issues**: Reduce batch sizes in migration scripts for large datasets

### Performance Optimization

- Adjust `BATCH_SIZE` in migration scripts for your data volume
- Monitor database performance during migration
- Consider running migrations during low-traffic periods
- Use database indexes to improve query performance

## Script Usage

Use the provided shell script for automated execution:

```bash
# Make script executable
chmod +x scripts/database_migration.sh

# Run migration
./scripts/database_migration.sh
```

The script includes error handling, logging, and validation steps to ensure a successful migration.

## Support

For issues or questions about this migration process:

1. Check the migration logs for error details
2. Verify database connectivity and permissions
3. Review the migration script output for warnings
4. Contact the development team for assistance 