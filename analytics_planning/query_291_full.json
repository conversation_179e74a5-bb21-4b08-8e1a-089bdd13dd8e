{"cache_invalidated_at": "2025-09-04T18:14:07.785063Z", "description": null, "archived": false, "view_count": 98, "collection_position": null, "source_card_id": 278, "table_id": null, "can_run_adhoc_query": true, "result_metadata": [{"database_type": "date", "semantic_type": "type/CreationDate", "unit": "day", "name": "week_start_date", "source": "breakout", "field_ref": ["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "day", "original-temporal-unit": "month"}], "effective_type": "type/Date", "display_name": "week_start_date: Day", "fingerprint": {"global": {"distinct-count": 24, "nil%": 0}, "type": {"type/DateTime": {"earliest": "2025-01-12", "latest": "2025-06-22"}}}, "base_type": "type/Date"}, {"field_ref": ["field", "campaign_code", {"base-type": "type/Text"}], "base_type": "type/Text", "database_type": "text", "name": "campaign_code", "effective_type": "type/Text", "display_name": "campaign_code", "fingerprint": {"global": {"distinct-count": 8, "nil%": 0}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 2.2895472330911124}}}, "source": "breakout"}, {"field_ref": ["field", "jeff_client_name", {"base-type": "type/Text"}], "base_type": "type/Text", "database_type": "text", "name": "jeff_client_name", "effective_type": "type/Text", "display_name": "jeff_client_name", "fingerprint": {"global": {"distinct-count": 10, "nil%": 0.05310229178311906}, "type": {"type/Text": {"percent-json": 0, "percent-url": 0, "percent-email": 0, "percent-state": 0, "average-length": 8.26774734488541}}}, "source": "breakout"}, {"semantic_type": "type/Quantity", "name": "sum", "source": "aggregation", "field_ref": ["aggregation", 0], "effective_type": "type/BigInteger", "aggregation_index": 0, "ident": "CrdaeAA9w-loICfqhGKio", "display_name": "Sum of email_1_sent_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_2", "source": "aggregation", "field_ref": ["aggregation", 1], "effective_type": "type/BigInteger", "aggregation_index": 1, "ident": "_nydEUf9dSVwWv466T_rq", "display_name": "Sum of replies_after_email_1_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_3", "source": "aggregation", "field_ref": ["aggregation", 2], "effective_type": "type/BigInteger", "aggregation_index": 2, "ident": "oVbfuXo9Duf3_ya1PUSAD", "display_name": "Sum of automated_replies_after_email_1_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_4", "source": "aggregation", "field_ref": ["aggregation", 3], "effective_type": "type/BigInteger", "aggregation_index": 3, "ident": "Va1YZiQfvAMNpb8vE93Ub", "display_name": "Sum of error_replies_after_email_1_count", "base_type": "type/BigInteger"}, {"ident": "JHGvBv84M9zp89AmNhSKM", "base_type": "type/BigInteger", "name": "sum_5", "display_name": "Sum of meeting_slots_sent_after_1", "effective_type": "type/BigInteger", "source": "aggregation", "field_ref": ["aggregation", 4], "aggregation_index": 4}, {"semantic_type": "type/Quantity", "name": "sum_6", "source": "aggregation", "field_ref": ["aggregation", 5], "effective_type": "type/BigInteger", "aggregation_index": 5, "ident": "SMOCRbVKFDlWb0ZLsu1Fu", "display_name": "Sum of email_2_sent_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_7", "source": "aggregation", "field_ref": ["aggregation", 6], "effective_type": "type/BigInteger", "aggregation_index": 6, "ident": "WDulOGUIoB9RUnCDYO7xj", "display_name": "Sum of automated_replies_after_email_2_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_8", "source": "aggregation", "field_ref": ["aggregation", 7], "effective_type": "type/BigInteger", "aggregation_index": 7, "ident": "lD5iEzyBcJ7KsLVzqEgDi", "display_name": "Sum of error_replies_after_email_2_count", "base_type": "type/BigInteger"}, {"ident": "aMBfxKnJveavtd2aCZjCj", "base_type": "type/BigInteger", "name": "sum_9", "display_name": "Sum of meeting_slots_sent_after_2", "effective_type": "type/BigInteger", "source": "aggregation", "field_ref": ["aggregation", 8], "aggregation_index": 8}, {"semantic_type": "type/Quantity", "name": "sum_10", "source": "aggregation", "field_ref": ["aggregation", 9], "effective_type": "type/BigInteger", "aggregation_index": 9, "ident": "rxfBk2isvvcwyt2MihiH_", "display_name": "Sum of replies_after_email_3_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_11", "source": "aggregation", "field_ref": ["aggregation", 10], "effective_type": "type/BigInteger", "aggregation_index": 10, "ident": "c_9kKrG67qAEyur-up1l2", "display_name": "Sum of automated_replies_after_email_3_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_12", "source": "aggregation", "field_ref": ["aggregation", 11], "effective_type": "type/BigInteger", "aggregation_index": 11, "ident": "qQ8Z7kqqaNbskk7c9F5RZ", "display_name": "Sum of error_replies_after_email_3_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_13", "source": "aggregation", "field_ref": ["aggregation", 12], "effective_type": "type/BigInteger", "aggregation_index": 12, "ident": "csQmKIZk387qEm3VzkhUl", "display_name": "Sum of replies_after_email_2_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_14", "source": "aggregation", "field_ref": ["aggregation", 13], "effective_type": "type/BigInteger", "aggregation_index": 13, "ident": "NmgyI9ttGOXNyRBX9AP0r", "display_name": "Sum of email_3_sent_count", "base_type": "type/BigInteger"}, {"ident": "-vXjYKEZH_pTBgsCJ4NBe", "base_type": "type/BigInteger", "name": "sum_15", "display_name": "Sum of meeting_slots_sent_after_3", "effective_type": "type/BigInteger", "source": "aggregation", "field_ref": ["aggregation", 14], "aggregation_index": 14}, {"ident": "qjKiJ9-rs548LB6d6ptXq", "base_type": "type/BigInteger", "name": "sum_16", "display_name": "Sum of meeting_slots_sent_unknown", "effective_type": "type/BigInteger", "source": "aggregation", "field_ref": ["aggregation", 15], "aggregation_index": 15}, {"ident": "vMyI0lVI7BWMr9h8bWjt5", "base_type": "type/BigInteger", "name": "sum_17", "display_name": "Sum of total_meeting_slots_sent", "effective_type": "type/BigInteger", "source": "aggregation", "field_ref": ["aggregation", 16], "aggregation_index": 16}, {"ident": "Gb578g9x5dgOhYNMB9M6g", "base_type": "type/BigInteger", "name": "sum_18", "display_name": "Sum of meetings_booked", "effective_type": "type/BigInteger", "source": "aggregation", "field_ref": ["aggregation", 17], "aggregation_index": 17}, {"semantic_type": "type/Quantity", "name": "sum_19", "source": "aggregation", "field_ref": ["aggregation", 18], "effective_type": "type/BigInteger", "aggregation_index": 18, "ident": "U2NtWchBm9EyyA0Zfpx9s", "display_name": "Sum of email_1_sent_personal_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_20", "source": "aggregation", "field_ref": ["aggregation", 19], "effective_type": "type/BigInteger", "aggregation_index": 19, "ident": "UXuicYrYq9cpo-S1_EMxh", "display_name": "Sum of email_1_sent_role_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_21", "source": "aggregation", "field_ref": ["aggregation", 20], "effective_type": "type/BigInteger", "aggregation_index": 20, "ident": "mqrVfzMjk6XcL4yZbD7sr", "display_name": "Sum of email_1_sent_work_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_22", "source": "aggregation", "field_ref": ["aggregation", 21], "effective_type": "type/BigInteger", "aggregation_index": 21, "ident": "Ut2R6ZVYSY6C3PdSnq3sz", "display_name": "Sum of email_1_sent_unknown_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_23", "source": "aggregation", "field_ref": ["aggregation", 22], "effective_type": "type/BigInteger", "aggregation_index": 22, "ident": "gvWGSbxZ_D33kH-fXg0Of", "display_name": "Sum of email_2_sent_personal_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_24", "source": "aggregation", "field_ref": ["aggregation", 23], "effective_type": "type/BigInteger", "aggregation_index": 23, "ident": "NJuAoq-RqlJT2ETqwfggn", "display_name": "Sum of email_2_sent_role_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_25", "source": "aggregation", "field_ref": ["aggregation", 24], "effective_type": "type/BigInteger", "aggregation_index": 24, "ident": "fj2HsnYkMtsuh1UWadkR7", "display_name": "Sum of email_2_sent_work_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_26", "source": "aggregation", "field_ref": ["aggregation", 25], "effective_type": "type/BigInteger", "aggregation_index": 25, "ident": "nLBdxByDXA1X7zA7yjpc_", "display_name": "Sum of email_2_sent_unknown_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_27", "source": "aggregation", "field_ref": ["aggregation", 26], "effective_type": "type/BigInteger", "aggregation_index": 26, "ident": "wGcdp6oMgIeiYp1zK4lIK", "display_name": "Sum of email_3_sent_personal_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_28", "source": "aggregation", "field_ref": ["aggregation", 27], "effective_type": "type/BigInteger", "aggregation_index": 27, "ident": "gRtaoWmoZKfNK7bAYL2B8", "display_name": "Sum of email_3_sent_role_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_29", "source": "aggregation", "field_ref": ["aggregation", 28], "effective_type": "type/BigInteger", "aggregation_index": 28, "ident": "WyQuonHdJDkxtRjYQcawa", "display_name": "Sum of email_3_sent_work_count", "base_type": "type/BigInteger"}, {"semantic_type": "type/Quantity", "name": "sum_30", "source": "aggregation", "field_ref": ["aggregation", 29], "effective_type": "type/BigInteger", "aggregation_index": 29, "ident": "2JKlTgxsFqetKxOi5HU5m", "display_name": "Sum of email_3_sent_unknown_count", "base_type": "type/BigInteger"}], "creator": {"email": "<EMAIL>", "first_name": "TechTeam", "last_login": "2025-09-11T14:20:40.840338Z", "is_qbnewb": false, "is_superuser": true, "id": 2, "last_name": "Admin", "date_joined": "2024-09-30T18:52:12.583269Z", "common_name": "TechTeam Admin"}, "initially_published_at": null, "can_write": true, "database_id": 4, "enable_embedding": false, "collection_id": 48, "query_type": "query", "name": "Smartlead Aggregate Analytics (by funnel & client)", "last_query_start": "2025-08-27T09:38:13.550574Z", "dashboard_count": 1, "last_used_at": "2025-08-27T09:38:13.617521Z", "dashboard": null, "type": "question", "average_query_time": 28134.45238095238, "creator_id": 2, "can_restore": false, "moderation_reviews": [], "updated_at": "2025-09-04T18:14:07.784183Z", "made_public_by_id": null, "embedding_params": null, "cache_ttl": null, "dataset_query": {"database": 4, "type": "query", "query": {"breakout": [["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "day", "original-temporal-unit": "month"}], ["field", "campaign_code", {"base-type": "type/Text"}], ["field", "jeff_client_name", {"base-type": "type/Text"}]], "order-by": [["desc", ["field", "week_start_date", {"base-type": "type/Date", "temporal-unit": "day", "inherited-temporal-unit": "day"}]]], "aggregation": [["sum", ["field", "email_1_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_1_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_1", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_2", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "automated_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "error_replies_after_email_3_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "replies_after_email_2_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_after_3", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meeting_slots_sent_unknown", {"base-type": "type/BigInteger"}]], ["sum", ["field", "total_meeting_slots_sent", {"base-type": "type/BigInteger"}]], ["sum", ["field", "meetings_booked", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_personal_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_role_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_work_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_1_sent_unknown_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_personal_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_role_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_work_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_2_sent_unknown_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_personal_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_role_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_work_count", {"base-type": "type/BigInteger"}]], ["sum", ["field", "email_3_sent_unknown_count", {"base-type": "type/BigInteger"}]]], "source-table": "card__278", "aggregation-idents": {"0": "CrdaeAA9w-loICfqhGKio", "1": "_nydEUf9dSVwWv466T_rq", "2": "oVbfuXo9Duf3_ya1PUSAD", "3": "Va1YZiQfvAMNpb8vE93Ub", "4": "JHGvBv84M9zp89AmNhSKM", "5": "SMOCRbVKFDlWb0ZLsu1Fu", "6": "WDulOGUIoB9RUnCDYO7xj", "7": "lD5iEzyBcJ7KsLVzqEgDi", "8": "aMBfxKnJveavtd2aCZjCj", "9": "rxfBk2isvvcwyt2MihiH_", "10": "c_9kKrG67qAEyur-up1l2", "11": "qQ8Z7kqqaNbskk7c9F5RZ", "12": "csQmKIZk387qEm3VzkhUl", "13": "NmgyI9ttGOXNyRBX9AP0r", "14": "-vXjYKEZH_pTBgsCJ4NBe", "15": "qjKiJ9-rs548LB6d6ptXq", "16": "vMyI0lVI7BWMr9h8bWjt5", "17": "Gb578g9x5dgOhYNMB9M6g", "18": "U2NtWchBm9EyyA0Zfpx9s", "19": "UXuicYrYq9cpo-S1_EMxh", "20": "mqrVfzMjk6XcL4yZbD7sr", "21": "Ut2R6ZVYSY6C3PdSnq3sz", "22": "gvWGSbxZ_D33kH-fXg0Of", "23": "NJuAoq-RqlJT2ETqwfggn", "24": "fj2HsnYkMtsuh1UWadkR7", "25": "nLBdxByDXA1X7zA7yjpc_", "26": "wGcdp6oMgIeiYp1zK4lIK", "27": "gRtaoWmoZKfNK7bAYL2B8", "28": "WyQuonHdJDkxtRjYQcawa", "29": "2JKlTgxsFqetKxOi5HU5m"}, "breakout-idents": {"0": "Z7EoGCBkxqe9i3vW-fmpf", "1": "l4xsINkbbnrn9fESv4r-w", "2": "5TqfOHck8P8qlowFoeTpX"}, "filter": [">", ["field", "week_start_date", {"base-type": "type/Date"}], "2025-05-01"]}}, "id": 291, "parameter_mappings": [], "can_manage_db": true, "display": "table", "archived_directly": false, "entity_id": "vgi587RpHrLXFO-uS9Fpv", "collection_preview": true, "last-edit-info": {"id": 2, "email": "<EMAIL>", "first_name": "TechTeam", "last_name": "Admin", "timestamp": "2025-09-04T18:14:07.806532Z"}, "visualization_settings": {"table.pivot": false, "table.column_formatting": [], "table.pivot_column": "week_start_date", "table.cell_column": "sum", "column_settings": {}}, "collection": {"authority_level": null, "description": "SmartLead Analytics queries for <PERSON> Seller (Dashboard)", "archived": false, "slug": "smartlead_analytics", "archive_operation_id": null, "name": "SmartLead Analytics", "personal_owner_id": null, "type": null, "is_sample": false, "id": 48, "archived_directly": null, "entity_id": "pv4Z7wvDZO7l5iUCyTaLu", "location": "/43/", "namespace": null, "is_personal": false, "created_at": "2025-09-04T18:14:03.607746Z"}, "metabase_version": "v0.54.5.4 (f262d88)", "parameters": [], "dashboard_id": null, "created_at": "2025-06-23T19:14:15.219573Z", "parameter_usage_count": 0, "public_uuid": null, "can_delete": false}