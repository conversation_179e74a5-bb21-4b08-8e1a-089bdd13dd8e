# Step 3: Dashboard Analysis - Complete Mapping

## Dashboard Overview
- **Name**: Old Jeff Dashboard
- **Total Cards**: 91 (but only 60 unique queries after removing duplicates)
- **Tabs**: 7 (but cards show as "No Tab" - may be a UI issue)
- **Filters**: 4 (Tags, Clients, Funnel, Provider)

## Key Findings

### Tables Actually Used

#### From public2 schema (our analytics tables):
1. **FunnelClientAnalytics** - ✅ CONFIRMED USED
   - Query 294: Prospects Reached (by Week)
   - Query 297: Raw Numbers by Funnel
   - Query 324: Email 1, 2, 3 sent week on week
   - Query 325: Email 1 Sent by Role Week on Week
   - Query 326: Prospects Reached by Client

2. **MeetingsFromReplit** - ✅ CONFIRMED USED
   - Query 308: Meetings CRM Stats by Client
   - Query 335: Slots Sent by Funnel
   - Query 337: Meetings Booked by Funnel

3. **InboxesFromReplit** - ✅ FOUND IN SQL
   - Used in dependency queries

4. **InboxTagsFromReplit** - ✅ FOUND IN SQL
   - Used in dependency queries

5. **ProviderAnalytics** - ❌ NOT FOUND
   - Despite Query 314 being "Smartlead Aggregate Analytics (by provider & client)"
   - It actually depends on Query 313, not ProviderAnalytics table

6. **TagAnalytics** - ❌ NOT FOUND
   - Despite Query 290 being "Smartlead Aggregate Analytics (by Tag)"
   - It actually depends on Query 289, not TagAnalytics table

### Query Dependency Chain

Many queries are based on other queries, not directly on tables:

```
Query 290 (Tag Analytics) → Query 289 → Query 277 → Base tables
Query 291 (Funnel/Client) → Query 278 → Query 277 → Base tables
Query 314 (Provider/Client) → Query 313 → Query 277 → Base tables
```

**Query 277 appears to be a critical base query** that many others depend on.

### Dashboard Filters

The dashboard has 4 filters but they're NOT connected to any cards (0 mappings each):
1. **Tags** - string filter, no cards mapped
2. **Clients** - string filter, no cards mapped
3. **Funnel** - string filter, no cards mapped
4. **Provider** - string filter, no cards mapped

This means filters likely don't work on the current dashboard!

## Query Categories

### Reply Rate Analytics (Multiple queries)
- All based on dependency queries (289, 313, etc.)
- NOT using ProviderAnalytics or TagAnalytics directly

### Email Sent Analytics
- Using FunnelClientAnalytics directly

### Meeting Analytics
- Using MeetingsFromReplit directly

### Funnel Analytics
- Complex SQL queries with CTEs
- Using raw tables from public schema

## Critical Base Queries

These queries are dependencies for many others:

1. **Query 277** - Most critical (base for many analytics)
2. **Query 119** - Sellers with search priority
3. **Query 117** - Sellers with SP classifications
4. **Query 278** - Base for funnel/client analytics
5. **Query 289** - Base for tag analytics
6. **Query 313** - Base for provider analytics

## Why ProviderAnalytics and TagAnalytics Aren't Used

The dashboard queries that should use these tables are actually:
1. Getting data from upstream queries (277, 278, 289, 313)
2. These upstream queries likely calculate the same metrics on-the-fly
3. This explains why ProviderAnalytics and TagAnalytics timeout - they're not needed!

## Implications for Our New System

1. **Focus on what's actually used**:
   - FunnelClientAnalytics (has all 32 metrics we need)
   - MeetingsFromReplit (but needs SP data added)
   - Base Email table for raw data

2. **Critical dependency**: We need to understand Query 277
   - It's the foundation for tag, provider, and other analytics
   - Likely does complex joins and calculations

3. **Filters need fixing**: Current filters aren't connected

4. **Skip unused tables**:
   - ProviderAnalytics (times out, not used)
   - TagAnalytics (times out, not used)

## Next Steps

1. Fetch Query 277 to understand the base logic
2. Map how Query 277 connects to our 5 dimensions
3. Determine if we need to replicate Query 277's logic or create something new