/**
 * Step 0: Fetch all raw data needed for 5D analytics
 * Fetches from multiple sources and saves to JSON files for processing
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir);
}

// API configurations
const METABASE_URL = 'metabase.equalcollective.com';
const METABASE_API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';
const JEFFCRM_URL = 'jeffcrm.equalcollective.com';

/**
 * Generic HTTPS request helper
 */
function makeRequest(hostname, path, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname,
      path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(new Error(`Failed to parse JSON from ${hostname}${path}: ${e.message}`));
        }
      });
    }).on('error', reject);
  });
}

/**
 * 1. Fetch Query 271 (SP data) from Metabase
 */
async function fetchSPData() {
  console.log('📥 Fetching SP data from Query 271...');
  
  try {
    // First get the query structure
    const queryInfo = await makeRequest(
      METABASE_URL, 
      '/api/card/271',
      { 'X-API-KEY': METABASE_API_KEY }
    );
    
    console.log(`   Query name: ${queryInfo.name}`);
    
    // Execute the query to get results
    const results = await makeRequest(
      METABASE_URL,
      '/api/card/271/query/json',
      { 'X-API-KEY': METABASE_API_KEY }
    );
    
    console.log(`   ✅ Fetched ${results.length} SP records`);
    
    // Save to file
    fs.writeFileSync(
      path.join(dataDir, 'sp_data.json'),
      JSON.stringify(results, null, 2)
    );
    
    // Create a map for faster lookups
    const spMap = {};
    results.forEach(row => {
      const email = row['Seller Group - Prospects with eMail Classification__email'] || row.email;
      if (email) {
        spMap[email.toLowerCase()] = {
          sp: row.dominant_search_priority || row.jeff_search_priority || 'UNK',
          email_type: row['Seller Group - Prospects with eMail Classification__626da005'] || row.prospect_email_type || 'unknown'
        };
      }
    });
    
    fs.writeFileSync(
      path.join(dataDir, 'sp_map.json'),
      JSON.stringify(spMap, null, 2)
    );
    
    console.log(`   ✅ Created SP lookup map with ${Object.keys(spMap).length} entries`);
    
    return spMap;
  } catch (error) {
    console.error('   ❌ Error fetching SP data:', error.message);
    // Return empty map if fails
    return {};
  }
}

/**
 * 2. Fetch inbox data (email provider info)
 */
async function fetchInboxData() {
  console.log('📥 Fetching inbox data from JeffCRM...');
  
  try {
    const inboxes = await makeRequest(
      JEFFCRM_URL,
      '/api/public/inbox-tags'
    );
    
    console.log(`   ✅ Fetched ${inboxes.length} inbox records`);
    
    // Save raw data
    fs.writeFileSync(
      path.join(dataDir, 'inboxes.json'),
      JSON.stringify(inboxes, null, 2)
    );
    
    // Create provider map for faster lookups
    const providerMap = {};
    inboxes.forEach(inbox => {
      if (inbox.email) {
        const domain = inbox.domain?.name || inbox.email.split('@')[1];
        const provider = inbox.domain?.currentProvider?.name || 'Unknown';
        
        providerMap[inbox.email.toLowerCase()] = {
          provider: provider,
          domain: domain,
          domain_type: classifyDomainType(domain)
        };
      }
    });
    
    fs.writeFileSync(
      path.join(dataDir, 'provider_map.json'),
      JSON.stringify(providerMap, null, 2)
    );
    
    console.log(`   ✅ Created provider lookup map with ${Object.keys(providerMap).length} entries`);
    
    return providerMap;
  } catch (error) {
    console.error('   ❌ Error fetching inbox data:', error.message);
    return {};
  }
}

/**
 * 3. Fetch meetings data for attribution
 */
async function fetchMeetingsData() {
  console.log('📥 Fetching meetings data from JeffCRM...');
  
  try {
    const meetings = await makeRequest(
      JEFFCRM_URL,
      '/api/public/meetings'
    );
    
    console.log(`   ✅ Fetched ${meetings.length} meeting records`);
    
    // Save raw data
    fs.writeFileSync(
      path.join(dataDir, 'meetings.json'),
      JSON.stringify(meetings, null, 2)
    );
    
    // Process meetings for attribution
    const meetingsByClient = {};
    
    meetings.forEach(meeting => {
      if (!meeting.client?.smartleadId) return;
      
      const clientId = meeting.client.smartleadId;
      const campaignName = meeting.campaignName || '';
      const funnel = extractFunnelCode(campaignName);
      
      // Initialize client entry
      if (!meetingsByClient[clientId]) {
        meetingsByClient[clientId] = {};
      }
      if (!meetingsByClient[clientId][funnel]) {
        meetingsByClient[clientId][funnel] = {
          slots_sent_after_1: 0,
          slots_sent_after_2: 0,
          slots_sent_after_3: 0,
          slots_sent_unknown: 0,
          booked: 0
        };
      }
      
      // Attribution logic based on email history
      const maxEmailSeq = getMaxEmailSequence(meeting.emailHistory);
      
      if (meeting.currentStage === 'slots_sent' || meeting.slotSentDate) {
        if (maxEmailSeq === '1') meetingsByClient[clientId][funnel].slots_sent_after_1++;
        else if (maxEmailSeq === '2') meetingsByClient[clientId][funnel].slots_sent_after_2++;
        else if (maxEmailSeq === '3') meetingsByClient[clientId][funnel].slots_sent_after_3++;
        else meetingsByClient[clientId][funnel].slots_sent_unknown++;
      }
      
      if (meeting.currentStage === 'booked' || meeting.bookingDate) {
        meetingsByClient[clientId][funnel].booked++;
      }
    });
    
    fs.writeFileSync(
      path.join(dataDir, 'meetings_by_client.json'),
      JSON.stringify(meetingsByClient, null, 2)
    );
    
    console.log(`   ✅ Processed meetings for ${Object.keys(meetingsByClient).length} clients`);
    
    return meetingsByClient;
  } catch (error) {
    console.error('   ❌ Error fetching meetings data:', error.message);
    return {};
  }
}

/**
 * Helper: Extract funnel code from campaign name
 */
function extractFunnelCode(campaignName) {
  const match = campaignName.match(/(\d{1,2}[A-Z])/);
  if (match) return match[1];
  if (campaignName.includes('HKP')) return 'HKP';
  return 'UNK';
}

/**
 * Helper: Get max email sequence from email history
 */
function getMaxEmailSequence(emailHistory) {
  if (!emailHistory || !Array.isArray(emailHistory)) return null;
  
  let maxSeq = null;
  emailHistory.forEach(email => {
    if (email.type === 'SENT' && email.email_seq_number) {
      if (!maxSeq || parseInt(email.email_seq_number) > parseInt(maxSeq)) {
        maxSeq = email.email_seq_number;
      }
    }
  });
  
  return maxSeq;
}

/**
 * Helper: Classify domain type
 */
function classifyDomainType(domain) {
  if (!domain) return 'unknown';
  
  const tld = domain.substring(domain.lastIndexOf('.'));
  
  if (['.com', '.net', '.org'].includes(tld)) {
    return 'standard';
  } else if (['.click', '.top', '.site', '.sbs', '.agency'].includes(tld)) {
    return 'alternative';
  } else {
    return 'other';
  }
}

/**
 * Main execution
 */
async function fetchAllData() {
  console.log('🚀 Starting data fetch for 5D Analytics System\n');
  console.log(`📁 Data will be saved to: ${dataDir}\n`);
  
  const startTime = Date.now();
  
  try {
    // Fetch all data sources in parallel where possible
    const [spMap, providerMap, meetingsData] = await Promise.all([
      fetchSPData(),
      fetchInboxData(),
      fetchMeetingsData()
    ]);
    
    // Save summary
    const summary = {
      timestamp: new Date().toISOString(),
      sp_records: Object.keys(spMap).length,
      inbox_records: Object.keys(providerMap).length,
      meetings_processed: Object.keys(meetingsData).length,
      execution_time_ms: Date.now() - startTime
    };
    
    fs.writeFileSync(
      path.join(dataDir, 'fetch_summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\n📊 Fetch Summary:');
    console.log(`   SP records: ${summary.sp_records}`);
    console.log(`   Inbox records: ${summary.inbox_records}`);
    console.log(`   Meetings processed: ${summary.meetings_processed}`);
    console.log(`   Execution time: ${(summary.execution_time_ms / 1000).toFixed(2)}s`);
    
    console.log('\n✅ Data fetch completed successfully!');
    return true;
  } catch (error) {
    console.error('\n❌ Fatal error during data fetch:', error);
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  fetchAllData().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { fetchAllData, fetchSPData, fetchInboxData, fetchMeetingsData };