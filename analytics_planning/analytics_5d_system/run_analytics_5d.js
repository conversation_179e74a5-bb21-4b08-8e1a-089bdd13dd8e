#!/usr/bin/env node

/**
 * Main orchestrator for 5D Analytics System
 * Runs all steps in sequence to build the analytics tables
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Change to script directory
process.chdir(__dirname);

/**
 * Run a step with error handling
 */
function runStep(stepNumber, scriptName, description) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`STEP ${stepNumber}: ${description}`);
  console.log('='.repeat(60) + '\n');
  
  try {
    execSync(`node ${scriptName}`, { 
      stdio: 'inherit',
      cwd: __dirname
    });
    console.log(`\n✅ Step ${stepNumber} completed successfully`);
    return true;
  } catch (error) {
    console.error(`\n❌ Step ${stepNumber} failed: ${error.message}`);
    return false;
  }
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 5D ANALYTICS SYSTEM - FULL BUILD PROCESS');
  console.log('=' .repeat(60));
  console.log('This will:');
  console.log('  1. Fetch data from Metabase and JeffCRM');
  console.log('  2. Process emails and apply classification logic');
  console.log('  3. Create tables and load aggregated data');
  console.log('=' .repeat(60));
  
  const startTime = Date.now();
  
  // Create data directory if it doesn't exist
  const dataDir = path.join(__dirname, 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir);
    console.log(`\n📁 Created data directory: ${dataDir}`);
  }
  
  // Run all steps
  const steps = [
    { 
      number: 0, 
      script: '0_fetchData.js', 
      description: 'Fetch all data sources (SP, Inboxes, Meetings)' 
    },
    { 
      number: 1, 
      script: '1_processAndAggregate.js', 
      description: 'Process emails and create aggregations' 
    },
    { 
      number: 2, 
      script: '2_createTables.js', 
      description: 'Create tables and load data' 
    }
  ];
  
  let allSuccess = true;
  
  for (const step of steps) {
    const success = runStep(step.number, step.script, step.description);
    if (!success) {
      allSuccess = false;
      console.error(`\n⚠️  Stopping due to failure in Step ${step.number}`);
      break;
    }
  }
  
  // Final summary
  console.log('\n' + '='.repeat(60));
  console.log('FINAL SUMMARY');
  console.log('='.repeat(60));
  
  const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);
  
  if (allSuccess) {
    console.log('✅ All steps completed successfully!');
    console.log(`⏱️  Total execution time: ${executionTime} seconds`);
    console.log('\n📊 Analytics table is ready for use:');
    console.log('   Table: public2.analytics_5_dimensions');
    console.log('   Dimensions: client, funnel, email_provider, domain_type, sp');
    console.log('   Metrics: 32 metrics across email sequences');
    console.log('\n🎯 You can now:');
    console.log('   1. Query the table directly for analytics');
    console.log('   2. Create Metabase dashboards using this table');
    console.log('   3. Run test_analytics.js to compare with old system');
  } else {
    console.log('❌ Build process failed');
    console.log(`⏱️  Execution time before failure: ${executionTime} seconds`);
    console.log('\n🔧 To debug:');
    console.log('   1. Check the error message above');
    console.log('   2. Run individual steps manually');
    console.log('   3. Check data/ directory for intermediate files');
  }
  
  process.exit(allSuccess ? 0 : 1);
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('\n❌ Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.error('\n❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run main
if (require.main === module) {
  main();
}

module.exports = { main };