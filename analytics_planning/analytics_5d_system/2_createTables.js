/**
 * Step 2: Create analytics tables in database
 * Creates the analytics_5_dimensions table and loads processed data
 */

const prisma = require("../../database/prisma/getPrismaClient");
const fs = require('fs');
const path = require('path');

const dataDir = path.join(__dirname, 'data');

/**
 * Create the analytics table if it doesn't exist
 */
async function createAnalyticsTable() {
  console.log('🏗️  Creating analytics_5_dimensions table...');
  
  try {
    // Drop existing table if needed (for fresh start)
    await prisma.$executeRawUnsafe(`
      DROP TABLE IF EXISTS "public2"."analytics_5_dimensions" CASCADE
    `);
    
    // Create new table with all dimensions and metrics
    await prisma.$executeRawUnsafe(`
      CREATE TABLE "public2"."analytics_5_dimensions" (
        -- Dimensions
        "week_start_date" DATE NOT NULL,
        "client" VARCHAR(255) NOT NULL,
        "funnel" VARCHAR(10) NOT NULL,
        "email_provider" VARCHAR(100) NOT NULL,
        "domain_type" VARCHAR(20) NOT NULL,
        "sp" VARCHAR(10) NOT NULL,
        
        -- Email sent metrics (total)
        "email_1_sent_count" INTEGER DEFAULT 0,
        "email_2_sent_count" INTEGER DEFAULT 0,
        "email_3_sent_count" INTEGER DEFAULT 0,
        
        -- Email 1 by prospect type
        "email_1_sent_personal" INTEGER DEFAULT 0,
        "email_1_sent_role" INTEGER DEFAULT 0,
        "email_1_sent_work" INTEGER DEFAULT 0,
        "email_1_sent_unknown" INTEGER DEFAULT 0,
        
        -- Email 2 by prospect type
        "email_2_sent_personal" INTEGER DEFAULT 0,
        "email_2_sent_role" INTEGER DEFAULT 0,
        "email_2_sent_work" INTEGER DEFAULT 0,
        "email_2_sent_unknown" INTEGER DEFAULT 0,
        
        -- Email 3 by prospect type
        "email_3_sent_personal" INTEGER DEFAULT 0,
        "email_3_sent_role" INTEGER DEFAULT 0,
        "email_3_sent_work" INTEGER DEFAULT 0,
        "email_3_sent_unknown" INTEGER DEFAULT 0,
        
        -- Reply metrics
        "replies_after_email_1" INTEGER DEFAULT 0,
        "replies_after_email_2" INTEGER DEFAULT 0,
        "replies_after_email_3" INTEGER DEFAULT 0,
        
        -- Automated reply metrics
        "automated_replies_after_email_1" INTEGER DEFAULT 0,
        "automated_replies_after_email_2" INTEGER DEFAULT 0,
        "automated_replies_after_email_3" INTEGER DEFAULT 0,
        
        -- Error reply metrics
        "error_replies_after_email_1" INTEGER DEFAULT 0,
        "error_replies_after_email_2" INTEGER DEFAULT 0,
        "error_replies_after_email_3" INTEGER DEFAULT 0,
        
        -- Meeting metrics
        "meeting_slots_sent_after_1" INTEGER DEFAULT 0,
        "meeting_slots_sent_after_2" INTEGER DEFAULT 0,
        "meeting_slots_sent_after_3" INTEGER DEFAULT 0,
        "meeting_slots_sent_unknown" INTEGER DEFAULT 0,
        "meetings_booked" INTEGER DEFAULT 0,
        
        -- Additional metrics
        "total_prospects_reached" INTEGER DEFAULT 0,
        
        -- Metadata
        "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- Primary key on all dimensions
        PRIMARY KEY ("week_start_date", "client", "funnel", "email_provider", "domain_type", "sp")
      )
    `);
    
    console.log('   ✅ Table created successfully');
    
    // Create indexes for faster queries
    console.log('📇 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX idx_5d_client ON "public2"."analytics_5_dimensions" ("client")',
      'CREATE INDEX idx_5d_funnel ON "public2"."analytics_5_dimensions" ("funnel")',
      'CREATE INDEX idx_5d_sp ON "public2"."analytics_5_dimensions" ("sp")',
      'CREATE INDEX idx_5d_provider ON "public2"."analytics_5_dimensions" ("email_provider")',
      'CREATE INDEX idx_5d_domain ON "public2"."analytics_5_dimensions" ("domain_type")',
      'CREATE INDEX idx_5d_week ON "public2"."analytics_5_dimensions" ("week_start_date")',
      'CREATE INDEX idx_5d_client_funnel ON "public2"."analytics_5_dimensions" ("client", "funnel")',
      'CREATE INDEX idx_5d_week_client ON "public2"."analytics_5_dimensions" ("week_start_date", "client")'
    ];
    
    for (const index of indexes) {
      try {
        await prisma.$executeRawUnsafe(index);
        console.log(`   ✅ ${index.match(/idx_5d_\w+/)[0]} created`);
      } catch (e) {
        console.log(`   ⚠️  ${index.match(/idx_5d_\w+/)[0]} already exists or failed`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('   ❌ Error creating table:', error.message);
    throw error;
  }
}

/**
 * Load processed data into the table
 */
async function loadProcessedData() {
  console.log('\n📥 Loading processed data into analytics table...');
  
  try {
    // Read processed aggregations
    const aggregationsPath = path.join(dataDir, 'processed_aggregations.json');
    
    if (!fs.existsSync(aggregationsPath)) {
      console.error('   ❌ Processed aggregations not found. Run step 1 first.');
      return false;
    }
    
    const aggregations = JSON.parse(fs.readFileSync(aggregationsPath, 'utf8'));
    console.log(`   Found ${aggregations.length} aggregations to load`);
    
    // Clear existing data
    console.log('   Clearing existing data...');
    await prisma.$executeRawUnsafe(`
      TRUNCATE TABLE "public2"."analytics_5_dimensions"
    `);
    
    // Insert in batches
    const BATCH_SIZE = 100;
    let inserted = 0;
    
    for (let i = 0; i < aggregations.length; i += BATCH_SIZE) {
      const batch = aggregations.slice(i, i + BATCH_SIZE);
      
      // Build insert values
      const values = [];
      const placeholders = [];
      let paramIndex = 1;
      
      for (const agg of batch) {
        const rowPlaceholders = [];
        
        // Add all values in order
        const rowValues = [
          agg.week_start_date,
          agg.client,
          agg.funnel,
          agg.email_provider,
          agg.domain_type,
          agg.sp,
          
          // Metrics
          agg.email_1_sent_count || 0,
          agg.email_2_sent_count || 0,
          agg.email_3_sent_count || 0,
          
          agg.email_1_sent_personal || 0,
          agg.email_1_sent_role || 0,
          agg.email_1_sent_work || 0,
          agg.email_1_sent_unknown || 0,
          
          agg.email_2_sent_personal || 0,
          agg.email_2_sent_role || 0,
          agg.email_2_sent_work || 0,
          agg.email_2_sent_unknown || 0,
          
          agg.email_3_sent_personal || 0,
          agg.email_3_sent_role || 0,
          agg.email_3_sent_work || 0,
          agg.email_3_sent_unknown || 0,
          
          agg.replies_after_email_1 || 0,
          agg.replies_after_email_2 || 0,
          agg.replies_after_email_3 || 0,
          
          agg.automated_replies_after_email_1 || 0,
          agg.automated_replies_after_email_2 || 0,
          agg.automated_replies_after_email_3 || 0,
          
          agg.error_replies_after_email_1 || 0,
          agg.error_replies_after_email_2 || 0,
          agg.error_replies_after_email_3 || 0,
          
          agg.meeting_slots_sent_after_1 || 0,
          agg.meeting_slots_sent_after_2 || 0,
          agg.meeting_slots_sent_after_3 || 0,
          agg.meeting_slots_sent_unknown || 0,
          agg.meetings_booked || 0,
          
          agg.total_prospects_reached || 0
        ];
        
        // Create placeholders for this row
        for (let j = 0; j < rowValues.length; j++) {
          rowPlaceholders.push(`$${paramIndex++}`);
        }
        
        values.push(...rowValues);
        placeholders.push(`(${rowPlaceholders.join(', ')})`);
      }
      
      // Execute batch insert
      const insertQuery = `
        INSERT INTO "public2"."analytics_5_dimensions" (
          "week_start_date", "client", "funnel", "email_provider", "domain_type", "sp",
          "email_1_sent_count", "email_2_sent_count", "email_3_sent_count",
          "email_1_sent_personal", "email_1_sent_role", "email_1_sent_work", "email_1_sent_unknown",
          "email_2_sent_personal", "email_2_sent_role", "email_2_sent_work", "email_2_sent_unknown",
          "email_3_sent_personal", "email_3_sent_role", "email_3_sent_work", "email_3_sent_unknown",
          "replies_after_email_1", "replies_after_email_2", "replies_after_email_3",
          "automated_replies_after_email_1", "automated_replies_after_email_2", "automated_replies_after_email_3",
          "error_replies_after_email_1", "error_replies_after_email_2", "error_replies_after_email_3",
          "meeting_slots_sent_after_1", "meeting_slots_sent_after_2", "meeting_slots_sent_after_3",
          "meeting_slots_sent_unknown", "meetings_booked", "total_prospects_reached"
        )
        VALUES ${placeholders.join(', ')}
        ON CONFLICT ("week_start_date", "client", "funnel", "email_provider", "domain_type", "sp")
        DO UPDATE SET
          "email_1_sent_count" = EXCLUDED."email_1_sent_count",
          "email_2_sent_count" = EXCLUDED."email_2_sent_count",
          "email_3_sent_count" = EXCLUDED."email_3_sent_count",
          "updated_at" = CURRENT_TIMESTAMP
      `;
      
      await prisma.$executeRawUnsafe(insertQuery, ...values);
      inserted += batch.length;
      
      console.log(`   Inserted ${inserted}/${aggregations.length} rows...`);
    }
    
    console.log(`   ✅ Successfully loaded ${inserted} rows`);
    
    // Verify data
    const count = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM "public2"."analytics_5_dimensions"
    `;
    
    console.log(`\n📊 Verification: ${count[0].count} rows in table`);
    
    // Show sample metrics
    const sample = await prisma.$queryRaw`
      SELECT 
        COUNT(DISTINCT "client") as unique_clients,
        COUNT(DISTINCT "funnel") as unique_funnels,
        COUNT(DISTINCT "sp") as unique_sps,
        COUNT(DISTINCT "email_provider") as unique_providers,
        COUNT(DISTINCT "domain_type") as unique_domains,
        SUM("email_1_sent_count") as total_email_1,
        SUM("email_2_sent_count") as total_email_2,
        SUM("email_3_sent_count") as total_email_3,
        SUM("replies_after_email_1" + "replies_after_email_2" + "replies_after_email_3") as total_replies,
        SUM("meetings_booked") as total_booked
      FROM "public2"."analytics_5_dimensions"
    `;
    
    console.log('\n📈 Summary Statistics:');
    console.log(`   Unique clients: ${sample[0].unique_clients}`);
    console.log(`   Unique funnels: ${sample[0].unique_funnels}`);
    console.log(`   Unique SPs: ${sample[0].unique_sps}`);
    console.log(`   Unique providers: ${sample[0].unique_providers}`);
    console.log(`   Unique domain types: ${sample[0].unique_domains}`);
    console.log(`   Total Email 1 sent: ${sample[0].total_email_1}`);
    console.log(`   Total Email 2 sent: ${sample[0].total_email_2}`);
    console.log(`   Total Email 3 sent: ${sample[0].total_email_3}`);
    console.log(`   Total replies: ${sample[0].total_replies}`);
    console.log(`   Total meetings booked: ${sample[0].total_booked}`);
    
    return true;
  } catch (error) {
    console.error('   ❌ Error loading data:', error.message);
    throw error;
  }
}

/**
 * Main execution
 */
async function createAndLoadTables() {
  console.log('🚀 Starting table creation and data loading\n');
  
  try {
    await createAnalyticsTable();
    await loadProcessedData();
    
    console.log('\n✅ Table creation and data loading complete!');
    return true;
  } catch (error) {
    console.error('\n❌ Fatal error:', error);
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  createAndLoadTables()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { createAnalyticsTable, loadProcessedData, createAndLoadTables };