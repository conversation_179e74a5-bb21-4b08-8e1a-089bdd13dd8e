/**
 * Fetch emails/inboxes data from Replit to understand structure
 */

const https = require('https');
const fs = require('fs');

const REPLIT_URL = 'jeffdb.replit.app';

function fetchFromReplit(endpoint) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: REPLIT_URL,
      path: endpoint,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function analyzeEmailsData() {
  console.log('Fetching emails/inboxes data from Replit...\n');
  
  try {
    const emails = await fetchFromReplit('/emails');
    
    console.log(`Total email accounts: ${emails.length}\n`);
    
    // Sample first 3 emails for structure
    console.log('=== SAMPLE EMAIL/INBOX DATA (First 3) ===\n');
    emails.slice(0, 3).forEach((email, i) => {
      console.log(`\n--- Email ${i + 1} ---`);
      console.log(JSON.stringify(email, null, 2));
    });
    
    // Analyze structure
    console.log('\n\n=== FIELD ANALYSIS ===\n');
    
    // Get all unique fields
    const allFields = new Set();
    emails.forEach(email => {
      Object.keys(email).forEach(key => allFields.add(key));
    });
    
    console.log('All fields found:');
    Array.from(allFields).sort().forEach(field => {
      console.log(`  - ${field}`);
    });
    
    // Analyze key fields for our dimensions
    console.log('\n=== KEY FIELDS FOR ANALYTICS ===\n');
    
    // 1. Email Provider Analysis
    console.log('1. EMAIL PROVIDER DIMENSION:');
    const providerTypes = {};
    emails.forEach(email => {
      const type = email.type || 'UNKNOWN';
      providerTypes[type] = (providerTypes[type] || 0) + 1;
    });
    console.log('  Provider types distribution:');
    Object.entries(providerTypes).forEach(([type, count]) => {
      console.log(`    - ${type}: ${count} (${(count/emails.length*100).toFixed(1)}%)`);
    });
    
    // Check for detailed provider info
    console.log('\n  Detailed provider info (first 10 with domain.currentProvider):');
    emails.filter(e => e.domain?.currentProvider).slice(0, 10).forEach(email => {
      console.log(`    - ${email.email} → ${email.domain.currentProvider.name || email.domain.currentProvider}`);
    });
    
    // 2. Domain Type Analysis
    console.log('\n2. DOMAIN TYPE DIMENSION:');
    const domainTypes = {};
    emails.forEach(email => {
      if (email.email) {
        const domain = email.email.split('@')[1];
        if (domain) {
          const tld = domain.substring(domain.lastIndexOf('.'));
          domainTypes[tld] = (domainTypes[tld] || 0) + 1;
        }
      }
    });
    console.log('  Top 10 TLDs:');
    Object.entries(domainTypes)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([tld, count]) => {
        console.log(`    - ${tld}: ${count} (${(count/emails.length*100).toFixed(1)}%)`);
      });
    
    // 3. Client mapping
    console.log('\n3. CLIENT MAPPING:');
    const clientIds = new Set();
    emails.forEach(email => {
      if (email.clientId) clientIds.add(email.clientId);
    });
    console.log(`  Unique client IDs: ${clientIds.size}`);
    console.log('  Sample client IDs:', Array.from(clientIds).slice(0, 5));
    
    // 4. SmartLead ID for joining
    console.log('\n4. SMARTLEAD ID (for joining):');
    const hasSmartleadId = emails.filter(e => e.smartleadId).length;
    console.log(`  Emails with smartleadId: ${hasSmartleadId} (${(hasSmartleadId/emails.length*100).toFixed(1)}%)`);
    
    // 5. Check for additional useful fields
    console.log('\n5. ADDITIONAL FIELDS:');
    const sampleEmail = emails.find(e => e.domain?.currentProvider) || emails[0];
    if (sampleEmail.domain) {
      console.log('  Domain object structure:');
      console.log(JSON.stringify(sampleEmail.domain, null, 4));
    }
    
    // Save sample data
    fs.writeFileSync('data/emails_sample.json', JSON.stringify(emails.slice(0, 10), null, 2));
    console.log('\n✅ Sample data saved to data/emails_sample.json');
    
    // Summary for mapping
    console.log('\n=== MAPPING SUMMARY ===\n');
    console.log('For Email Provider Dimension:');
    console.log('  - Primary field: email.type (GMAIL/SMTP/OUTLOOK)');
    console.log('  - Secondary field: email.domain.currentProvider.name');
    console.log('\nFor Domain Type Dimension:');
    console.log('  - Extract from: email.email (split by @, get TLD)');
    console.log('  - Classify TLDs into: standard (.com/.net/.org), alternative, other');
    console.log('\nFor Joining with Meetings:');
    console.log('  - Join field: email.email matches meetings.emailHistory[].from');
    console.log('\nFor Client Mapping:');
    console.log('  - Use: email.clientId to verify client ownership');
    
  } catch (error) {
    console.error('Error fetching emails data:', error);
  }
}

analyzeEmailsData();