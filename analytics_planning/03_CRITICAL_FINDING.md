# CRITICAL FINDING: How Current Analytics Works

## The Missing Link: Query 277

Query 277 ("Smartlead eMails with Custom Reply Status") is the foundation of the entire analytics system. Here's what it does:

### 1. Base Table
- Uses `mv_email_with_thread_start` as the foundation

### 2. Key Joins
```sql
-- Join with Campaign table for campaign info
LEFT JOIN "public"."Campaign" ON mv_email."campaingId" = "Campaign"."campaignId"

-- Join with Client table for client info  
LEFT JOIN "public"."Client" ON "Campaign"."clientId" = "Client"."clientId"

-- JOIN WITH QUERY 271 FOR SP DATA!
LEFT JOIN {{#271-seller-group-sellers-with-sp-classifications-prospects-w-emails}} AS "Prospects - email_type" 
  ON mv_email."toEmailID" = "Prospects - email_type"."email"
```

### 3. What Query 277 Provides

#### Dimensions:
1. **Client**: `jeff_client_name` (from Client table join)
2. **Campaign Code**: Extracted using regex from campaign name
3. **SP Data**: `jeff_search_priority` and `dominant_search_priority` (from Query 271 join)
4. **Email Type**: `prospect_email_type` (from Query 271)

#### Custom Email Status Classification:
```sql
CASE
  WHEN type = 'REPLY' THEN
    -- Complex logic to classify as:
    -- REPLY_CS_AUTOMATED (customer service auto-replies)
    -- ERROR_REPLY (bounce/error messages)
    -- REPLY (genuine replies)
END AS jeff_email_status
```

## The Complete Data Flow

```
Query 271 (SP Data from Metabase) ──┐
                                     │
mv_email_with_thread_start ─────────┼──→ Query 277 (Base with SP)
                                     │         │
Campaign Table ─────────────────────┤         ├──→ Query 278 (Funnel/Client Analytics)
                                     │         ├──→ Query 289 (Tag Analytics)
Client Table ────────────────────────┘         └──→ Query 313 (Provider Analytics)
```

## Why ProviderAnalytics and TagAnalytics Tables Aren't Used

**They're redundant!** The dashboard queries don't use these tables because:

1. Query 277 already joins all the necessary data
2. Downstream queries (278, 289, 313) aggregate from Query 277
3. The tables created by generateAnalytics.sh are just cached versions of what these queries calculate

## What This Means for Our New System

### Option 1: Replicate Current Approach
- Keep using Query 271 for SP data
- Join with mv_email_with_thread_start
- Create aggregations as needed

### Option 2: Build Integrated MV (Recommended)
- Create new MV that includes SP data directly
- Avoid dependency on Query 271 (which could change/break)
- Pre-calculate all 5 dimensions in the MV

### Option 3: Fix Existing Tables
- Make FunnelClientAnalytics include SP dimension
- Fix MeetingsFromReplit to preserve SP data
- Use these tables as intended

## Key Insights

1. **SP data is already being joined** - Query 277 shows exactly how to connect SP to emails (via toEmailID)

2. **Email Provider is missing** - Current system doesn't have email provider dimension

3. **Domain Type is missing** - Current system doesn't classify domain types

4. **The 4 dashboard filters don't work** because they're not mapped to any cards

5. **Most "analytics" queries are just views** on Query 277, not using the pre-calculated tables

## Our 5 Dimensions Mapping

| Dimension | Current Source | Our Approach |
|-----------|---------------|--------------|
| Client | Client table join | ✅ Same |
| Funnel | Campaign name regex | ✅ Same |
| Email Provider | ❌ Missing | Need to add from inbox/domain |
| Domain Type | ❌ Missing | Need to classify TLDs |
| SP | Query 271 join | ✅ Can use same approach |

## Next Steps

1. Decide which approach to take (Option 1, 2, or 3)
2. Map email provider data source
3. Implement domain type classification
4. Create the new analytics system with all 5 dimensions