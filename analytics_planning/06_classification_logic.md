# Step 6: Complete Classification Logic from Query 277 & 278

## 1. Email Status Classification (jeff_email_status)

From Query 277, emails are classified into:

### REPLY_CS_AUTOMATED (Customer Service Auto-replies)
Detected when messageId contains:
- zendesk
- freshdesk
- helpscout
- hubspot
- intercom

OR when body contains:
- "is an automated message"
- "important to us"
- "will get back to you as quickly as possible"
- "customer service"
- "auto message"
- "customer care"
- "will get back to you within"
- "auto-reply"
- "for your patience"
- "thank you for your request"
- "business hours are"
- "received your request."
- "hear back from us within"
- "thank you for contacting"
- "thank you for reaching"
- "support experience"
- "support team"

### ERROR_REPLY (Bounce/Error Messages)
Detected when:
1. messageId contains "mx.google.com"
2. messageId contains "prod.outlook.com" or "exchangelabs.com" AND body contains:
   - "fail"
   - "failure"
   - "error"
   - "action required recipient unknown"
   - "your message to"
   - "rejected"
3. Body contains:
   - "automatically by mail delivery software"
   - "delivery has failed"
   - "created automatically by mail delivery software"
   - "your message could not be delivered"
   - "message could not be delivered"
   - "recipient address rejected"
   - ("mail system" AND "rejected")

### REPLY (Genuine Replies)
Any reply that doesn't match the above patterns

### FORWARD/SENT
Kept as-is from the type field

## 2. Prospect Email Type Classification

From Query 278, prospect emails are classified into 4 types:
- **personal** - Personal email addresses
- **role** - Role-based emails (e.g., info@, support@)
- **work** - Work email addresses
- **unknown** - NULL or empty prospect_email_type

## 3. Campaign/Funnel Code Extraction

From Query 277:
```sql
CASE 
  WHEN SUBSTRING(campaign_name FROM '[0-9]{1,2}[A-Z]') IS NOT NULL 
    THEN SUBSTRING(campaign_name FROM '[0-9]{1,2}[A-Z]')
  WHEN parentCampaignId IS NOT NULL 
    THEN 'HKP'
  ELSE 'UNK'
END AS jeff_campaign_code
```

Examples:
- "AMZ Ads Success 5B - 2" → "5B"
- "DTC Retail Success 10B" → "10B"
- Campaign with parentCampaignId → "HKP"
- No pattern match → "UNK"

## 4. Complete Metric Breakdown (32 Metrics)

### Email Sent Metrics (12 metrics)
By email sequence (1, 2, 3) and prospect type (personal, role, work, unknown):

```sql
-- Email 1
email_1_sent_personal_count
email_1_sent_role_count
email_1_sent_work_count
email_1_sent_unknown_count

-- Email 2
email_2_sent_personal_count
email_2_sent_role_count
email_2_sent_work_count
email_2_sent_unknown_count

-- Email 3
email_3_sent_personal_count
email_3_sent_role_count
email_3_sent_work_count
email_3_sent_unknown_count
```

### Reply Metrics (9 metrics)
By email sequence and reply type:

```sql
-- Genuine replies
replies_after_email_1_count
replies_after_email_2_count
replies_after_email_3_count

-- Automated replies
automated_replies_after_email_1_count
automated_replies_after_email_2_count
automated_replies_after_email_3_count

-- Error replies
error_replies_after_email_1_count
error_replies_after_email_2_count
error_replies_after_email_3_count
```

### Meeting Metrics (6 metrics)
```sql
meeting_slots_sent_after_1
meeting_slots_sent_after_2
meeting_slots_sent_after_3
meeting_slots_sent_unknown
total_meeting_slots_sent
meetings_booked
```

### Total Counts (5 metrics)
```sql
email_1_sent_count (total)
email_2_sent_count (total)
email_3_sent_count (total)
total_emails_sent
total_prospects_reached (unique leadIds)
```

## 5. Attribution Logic

From Query 278, replies are attributed to email sequences using:

```sql
-- First, identify which emails were sent to each lead
lead_sequences AS (
  SELECT 
    leadId,
    MAX(email_1_sent) AS has_email_1_sent,
    MAX(email_2_sent) AS has_email_2_sent,
    MAX(email_3_sent) AS has_email_3_sent
  FROM email_analytics_base
  GROUP BY leadId
)

-- Then attribute replies based on what was sent
SUM(CASE WHEN is_reply = 1 AND has_email_1_sent = 1 THEN 1 ELSE 0 END) AS replies_after_email_1
```

## 6. JavaScript Implementation Pattern

```javascript
// Email status classification
function classifyEmailStatus(email) {
  if (email.type === 'SENT' || email.type === 'FORWARD') {
    return email.type;
  }
  
  if (email.type === 'REPLY') {
    const messageId = (email.messageId || '').toLowerCase();
    const body = (email.body || '').toLowerCase();
    
    // Check for CS automated
    const csKeywords = [
      'zendesk', 'freshdesk', 'helpscout', 'hubspot', 'intercom'
    ];
    const csBodyPhrases = [
      'is an automated message', 'important to us', 
      'will get back to you as quickly as possible',
      'customer service', 'auto message', 'customer care',
      // ... etc
    ];
    
    if (csKeywords.some(kw => messageId.includes(kw)) ||
        csBodyPhrases.some(phrase => body.includes(phrase))) {
      return 'REPLY_CS_AUTOMATED';
    }
    
    // Check for errors
    if (messageId.includes('mx.google.com')) {
      return 'ERROR_REPLY';
    }
    
    // ... more error checks
    
    return 'REPLY';
  }
  
  return null;
}

// Funnel code extraction
function extractFunnelCode(campaignName, parentCampaignId) {
  const match = campaignName.match(/(\d{1,2}[A-Z])/);
  if (match) return match[1];
  if (parentCampaignId) return 'HKP';
  return 'UNK';
}

// Prospect email type (from Query 271)
function getProspectEmailType(email) {
  // This comes from Query 271 join
  return email.prospect_email_type || 'unknown';
}
```

## 7. Data Flow for New System

```
Email Table
  ├── Join Query 271 (via toEmailID) → SP + prospect_email_type
  ├── Join InboxesFromReplit (via fromEmailID) → Provider + Domain
  ├── Join Campaign (via campaignId) → Extract funnel code
  ├── Join Client (via Campaign.clientId) → Client name
  └── Apply classification logic → jeff_email_status

Then aggregate by:
- 5 Dimensions: Client, Funnel, Provider, Domain, SP
- 32 Metrics: As listed above
- Time: By week_start_date
```

## Key Points for Implementation

1. **All classifications happen at query time** - not stored in database
2. **Case-insensitive matching** - Always use LOWER() or toLowerCase()
3. **Attribution requires tracking** what emails were sent to each lead
4. **COALESCE to 'UNK'** for missing dimensional data
5. **Performance**: Process in batches by date range to avoid timeouts