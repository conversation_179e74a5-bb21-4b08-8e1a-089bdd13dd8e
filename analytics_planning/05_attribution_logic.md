# Step 5: Attribution Logic & Field Mappings

## Attribution Logic for Meetings

### Key Finding: No Direct "funnel" Field
- Meetings data does NOT have a dedicated "funnel" field
- Funnel code is embedded in `campaignName` (e.g., "AMZ Ads Success 5B - 2")
- Need to extract using regex pattern like in Query 277

## Complete Attribution System

### 1. Email Sequence Attribution
Based on the meeting data structure:

```javascript
// From meeting JSON:
emailHistory: [
  { email_seq_number: "1", time: "2025-09-15T14:04:01.342Z", type: "SENT" },
  { email_seq_number: "1", time: "2025-09-15T14:16:16.000Z", type: "REPLY" },
  { email_seq_number: null, time: "2025-09-15T18:41:06.879Z", type: "SENT" }
]

stageHistory: [
  { stage: "slots_sent", changedAt: "2025-09-15T18:44:20.178Z" }
]
```

**Attribution Logic**:
1. Find the MAX `email_seq_number` from emailHistory before the stage change
2. Attribute the stage change to that email sequence
3. In this example: slots_sent at 18:44 → Attributed to Email 2 (sent at 18:41)

### 2. Field Mappings for 5 Dimensions

#### Dimension 1: Client
```
meetings.clientId → Client.clientId
meetings.client.smartleadId → Client.smartleadId
meetings.client.businessName → jeff_client_name
```

#### Dimension 2: Funnel
```
meetings.campaignName → Extract using regex
Pattern: /(\d+[A-Z])/
Example: "AMZ Ads Success 5B - 2" → "5B"
Default: 'UNK' if no pattern match
```

#### Dimension 3: Email Provider
```
meetings.emailHistory[].from → Inbox.email → Inbox.type
First SENT email's 'from' field → Maps to inbox email → Get provider type
Example: "<EMAIL>" → GMAIL/SMTP/OUTLOOK
```

#### Dimension 4: Domain Type
```
meetings.emailHistory[].from → Extract domain → Classify TLD
Example: "<EMAIL>" → ".com" → "standard"
Classification:
- .com, .net, .org → "standard"
- .click, .top, .site → "alternative"
- Others → "other"
```

#### Dimension 5: Search Priority (SP)
```
meetings.contactEmail → Query271.email → Query271.dominant_search_priority
Example: "<EMAIL>" → "SP3A" (from Query 271)
Default: 'UNK' if not found in Query 271
```

## Attribution Timing Rules

### For slots_sent:
1. Look at all SENT emails before `slotSentDate`
2. Find the highest `email_seq_number` 
3. Attribute to that email (1, 2, or 3)

### For booked:
1. Look at all SENT emails before `bookingDate`
2. Find the highest `email_seq_number`
3. Attribute to that email (1, 2, or 3)

## Current Distribution (from existing data):
- 51.7% slots_sent after Email 1
- 21.1% slots_sent after Email 2  
- 9.2% slots_sent after Email 3
- 17.9% no attribution (no email_seq_number)

## SQL Implementation Pattern

```sql
-- Get attribution for each meeting
WITH email_attribution AS (
  SELECT 
    meeting_id,
    MAX(CAST(email_seq_number AS INT)) as attributed_to_email
  FROM emailHistory
  WHERE 
    type = 'SENT' 
    AND time < stage_change_time
    AND email_seq_number IS NOT NULL
  GROUP BY meeting_id
)

-- Join with dimensions
SELECT
  -- Client dimension
  COALESCE(c.businessName, 'UNK') as client,
  
  -- Funnel dimension  
  COALESCE(
    SUBSTRING(campaignName FROM '(\d+[A-Z])'),
    'UNK'
  ) as funnel,
  
  -- Email Provider dimension
  COALESCE(i.type, 'UNK') as email_provider,
  
  -- Domain Type dimension
  CASE 
    WHEN domain LIKE '%.com' OR domain LIKE '%.net' OR domain LIKE '%.org' 
      THEN 'standard'
    WHEN domain LIKE '%.click' OR domain LIKE '%.top' OR domain LIKE '%.site'
      THEN 'alternative'  
    ELSE 'other'
  END as domain_type,
  
  -- SP dimension
  COALESCE(sp.dominant_search_priority, 'UNK') as sp,
  
  -- Attribution
  attributed_to_email,
  
  -- Metrics
  COUNT(*) as meetings_count
  
FROM meetings m
LEFT JOIN Client c ON m.clientId = c.clientId
LEFT JOIN Inbox i ON m.sender_email = i.email
LEFT JOIN Query271 sp ON m.contactEmail = sp.email
LEFT JOIN email_attribution ea ON m.id = ea.meeting_id
GROUP BY 1,2,3,4,5,6
```

## Data Flow for New System

```
Meetings (Replit API)
    ├── contactEmail → Query 271 → SP dimension
    ├── emailHistory[].from → Inbox API → Provider dimension
    ├── campaignName → Regex → Funnel dimension
    ├── clientId → Client table → Client dimension
    └── emailHistory + stageHistory → Attribution logic

Email Table (Database)
    ├── toEmailID → Query 271 → SP dimension
    ├── fromEmailID → Inbox data → Provider dimension
    ├── campaignId → Campaign table → Funnel dimension
    └── email_seq_number → Attribution
```

## Next Step (Step 6)
Implement the complete solution with:
1. Fetch all data sources
2. Apply mappings and transformations
3. Create new MV with 5 dimensions
4. Generate aggregated analytics tables