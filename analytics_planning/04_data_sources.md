# Step 4: Data Sources Analysis

## 1. Replit Data Sources

### 1.1 Meetings Data (/meetings endpoint)
**Total Records**: 985 meetings

#### Key Fields for Our Analytics:

1. **For Mapping to Prospects (to get SP)**:
   - `contactEmail` - The prospect's email (maps to toEmailID in Email table)
   - `leadId` - Can also use for joining
   - ❌ NOT using `jeffSearchPriority` from meetings (as instructed)

2. **For Mapping to Inboxes (to get Email Provider)**:
   - `emailHistory[].from` - The sender email (maps to fromEmailID)
   - We extract the earliest SENT email's 'from' field

3. **For Client/Campaign Mapping**:
   - `clientId` - SmartLead client ID (maps to Client table)
   - `campaignId` - SmartLead campaign ID (maps to Campaign table)
   - `client.smartleadId` - Another way to get client ID
   - `campaignName` - Has the funnel code embedded (e.g., "DTC Retail Success 10B")

4. **For Attribution (Email Sequence)**:
   - `emailHistory[].email_seq_number` - Which email (1, 2, or 3) led to action
   - `stageHistory[].stage` - 'slots_sent' or 'booked'
   - `stageHistory[].changedAt` - When the stage changed (date)
   
   Current attribution logic:
   - Finds MAX email_seq_number from emailHistory
   - Attributes slots_sent to that email sequence
   - Distribution: 51.7% after Email 1, 21.1% after Email 2, 9.2% after Email 3

### 1.2 Emails/Inboxes Data (/emails endpoint)
**Total Records**: 2,379 email accounts

#### Key Fields for Our Analytics:

1. **For Email Provider Dimension**:
   - `type` - GMAIL (49.4%), SMTP (32.5%), OUTLOOK (18.1%)
   - `domain.currentProvider.name` - Detailed provider (Maildoso, ZapMail, etc.)
   - `email` - The inbox email address

2. **For Domain Type Classification**:
   - `email` - Extract domain from email address
   - Can classify TLD (.com, .click, .top, etc.)
   - 690 unique domains

3. **For Joining**:
   - `smartleadId` - Unique ID in SmartLead
   - `clientId` - Which client owns this inbox

## 2. Database Tables (via Metabase/Prisma)

### 2.1 Core Tables We'll Use

#### Email Table (Raw SmartLead data)
- Contains all sent/received emails
- Key fields:
  - `id` - Unique email ID
  - `toEmailID` - Recipient email (for SP join)
  - `fromEmailID` - Sender email (for provider join)
  - `campaingId` - Campaign ID (note the typo)
  - `email_seq_number` - Email sequence (1, 2, 3)
  - `type` - SENT, REPLY, ERROR, etc.
  - `time` - Timestamp
  - `body` - Email content
  - `subject` - Email subject

#### mv_email_with_thread_start (Materialized View)
- Pre-calculated email threads
- Same fields as Email table
- Better performance for queries
- Currently missing SP dimension

#### Campaign Table
- `campaignId` - SmartLead campaign ID
- `name` - Campaign name (contains funnel code)
- `clientId` - Links to Client table
- `parentCampaignId` - For hierarchical campaigns

#### Client Table  
- `clientId` - SmartLead client ID
- `name` - Person name
- `businessName` - Company/client name (jeff_client_name)

### 2.2 Analytics Tables (Created by generateAnalytics.sh)

#### FunnelClientAnalytics ✅ USED
- Pre-aggregated metrics by week, campaign, client
- Has all 32 metrics we need
- Missing: SP dimension, email provider, domain type

#### MeetingsFromReplit ✅ USED (but flawed)
- Aggregated by date|client|funnel|fromEmailId
- Has attribution (slots_sent_after_1/2/3)
- Missing: SP data (lost during aggregation)
- Missing: Individual meeting details

## 3. External Data Sources

### Query 271 (SP Data)
- 170K+ records with Search Priority
- Fields:
  - `email` (or "Prospect → Email") - For joining
  - `dominant_search_priority` - SP1-SP8
  - `Seller Group ID` - Lead/seller ID

### Query 277 (Base Analytics Query)
- Joins mv_email_with_thread_start with Query 271
- Adds custom email status classification
- Foundation for most dashboard queries

## Data Volumes Summary

| Source | Records | Update Frequency |
|--------|---------|------------------|
| Meetings (Replit) | 985 | Daily |
| Emails/Inboxes (Replit) | 2,379 | Daily |
| Email Table | Millions | Real-time |
| Query 271 (SP) | 170K+ | Daily? |
| FunnelClientAnalytics | 2-3K | Daily |
| MeetingsFromReplit | ~1K aggregated | Daily |

## What's Missing

1. **Email Provider mapping** - Need to join emails with inbox data
2. **Domain Type classification** - Need to implement TLD categorization
3. **SP in main flow** - Currently only in Query 271, not in tables
4. **Proper attribution** - Current system loses individual meeting data

## Next Step (Step 5)
Map how these data sources connect to create our 5 dimensions:
- Client → Client table via smartleadId
- Funnel → Extract from Campaign name
- Email Provider → Join with /emails data via fromEmailID
- Domain Type → Classify from email domain
- SP → Join with Query 271 via toEmailID (prospect email)