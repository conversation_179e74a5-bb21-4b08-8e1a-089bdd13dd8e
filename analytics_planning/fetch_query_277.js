/**
 * Fetch Query 277 - The critical base query
 */

const https = require('https');
const fs = require('fs');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: path,
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function fetchQuery277() {
  console.log('Fetching Query 277 - The critical base query...\n');
  
  const query = await makeRequest('/api/card/277');
  
  console.log('Query Name:', query.name);
  console.log('Type:', query.query_type);
  console.log('Collection:', query.collection?.name || 'Root');
  console.log('Created:', query.created_at);
  console.log('Updated:', query.updated_at);
  console.log();
  
  // Check if it's native SQL
  if (query.dataset_query?.native?.query) {
    const sql = query.dataset_query.native.query;
    console.log('SQL Query Length:', sql.length, 'characters');
    console.log('\n=== SQL QUERY ===\n');
    console.log(sql);
    
    // Save to file
    fs.writeFileSync('query_277.sql', sql);
    console.log('\n✅ SQL saved to query_277.sql');
    
    // Extract tables used
    const tables = new Set();
    const patterns = [
      /FROM\s+"?(\w+)"?/gi,
      /JOIN\s+"?(\w+)"?/gi,
      /"public2"\."(\w+)"/gi,
      /\s+(\w+)\s+(?:AS\s+)?\w+\s+(?:ON|WHERE)/gi
    ];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(sql)) !== null) {
        if (match[1] && !['SELECT', 'FROM', 'WHERE', 'AND', 'OR', 'ON', 'AS'].includes(match[1].toUpperCase())) {
          tables.add(match[1]);
        }
      }
    });
    
    console.log('\nTables referenced:');
    Array.from(tables).forEach(table => {
      console.log('  -', table);
    });
  }
  
  // Check if it's a GUI query
  if (query.dataset_query?.query) {
    console.log('GUI Query Structure:');
    console.log(JSON.stringify(query.dataset_query.query, null, 2));
    
    // Save to file
    fs.writeFileSync('query_277_structure.json', JSON.stringify(query.dataset_query, null, 2));
    console.log('\n✅ Query structure saved to query_277_structure.json');
  }
  
  // Save full query details
  fs.writeFileSync('query_277_full.json', JSON.stringify(query, null, 2));
  console.log('✅ Full query details saved to query_277_full.json');
}

fetchQuery277().catch(console.error);