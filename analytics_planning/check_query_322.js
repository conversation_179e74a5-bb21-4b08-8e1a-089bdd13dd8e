/**
 * Check what Query 322 actually uses by fetching from Metabase
 */

const https = require('https');

const METABASE_URL = 'metabase.equalcollective.com';
const API_KEY = 'mb_YwbdsJ+gngJYJC2cLN2J21gdlwtClwctlNhNKaXVl00=';

function fetchQuery() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: METABASE_URL,
      path: '/api/card/322',
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY
      }
    };
    
    https.get(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(e);
        }
      });
    }).on('error', reject);
  });
}

async function checkQuery322() {
  try {
    console.log('Fetching Query 322 details from Metabase...\n');
    
    // Fetch query details
    const query = await fetchQuery();
    
    console.log('Query Name:', query.name);
    console.log('Query Type:', query.query_type);
    console.log('Display:', query.display);
    
    // Check dataset_query
    if (query.dataset_query) {
      console.log('\nDataset Query Type:', query.dataset_query.type);
      
      // Check if it's a native SQL query
      if (query.dataset_query.native) {
        console.log('\nNative SQL Query:');
        console.log(query.dataset_query.native.query);
        
        // Check for table references
        const sql = query.dataset_query.native.query || '';
        console.log('\n=== TABLE USAGE CHECK ===');
        
        const tablesToCheck = [
          'ProviderAnalytics',
          'TagAnalytics',
          'FunnelClientAnalytics',
          'MeetingsFromReplit',
          'mv_email_with_thread_start'
        ];
        
        tablesToCheck.forEach(table => {
          if (sql.toLowerCase().includes(table.toLowerCase())) {
            console.log(`✅ Uses ${table}`);
          }
        });
      }
      
      // Check if it's a GUI query
      if (query.dataset_query.query) {
        console.log('\nGUI Query Structure:');
        console.log('Source Table ID:', query.dataset_query.query['source-table']);
        
        // Map table IDs
        const tableMapping = {
          99: 'FunnelClientAnalytics',
          94: 'MeetingsFromReplit',
          // Add more mappings if known
        };
        
        const sourceTable = query.dataset_query.query['source-table'];
        if (tableMapping[sourceTable]) {
          console.log(`✅ Uses table: ${tableMapping[sourceTable]}`);
        } else {
          console.log(`Uses Table ID: ${sourceTable} (unknown mapping)`);
        }
        
        // Check aggregations
        if (query.dataset_query.query.aggregation) {
          console.log('\nAggregations:', JSON.stringify(query.dataset_query.query.aggregation, null, 2));
        }
      }
    }
    
    // Check visualization settings
    if (query.visualization_settings) {
      console.log('\nVisualization Type:', query.display);
    }
    
  } catch (error) {
    console.error('Error fetching query:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

checkQuery322();