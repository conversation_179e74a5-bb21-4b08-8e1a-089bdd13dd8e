/**
 * Examples of using the LLM Factory
 * This demonstrates how to use the new LLM factory for various AI tasks
 */

const { 
  callLLM, 
  simpleCompletion, 
  systemUserCompletion, 
  getAvailableModels 
} = require('../services/llm/llmFactory');

// Example 1: Simple text completion
async function exampleSimpleCompletion() {
  console.log('=== Example 1: Simple Completion ===');
  
  const result = await simpleCompletion(
    "What is the capital of France?",
    "gpt-4o-mini"
  );
  
  console.log('Response:', result.content);
  console.log('Cost:', `$${result.usage.cost.toFixed(4)}`);
  console.log('Tokens:', `${result.usage.inputTokens} in, ${result.usage.outputTokens} out`);
}

// Example 2: System + User prompt
async function exampleSystemUserCompletion() {
  console.log('\n=== Example 2: System + User Completion ===');
  
  const systemPrompt = "You are a helpful assistant that provides concise answers.";
  const userPrompt = "Explain quantum computing in simple terms.";
  
  const result = await systemUserCompletion(
    systemPrompt,
    userPrompt,
    "gpt-4o-mini"
  );
  
  console.log('Response:', result.content);
  console.log('Cost:', `$${result.usage.cost.toFixed(4)}`);
}

// Example 3: Web search enabled
async function exampleWebSearch() {
  console.log('\n=== Example 3: Web Search Enabled ===');
  
  const result = await callLLM({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "user",
        content: "Find the official website for Tesla Inc. Use web search to get current information."
      }
    ],
    enableWebSearch: true,
    temperature: 0.1
  });
  
  console.log('Response:', result.content);
  console.log('Tools used:', result.metadata.toolsUsed);
  console.log('Cost:', `$${result.usage.cost.toFixed(4)}`);
}

// Example 4: Website finding (like our use case)
async function exampleWebsiteFinding() {
  console.log('\n=== Example 4: Website Finding ===');
  
  const sellerData = {
    sellerName: "Apple Inc",
    businessName: "Apple Computer Company", 
    address: "1 Apple Park Way, Cupertino, CA 95014"
  };
  
  const prompt = `Find the official website for this company:
- Company: ${sellerData.sellerName}
- Business: ${sellerData.businessName}
- Address: ${sellerData.address}

Use web search to find their official website. Return a JSON response with this format:
{
  "websites": ["website1.com", "website2.com"],
  "success": true,
  "reasoning": "Brief explanation of findings"
}`;

  const result = await callLLM({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "user",
        content: prompt
      }
    ],
    enableWebSearch: true,
    temperature: 0.1,
    metadata: {
      sellerName: sellerData.sellerName,
      useCase: "website_finding"
    }
  });
  
  console.log('Response:', result.content);
  console.log('Cost:', `$${result.usage.cost.toFixed(4)}`);
  console.log('Response time:', `${result.responseTime}ms`);
  
  // Try to parse the JSON response
  try {
    const parsed = JSON.parse(result.content);
    console.log('Parsed websites:', parsed.websites);
    console.log('Success:', parsed.success);
  } catch (error) {
    console.log('Failed to parse JSON response');
  }
}

// Example 5: Custom tools
async function exampleCustomTools() {
  console.log('\n=== Example 5: Custom Tools ===');
  
  const customTool = {
    type: "function",
    function: {
      name: "calculate_cost",
      description: "Calculate the cost of a service based on usage",
      parameters: {
        type: "object",
        properties: {
          tokens: {
            type: "integer",
            description: "Number of tokens used"
          },
          rate: {
            type: "number", 
            description: "Rate per token"
          }
        },
        required: ["tokens", "rate"]
      }
    }
  };
  
  const result = await callLLM({
    model: "gpt-4o-mini",
    messages: [
      {
        role: "user",
        content: "Calculate the cost for using 1000 tokens at a rate of $0.001 per token. Use the calculate_cost function."
      }
    ],
    customTools: [customTool],
    temperature: 0.1
  });
  
  console.log('Response:', result.content);
  console.log('Tool calls:', result.toolCalls);
}

// Example 6: Error handling
async function exampleErrorHandling() {
  console.log('\n=== Example 6: Error Handling ===');
  
  const result = await callLLM({
    model: "invalid-model",
    messages: [
      {
        role: "user",
        content: "This should fail"
      }
    ]
  });
  
  if (!result.success) {
    console.log('Error occurred:', result.error);
    console.log('Model:', result.model);
  }
}

// Example 7: List available models
async function exampleListModels() {
  console.log('\n=== Example 7: Available Models ===');
  
  const models = getAvailableModels();
  
  console.log('Available models:');
  models.forEach(model => {
    console.log(`- ${model.model} (${model.provider})`);
    console.log(`  Pricing: $${model.pricing.input}/$${model.pricing.output} per million tokens`);
    console.log(`  Max tokens: ${model.maxTokens}`);
    console.log(`  Web search: ${model.supportsWebSearch ? 'Yes' : 'No'}`);
    console.log('');
  });
}

// Run all examples
async function runAllExamples() {
  try {
    await exampleListModels();
    await exampleSimpleCompletion();
    await exampleSystemUserCompletion();
    await exampleWebSearch();
    await exampleWebsiteFinding();
    await exampleCustomTools();
    await exampleErrorHandling();
    
    console.log('\n=== All examples completed! ===');
  } catch (error) {
    console.error('Error running examples:', error);
  }
}

// Export for use in other files
module.exports = {
  exampleSimpleCompletion,
  exampleSystemUserCompletion,
  exampleWebSearch,
  exampleWebsiteFinding,
  exampleCustomTools,
  exampleErrorHandling,
  exampleListModels,
  runAllExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples();
}
