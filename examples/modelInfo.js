/**
 * Example of using the Prisma Model Introspector
 */

const introspector = require('../utils/prismaModelIntrospector');

// Get all available model names
const modelNames = introspector.getModelNames();
console.log('Available models:', modelNames);

// Select a model to examine
const modelName = 'AmazonSeller';

// Get basic field types
console.log(`\nField types for ${modelName}:`);
const fieldTypes = introspector.getModelFieldTypes(modelName);
console.log(fieldTypes);

// Get scalar fields only (non-relation fields)
console.log(`\nScalar fields for ${modelName}:`);
const scalarFields = introspector.getScalarFields(modelName);
console.log(scalarFields);

// Get relation fields
console.log(`\nRelation fields for ${modelName}:`);
const relationFields = introspector.getRelationFields(modelName);
console.log(relationFields);

// Get detailed field information
console.log(`\nDetailed field info for ${modelName}.id:`);
const fieldDetails = introspector.getModelFieldDetails(modelName);
console.log(fieldDetails.id); 