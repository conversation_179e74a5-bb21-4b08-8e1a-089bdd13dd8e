/**
 * Verify that new MV (v2) has all columns from original MV
 * Plus the new SP columns
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

async function verifyMVCompatibility() {
  console.log('🔍 Verifying MV Compatibility...\n');
  
  try {
    // Get columns from original MV
    const originalColumns = await prisma.$queryRawUnsafe(`
      SELECT 
        column_name,
        ordinal_position,
        data_type
      FROM information_schema.columns
      WHERE table_name = 'mv_email_with_thread_start'
      ORDER BY ordinal_position
    `);
    
    // Get columns from new MV
    const newColumns = await prisma.$queryRawUnsafe(`
      SELECT 
        column_name,
        ordinal_position,
        data_type
      FROM information_schema.columns
      WHERE table_name = 'mv_email_with_thread_start_v2'
      ORDER BY ordinal_position
    `);
    
    console.log('📊 Column Comparison:\n');
    console.log(`Original MV: ${originalColumns.length} columns`);
    console.log(`New MV (v2): ${newColumns.length} columns\n`);
    
    // Check if all original columns exist in new MV
    console.log('✅ Checking all original columns exist in new MV:\n');
    console.log('Pos | Column Name                | Type         | Status');
    console.log('----|----------------------------|--------------|--------');
    
    let allOriginalExist = true;
    const newColumnNames = newColumns.map(c => c.column_name);
    
    originalColumns.forEach(col => {
      const exists = newColumnNames.includes(col.column_name);
      const status = exists ? '✅' : '❌ MISSING';
      if (!exists) allOriginalExist = false;
      
      console.log(
        `${col.ordinal_position.toString().padEnd(3)} | ` +
        `${col.column_name.padEnd(26)} | ` +
        `${col.data_type.padEnd(12)} | ` +
        `${status}`
      );
    });
    
    // Show new columns added
    console.log('\n📌 New columns in v2 (not in original):\n');
    const originalColumnNames = originalColumns.map(c => c.column_name);
    const addedColumns = newColumns.filter(c => !originalColumnNames.includes(c.column_name));
    
    if (addedColumns.length > 0) {
      console.log('Column Name         | Type         | Description');
      console.log('--------------------|--------------|------------');
      addedColumns.forEach(col => {
        let description = '';
        if (col.column_name === 'sp') description = 'Search Priority';
        if (col.column_name === 'email_provider') description = 'Gmail, Yahoo, etc';
        if (col.column_name === 'domain_type') description = 'STANDARD, COUNTRY, etc';
        
        console.log(
          `${col.column_name.padEnd(19)} | ` +
          `${col.data_type.padEnd(12)} | ` +
          `${description}`
        );
      });
    } else {
      console.log('No new columns added');
    }
    
    // Check order preservation for first N columns
    console.log('\n🔢 Column Order Verification (first 15 columns):\n');
    console.log('Original Order → New Order');
    console.log('---------------------------');
    
    let orderPreserved = true;
    for (let i = 0; i < Math.min(15, originalColumns.length); i++) {
      const origCol = originalColumns[i];
      const newCol = newColumns[i];
      
      if (origCol.column_name === newCol.column_name) {
        console.log(`✅ ${i+1}. ${origCol.column_name}`);
      } else {
        console.log(`❌ ${i+1}. ${origCol.column_name} → ${newCol.column_name} (ORDER CHANGED)`);
        orderPreserved = false;
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 COMPATIBILITY SUMMARY:\n');
    
    if (allOriginalExist && orderPreserved) {
      console.log('✅ FULLY COMPATIBLE - New MV can replace original');
      console.log('   - All original columns exist');
      console.log('   - Column order preserved');
      console.log('   - Safe to use in existing queries');
    } else if (allOriginalExist) {
      console.log('⚠️  PARTIALLY COMPATIBLE - All columns exist but order changed');
      console.log('   - Queries using column names will work');
      console.log('   - Queries using column positions may fail');
    } else {
      console.log('❌ NOT COMPATIBLE - Missing columns from original');
      console.log('   - Do not switch to production without fixing');
    }
    
    console.log(`\n📊 Added ${addedColumns.length} new columns: ${addedColumns.map(c => c.column_name).join(', ')}`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 MV Compatibility Check');
  console.log('=' .repeat(60));
  
  try {
    await verifyMVCompatibility();
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();