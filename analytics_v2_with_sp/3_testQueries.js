/**
 * Step 3: Test the new MV with sample queries
 * Run: node analytics_v2_with_sp/3_testQueries.js
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

async function testQueries() {
  console.log('🧪 Testing new MV with SP data...\n');
  
  try {
    // Test 1: Compare record counts
    console.log('1️⃣ Comparing MVs:');
    const comparison = await prisma.$queryRawUnsafe(`
      SELECT 
        'Original MV' as version,
        COUNT(*) as count
      FROM mv_email_with_thread_start
      UNION ALL
      SELECT 
        'New MV (v2)' as version,
        COUNT(*) as count
      FROM mv_email_with_thread_start_v2
    `);
    
    comparison.forEach(row => {
      console.log(`   ${row.version}: ${row.count} records`);
    });
    
    // Test 2: SP Distribution
    console.log('\n2️⃣ SP Distribution:');
    const spDist = await prisma.$queryRawUnsafe(`
      SELECT 
        sp,
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE type = 'SENT') as sent,
        COUNT(*) FILTER (WHERE type = 'REPLY') as replies,
        ROUND(
          COUNT(*) FILTER (WHERE type = 'REPLY') * 100.0 / 
          NULLIF(COUNT(*) FILTER (WHERE type = 'SENT'), 0), 
          2
        ) as reply_rate
      FROM mv_email_with_thread_start_v2
      GROUP BY sp
      ORDER BY sp
      LIMIT 10
    `);
    
    console.log('   SP   | Total  | Sent   | Replies | Reply Rate');
    console.log('   -----|--------|--------|---------|----------');
    spDist.forEach(row => {
      console.log(`   ${row.sp.padEnd(4)} | ${row.total.toString().padStart(6)} | ${row.sent.toString().padStart(6)} | ${row.replies.toString().padStart(7)} | ${row.reply_rate || 0}%`);
    });
    
    // Test 3: Email Provider Distribution
    console.log('\n3️⃣ Email Provider Distribution:');
    const providerDist = await prisma.$queryRawUnsafe(`
      SELECT 
        email_provider,
        COUNT(*) as count
      FROM mv_email_with_thread_start_v2
      GROUP BY email_provider
      ORDER BY count DESC
      LIMIT 5
    `);
    
    providerDist.forEach(row => {
      console.log(`   ${row.email_provider}: ${row.count}`);
    });
    
    // Test 4: Sample query with SP filter
    console.log('\n4️⃣ Sample Query - High Priority (SP1) Performance:');
    const sp1Stats = await prisma.$queryRawUnsafe(`
      SELECT 
        email_seq_number,
        COUNT(*) FILTER (WHERE type = 'SENT') as sent,
        COUNT(*) FILTER (WHERE type = 'REPLY') as replies
      FROM mv_email_with_thread_start_v2
      WHERE sp = 'SP1'
      GROUP BY email_seq_number
      ORDER BY email_seq_number
      LIMIT 3
    `);
    
    console.log('   Seq | Sent | Replies');
    console.log('   ----|------|--------');
    sp1Stats.forEach(row => {
      console.log(`   ${row.email_seq_number.padEnd(3)} | ${row.sent.toString().padStart(4)} | ${row.replies.toString().padStart(7)}`);
    });
    
    // Test 5: Verify SP coverage
    console.log('\n5️⃣ SP Coverage:');
    const coverage = await prisma.$queryRawUnsafe(`
      SELECT 
        CASE WHEN sp = 'UNK' THEN 'Without SP' ELSE 'With SP' END as status,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
      FROM mv_email_with_thread_start_v2
      GROUP BY CASE WHEN sp = 'UNK' THEN 'Without SP' ELSE 'With SP' END
    `);
    
    coverage.forEach(row => {
      console.log(`   ${row.status}: ${row.count} (${row.percentage}%)`);
    });
    
  } catch (error) {
    console.error('❌ Error running tests:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Step 3: Testing New MV');
  console.log('=' .repeat(60));
  
  try {
    await testQueries();
    
    console.log('\n' + '=' .repeat(60));
    console.log('✅ Tests complete!');
    console.log('\nIf everything looks good:');
    console.log('  - Update dashboard queries to use mv_email_with_thread_start_v2');
    console.log('  - Or run 4_switchToProduction.js to swap MVs');
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();