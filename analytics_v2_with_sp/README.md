# Analytics V2 with SP (Search Priority)

## What This Does
Adds Search Priority (SP) data to analytics without breaking the existing system.

## Files (Run in Order)

### 0. `0_fetchReplitData.js` 
- Fetches data from Replit (same as generateAnalytics.sh)
- Creates: TagAnalytics, ProviderAnalytics, FunnelClientAnalytics tables
- Syncs: InboxesFromReplit, MeetingsFromReplit
- Run this first to get all base data

### 1. `1_fetchSPData.js`
- Fetches Query 271 from Metabase (170K+ prospect records)
- Creates `sp_email_lookup` table with email→SP mappings
- Shows SP distribution (SP1-SP8, UNK)

### 2. `2_createMV.js`
- Creates `mv_email_with_thread_start_v2` (new MV with SP)
- Joins Email table with sp_email_lookup
- Adds: sp, email_provider, domain_type fields
- Original MV remains unchanged

### 3. `3_testQueries.js`
- Tests the new MV
- Shows SP distribution and coverage
- Compares with original MV
- Run this to verify everything works

### 4. `4_switchToProduction.js` (Optional)
- Swaps the MVs so existing queries use new one
- Creates backup of original
- Run with `--rollback` to undo

### 5. `5_refreshMV.js`
- Refreshes the MV with latest data
- Add to cron for automatic refresh

## How to Run

```bash
# From SellerBot root directory
cd /Users/<USER>/equal/SellerBot

# Step 0: Fetch Replit data (like generateAnalytics.sh)
node analytics_v2_with_sp/0_fetchReplitData.js

# Step 1: Create SP lookup table
node analytics_v2_with_sp/1_fetchSPData.js

# Step 2: Create new MV with SP
node analytics_v2_with_sp/2_createMV.js

# Step 3: Test the new MV
node analytics_v2_with_sp/3_testQueries.js

# Step 4: (Optional) Switch to production
node analytics_v2_with_sp/4_switchToProduction.js

# To rollback if needed
node analytics_v2_with_sp/4_switchToProduction.js --rollback

# Step 5: Refresh MV (add to cron)
node analytics_v2_with_sp/5_refreshMV.js
```

## What Gets Created

1. **sp_email_lookup** table
   ```sql
   email                | sp   | seller_id | business_name
   <EMAIL>    | SP1  | 12345     | Example Inc
   <EMAIL>    | SP3B | 67890     | Company LLC
   ```

2. **mv_email_with_thread_start_v2** materialized view
   - All original fields from Email table
   - Plus: `sp`, `email_provider`, `domain_type`

## Testing Without Risk

The new MV (`_v2`) exists alongside the original. Test with:

```sql
-- Check SP distribution
SELECT sp, COUNT(*) 
FROM mv_email_with_thread_start_v2 
GROUP BY sp;

-- Compare with original
SELECT COUNT(*) FROM mv_email_with_thread_start;  -- original
SELECT COUNT(*) FROM mv_email_with_thread_start_v2; -- new
```

## Using in Queries

### Before (no SP)
```sql
SELECT COUNT(*) 
FROM mv_email_with_thread_start 
WHERE type = 'SENT';
```

### After (with SP)
```sql
SELECT sp, COUNT(*) 
FROM mv_email_with_thread_start_v2 
WHERE type = 'SENT'
GROUP BY sp;
```

## Expected SP Distribution
- ~63% will have SP (SP1-SP8)
- ~37% will be UNK (emails to non-prospects)
- This is normal

## Production Deployment

### Option 1: Update Queries
Change dashboard queries to use `mv_email_with_thread_start_v2`

### Option 2: Swap MVs
Run `4_switchToProduction.js` to rename MVs

### Cron Setup

Option 1: Run full analytics (replaces generateAnalytics.sh)
```bash
# Run complete analytics every 15 minutes
*/15 * * * * cd /path/to/SellerBot && node analytics_v2_with_sp/run_analytics.js >> /var/log/analytics.log 2>&1
```

Option 2: Just refresh MV (if data sources update separately)
```bash
# Just refresh the MV every 15 minutes
*/15 * * * * cd /path/to/SellerBot && node analytics_v2_with_sp/5_refreshMV.js >> /var/log/analytics.log 2>&1
```

To edit cron:
```bash
crontab -e
# Add one of the above lines
```

## Rollback

If anything goes wrong:
```bash
# Option 1: Use the rollback script
node analytics_v2_with_sp/4_switchToProduction.js --rollback

# Option 2: Manual SQL
psql -c "DROP MATERIALIZED VIEW mv_email_with_thread_start_v2;"
psql -c "DROP TABLE sp_email_lookup;"
```

Original system continues working!