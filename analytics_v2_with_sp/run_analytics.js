#!/usr/bin/env node

// /**
//  * Main analytics script for cron
//  * Simply runs steps 0, 1, and 2 in sequence
//  * 
//  * Cron entry:
//  * */15 * * * * cd /path/to/SellerBot && node analytics_v2_with_sp/run_analytics.js
//  **/

const { execSync } = require('child_process');

console.log('🚀 Running Analytics V2 with SP');
console.log(`📅 ${new Date().toISOString()}`);
console.log('=' .repeat(60));

try {
  // Step 0: Fetch Replit data
  console.log('\n▶️  Step 0: Fetching Replit data...');
  execSync('node analytics_v2_with_sp/0_fetchReplitData.js', { stdio: 'inherit' });
  
  // Step 1: Fetch SP data
  console.log('\n▶️  Step 1: Fetching SP data...');
  execSync('node analytics_v2_with_sp/1_fetchSPData.js', { stdio: 'inherit' });
  
  // Step 2: Create/Refresh MV
  console.log('\n▶️  Step 2: Creating/Refreshing MV...');
  execSync('node analytics_v2_with_sp/2_createMV.js', { stdio: 'inherit' });
  
  console.log('\n' + '=' .repeat(60));
  console.log('✅ Analytics complete');
  
} catch (error) {
  console.error('❌ Analytics failed:', error.message);
  process.exit(1);
}