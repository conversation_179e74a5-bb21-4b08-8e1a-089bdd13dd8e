/**
 * Step 2: Create new materialized view with SP data
 * Run: node analytics_v2_with_sp/2_createMV.js
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

async function createMaterializedViewWithSP() {
  console.log('🔨 Creating materialized view with SP...');
  
  try {
    // Drop if exists
    await prisma.$executeRawUnsafe('DROP MATERIALIZED VIEW IF EXISTS mv_email_with_thread_start_v2 CASCADE');
    
    // Create new MV - same as original but with SP
    await prisma.$executeRawUnsafe(`
      CREATE MATERIALIZED VIEW mv_email_with_thread_start_v2 AS
      SELECT 
        e.id,
        e."leadId",
        e.subject,
        e.body,
        e.type,
        e."toEmailID",
        e."fromEmailID",
        e."time",
        e."messageId",
        e."campaingId",
        e.email_seq_number,
        e.open_count,
        
        -- Thread calculation (same as original)
        first_value(e."fromEmailID") OVER (
          PARTITION BY e."leadId" 
          ORDER BY
            CASE
              WHEN (e."fromEmailID" = '<EMAIL>'::text) THEN 2
              WHEN (e."fromEmailID" ~~ '%.onmicrosoft.com'::text) THEN 2
              ELSE 1
            END, 
            e."time",
            CASE
              WHEN (e.type = 'SENT'::text) THEN 1
              WHEN (e.type = 'FORWARD'::text) THEN 2
              WHEN (e.type = 'REPLY'::text) THEN 3
              ELSE 4
            END 
          ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
        ) AS "threadStartEmailId",
        
        first_value(e."toEmailID") OVER (
          PARTITION BY e."leadId" 
          ORDER BY
            CASE
              WHEN (e."fromEmailID" = '<EMAIL>'::text) THEN 2
              WHEN (e."fromEmailID" ~~ '%.onmicrosoft.com'::text) THEN 2
              ELSE 1
            END, 
            e."time",
            CASE
              WHEN (e.type = 'SENT'::text) THEN 1
              WHEN (e.type = 'FORWARD'::text) THEN 2
              WHEN (e.type = 'REPLY'::text) THEN 3
              ELSE 4
            END 
          ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
        ) AS "threadToEmailId",
        
        -- NEW: SP field with UNK fallback
        COALESCE(sp_lookup.sp, 'UNK') AS sp,
        
        -- NEW: Email provider
        CASE 
          WHEN e."toEmailID" ILIKE '%gmail%' THEN 'Gmail'
          WHEN e."toEmailID" ILIKE '%yahoo%' THEN 'Yahoo'
          WHEN e."toEmailID" ILIKE '%outlook%' OR e."toEmailID" ILIKE '%hotmail%' THEN 'Outlook'
          WHEN e."toEmailID" ILIKE '%icloud%' OR e."toEmailID" ILIKE '%me.com%' THEN 'iCloud'
          WHEN e."toEmailID" ILIKE '%aol%' THEN 'AOL'
          ELSE 'Custom'
        END AS email_provider,
        
        -- NEW: Domain type
        CASE
          WHEN SPLIT_PART(SPLIT_PART(e."toEmailID", '@', 2), '.', -1) IN ('com', 'net', 'org') THEN 'STANDARD'
          WHEN SPLIT_PART(SPLIT_PART(e."toEmailID", '@', 2), '.', -1) IN ('edu', 'gov', 'mil') THEN 'INSTITUTIONAL'
          WHEN LENGTH(SPLIT_PART(SPLIT_PART(e."toEmailID", '@', 2), '.', -1)) = 2 THEN 'COUNTRY'
          ELSE 'OTHER'
        END AS domain_type
        
      FROM "Email" e
      LEFT JOIN sp_email_lookup sp_lookup 
        ON LOWER(e."toEmailID") = sp_lookup.email
    `);
    
    console.log('✅ Materialized view created');
    
    // Create indexes
    console.log('📇 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX idx_mv_v2_leadid ON mv_email_with_thread_start_v2("leadId")',
      'CREATE INDEX idx_mv_v2_campaingid ON mv_email_with_thread_start_v2("campaingId")',
      'CREATE INDEX idx_mv_v2_time ON mv_email_with_thread_start_v2("time")',
      'CREATE INDEX idx_mv_v2_type ON mv_email_with_thread_start_v2(type)',
      'CREATE INDEX idx_mv_v2_sp ON mv_email_with_thread_start_v2(sp)',
      'CREATE INDEX idx_mv_v2_email_provider ON mv_email_with_thread_start_v2(email_provider)',
      'CREATE INDEX idx_mv_v2_composite ON mv_email_with_thread_start_v2(sp, type, email_seq_number)'
    ];
    
    for (const indexSql of indexes) {
      await prisma.$executeRawUnsafe(indexSql);
    }
    
    console.log('✅ Indexes created');
    
    // Verify SP distribution
    const stats = await prisma.$queryRawUnsafe(`
      SELECT 
        sp,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
      FROM mv_email_with_thread_start_v2
      GROUP BY sp
      ORDER BY sp
    `);
    
    console.log('\n📊 SP Distribution in MV:');
    stats.forEach(row => {
      console.log(`   ${row.sp}: ${row.count} (${row.percentage}%)`);
    });
    
    const total = await prisma.$queryRawUnsafe('SELECT COUNT(*) as total FROM mv_email_with_thread_start_v2');
    console.log(`\nTotal records: ${total[0].total}`);
    
  } catch (error) {
    console.error('❌ Error creating MV:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Step 2: Creating MV with SP');
  console.log('=' .repeat(60));
  
  try {
    await createMaterializedViewWithSP();
    
    console.log('\n' + '=' .repeat(60));
    console.log('✅ New MV created: mv_email_with_thread_start_v2');
    console.log('\nNext steps:');
    console.log('1. Test queries against mv_email_with_thread_start_v2');
    console.log('2. Run 3_testQueries.js to verify');
    console.log('3. When ready, run 4_switchToProduction.js');
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();