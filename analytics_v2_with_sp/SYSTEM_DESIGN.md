# Complete Analytics V2 System Design

## Data Flow Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│     REPLIT      │     │    METABASE     │     │   DATABASE      │
│                 │     │                 │     │   (Email)       │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                        │
         │                       │                        │
    ┌────▼────────────────┐     │                        │
    │ 0_fetchReplitData.js│     │                        │
    └────┬────────────────┘     │                        │
         │                       │                        │
         ▼                       │                        │
    ┌─────────────┐              │                        │
    │TagAnalytics │              │                        │
    │ProviderAn.  │              │                        │
    │FunnelClient │              │                        │
    │InboxesRepl. │              │                        │
    │MeetingsRepl.│              │                        │
    └─────────────┘              │                        │
                                 │                        │
                                 ▼                        │
                      ┌──────────────────┐                │
                      │1_fetchSPData.js  │                │
                      │  (Query 271)     │                │
                      └────────┬─────────┘                │
                               │                          │
                               ▼                          │
                      ┌──────────────────┐                │
                      │ sp_email_lookup  │                │
                      │  email → SP      │                │
                      └────────┬─────────┘                │
                               │                          │
                               └──────────┬───────────────┘
                                         │
                                         ▼
                              ┌──────────────────┐
                              │  2_createMV.js   │
                              └────────┬─────────┘
                                      │
                                      ▼
                        ┌──────────────────────────┐
                        │ mv_email_with_thread_    │
                        │      start_v2            │
                        │ (Email + SP + Provider)  │
                        └──────────┬───────────────┘
                                  │
                                  ▼
                            ┌───────────┐
                            │ DASHBOARD │
                            └───────────┘
```

## Data Sources

### 1. Replit Data (Step 0)
```javascript
// 0_fetchReplitData.js calls existing tooling scripts:
- createMetabaseAnalyticsTables.js tag → TagAnalytics
- createMetabaseAnalyticsTables.js providerAnalytics → ProviderAnalytics  
- createMetabaseAnalyticsTables.js funnelClient → FunnelClientAnalytics
- syncEmailTags.js inboxes → InboxesFromReplit
- syncEmailTags.js meetings → MeetingsFromReplit
```

### 2. Metabase Query 271 (Step 1)
```javascript
// 1_fetchSPData.js fetches:
{
  "Prospect → Email": "<EMAIL>",
  "dominant_search_priority": "SP1",
  "amazon_seller_id": "12345",
  "business_name": "Example Inc"
}
// Creates sp_email_lookup table
```

### 3. Email Table (Step 2)
```sql
-- 2_createMV.js joins:
FROM "Email" e
LEFT JOIN sp_email_lookup sp ON e.toEmailID = sp.email
```

## Tables Created

### Analytics Tables (from Replit)
1. **TagAnalytics** - Email tag analytics
2. **ProviderAnalytics** - Email provider stats  
3. **FunnelClientAnalytics** - Funnel/client metrics
4. **InboxesFromReplit** - Inbox data
5. **MeetingsFromReplit** - Meeting data

### SP Tables (new)
6. **sp_email_lookup** - Email to SP mapping
7. **mv_email_with_thread_start_v2** - Enhanced MV with SP

## Key Differences from Current System

### Current (generateAnalytics.sh)
```bash
1. Fetch Replit data → Tables
2. Create MV from Email table
3. No SP data
```

### New (analytics_v2_with_sp)
```bash
0. Fetch Replit data → Tables (same)
1. Fetch Query 271 → SP lookup (NEW)
2. Create MV with SP join (ENHANCED)
```

## Why This Design?

1. **Preserves existing flow** - Step 0 does exactly what generateAnalytics.sh does
2. **Adds SP layer** - Step 1 creates SP lookup from Query 271
3. **Non-breaking** - Creates v2 MV alongside original
4. **Same data sources** - Replit + Email table (consistent)
5. **Additional dimension** - SP enables new analytics

## What Query 277 Does (Why We Don't Use It)

Query 277 structure:
```sql
SELECT * FROM mv_email_with_thread_start mv_email
LEFT JOIN Campaign...
LEFT JOIN Client...
-- Query 277 DEPENDS on the MV we're trying to replace!
```

So we go directly to the source (Email table) instead.

## Production Deployment

### Replace generateAnalytics.sh with:
```bash
#!/bin/bash
# New analytics with SP

# Step 0: Fetch Replit data
node analytics_v2_with_sp/0_fetchReplitData.js

# Step 1: Fetch SP data
node analytics_v2_with_sp/1_fetchSPData.js

# Step 2: Create/refresh MV
node analytics_v2_with_sp/2_createMV.js

# Or just refresh if already created
node analytics_v2_with_sp/5_refreshMV.js
```

## Cron Setup
```bash
# Every 15 minutes
*/15 * * * * cd /path/to/SellerBot && bash analytics_v2_with_sp/run_all.sh
```