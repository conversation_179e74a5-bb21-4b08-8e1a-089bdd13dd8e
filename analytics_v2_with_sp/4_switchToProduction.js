/**
 * Step 4: Switch to production (optional)
 * Run: node analytics_v2_with_sp/4_switchToProduction.js
 * 
 * This swaps the MVs so existing queries automatically use the new one
 */

require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

async function switchToProduction() {
  console.log('🔄 Switching to new MV with SP...\n');
  console.log('⚠️  WARNING: This will affect all existing queries!');
  console.log('   Make sure you have tested thoroughly.\n');
  
  // Ask for confirmation
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const answer = await new Promise(resolve => {
    readline.question('Are you sure you want to switch? (yes/no): ', resolve);
  });
  readline.close();
  
  if (answer.toLowerCase() !== 'yes') {
    console.log('❌ Cancelled');
    return false;
  }
  
  try {
    console.log('\n📦 Creating backup of original MV...');
    
    // Rename original to backup
    await prisma.$executeRawUnsafe(`
      ALTER MATERIALIZED VIEW mv_email_with_thread_start 
      RENAME TO mv_email_with_thread_start_backup
    `);
    console.log('   Original renamed to: mv_email_with_thread_start_backup');
    
    // Rename v2 to production
    await prisma.$executeRawUnsafe(`
      ALTER MATERIALIZED VIEW mv_email_with_thread_start_v2 
      RENAME TO mv_email_with_thread_start
    `);
    console.log('   V2 renamed to: mv_email_with_thread_start');
    
    console.log('\n✅ Switch complete!');
    console.log('\n📋 Current state:');
    console.log('   mv_email_with_thread_start = NEW (with SP)');
    console.log('   mv_email_with_thread_start_backup = OLD (without SP)');
    
    return true;
    
  } catch (error) {
    console.error('❌ Error switching MVs:', error.message);
    console.log('\n🔧 Attempting rollback...');
    
    try {
      // Try to rollback
      await prisma.$executeRawUnsafe(`
        ALTER MATERIALIZED VIEW mv_email_with_thread_start 
        RENAME TO mv_email_with_thread_start_v2
      `);
      await prisma.$executeRawUnsafe(`
        ALTER MATERIALIZED VIEW mv_email_with_thread_start_backup 
        RENAME TO mv_email_with_thread_start
      `);
      console.log('✅ Rollback successful');
    } catch (rollbackError) {
      console.error('❌ Rollback failed:', rollbackError.message);
    }
    
    throw error;
  }
}

async function rollback() {
  console.log('🔙 Rolling back to original MV...\n');
  
  try {
    await prisma.$executeRawUnsafe(`
      ALTER MATERIALIZED VIEW mv_email_with_thread_start 
      RENAME TO mv_email_with_thread_start_v2
    `);
    
    await prisma.$executeRawUnsafe(`
      ALTER MATERIALIZED VIEW mv_email_with_thread_start_backup 
      RENAME TO mv_email_with_thread_start
    `);
    
    console.log('✅ Rollback complete!');
    console.log('\n📋 Current state:');
    console.log('   mv_email_with_thread_start = OLD (without SP)');
    console.log('   mv_email_with_thread_start_v2 = NEW (with SP)');
    
  } catch (error) {
    console.error('❌ Error during rollback:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Step 4: Switch to Production');
  console.log('=' .repeat(60));
  
  // Check if user wants to rollback
  const args = process.argv.slice(2);
  const isRollback = args[0] === '--rollback';
  
  try {
    if (isRollback) {
      await rollback();
    } else {
      await switchToProduction();
    }
  } catch (error) {
    console.error('Failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();