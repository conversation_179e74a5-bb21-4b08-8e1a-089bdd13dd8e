const cheerio = require("cheerio");
const xmlParser = require("fast-xml-parser");
const path = require("path");
const fs = require("fs").promises;
const Bottleneck = require("bottleneck");

// Import utilities
const {
  fetchHtmlWithRetry,
  checkWebsiteStatus,
} = require("../utils/httpUtils");
const {
  normalizeToHttps,
  cleanUrl,
  removeFileExtensions,
  getDomainFromUrl,
  isValidUrl,
} = require("../utils/urlUtils");
const { extractAllEmails } = require("../utils/emailUtils");
const { DEFAULT_OPTIONS } = require("../constants");

/**
 * Class to handle email extraction from websites
 */
class EmailExtractor {
  /**
   * Initialize EmailExtractor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    // Merge default options from constants with provided options
    this.options = { ...DEFAULT_OPTIONS, ...options };

    // Initialize the rate limiter
    this.limiter = new Bottleneck({
      maxConcurrent: this.options.maxConcurrent,
      minTime: this.options.minTime,
      reservoir: this.options.reservoirRefreshAmount,
      reservoirRefreshAmount: this.options.reservoirRefreshAmount,
      reservoirRefreshInterval: this.options.reservoirRefreshInterval,
    });
    this.failedUrls = new Set();
    this.failedCount = { count: 0 };

    // Add events for monitoring    this.limiter.on('failed', (error, jobInfo) => {
    this.limiter.on("failed", (error, jobInfo) => {
      console.warn(`Rate limiter job failed: ${error}, ${jobInfo.options.id}`);
    });
    this.limiter.on("depleted", (empty) => {
      console.log(
        `Rate limiter reservoir depleted, ${empty ? "now empty" : "still has capacity"}`
      );
    });

    this.visitedUrls = new Set();
    this.emails = new Set();
    this.emailUrls = {};
  }

  /**
   * Main method to extract emails from a website
   * @param {string} url - Website URL
   * @returns {Promise<{emails: string[], status: string}>}
   */
  async extractEmails(url) {
    if (!url) {
      return {
        emails: [],
        status: "Invalid URL",
        emailUrls: {},
      };
    }

    try {
      // Normalize URL to ensure it has a protocol but don't force HTTPS
      url = normalizeToHttps(url, false);

      // Check if the website is accessible
      const websiteStatus = await checkWebsiteStatus(url);
      if (!websiteStatus.isAccessible) {
        console.log(`Website not accessible: ${websiteStatus.status}`);
        // We'll still attempt to fetch with our fallback strategy even if initial check fails
      }

      // Reset state for this extraction
      this.visitedUrls = new Set();
      this.emails = new Set();

      // Start crawling the website
      await this.crawlWebsite(url);

      // Return results
      const emailsFound = Array.from(this.emails);
      let status = emailsFound.length > 0 ? "Done" : "No Emails Found";

      // If we initially detected the site as blocked but still managed to get emails
      // through our fallback strategy, update the status
      if (!websiteStatus.isAccessible && emailsFound.length > 0) {
        status = `Done (Recovered: ${websiteStatus.status})`;
      } else if (!websiteStatus.isAccessible) {
        status = websiteStatus.status;
      }

      return {
        emails: emailsFound,
        status: status,
        emailUrls: this.emailUrls,
      };
    } catch (error) {
      console.error(`Error extracting emails from ${url}:`, error);
      return {
        emails: [],
        status: `Error: ${error.message}`,
      };
    }
  }

  /**
   * Crawl website to find emails
   * @param {string} baseUrl - Website base URL
   */
  async crawlWebsite(baseUrl) {
    try {
      // Try to get the homepage HTML
      const homepageHtml = await fetchHtmlWithRetry(
        baseUrl,
        3,
        this.options.useProxy,
        this.options.saveHtml,
        this.failedCount,
        this.options.maxProxyPages,
        this.addFailedUrl.bind(this)
      );

      // DEBUG: Extract emails directly from homepage HTML first
      const homepageEmails = extractAllEmails(homepageHtml, baseUrl);
      console.log(`DEBUG: Found ${homepageEmails.length} emails on homepage HTML: ${homepageEmails.join(', ')}`);
      
      // Add homepage emails to our collection
      for (const email of homepageEmails) {
        const resultantEmail = String(email).toLowerCase();
        this.emails.add(resultantEmail);
        this.addEmailUrl(resultantEmail, baseUrl);
      }

      // Get all URLs to crawl
      const urls = await this.collectUrlsToCrawl(baseUrl, homepageHtml);

      // Use promise.all with rate limiting to crawl URLs in parallel but controlled manner
      const promises = urls.map((url) => {
        if (this.visitedUrls.size >= this.options.maxPages) {
          return Promise.resolve(); // Skip if we've reached max pages
        }

        if (this.emails.size >= this.options.maxEmails) {
          return Promise.resolve(); // Skip if we've reached max emails
        }

        if (!this.visitedUrls.has(url)) {
          // Schedule with the rate limiter
          return this.limiter.schedule(() => this.crawlPage(url));
        }

        return Promise.resolve();
      });

      // Wait for all the rate-limited crawling to complete
      await Promise.all(promises);
    } catch (error) {
      console.error(`Error crawling website ${baseUrl}:`, error);
      throw error;
    }
  }

  /**
   * Collect all URLs to crawl from the homepage
   * @param {string} baseUrl - Website base URL
   * @param {string} html - Homepage HTML
   * @returns {Promise<string[]>} - List of URLs to crawl
   */
  async collectUrlsToCrawl(baseUrl, html) {
    const domain = getDomainFromUrl(baseUrl);
    const uniqueUrls = new Set();

    try {
      // Extract relevant URLs based on keywords
      const relevantUrls = this.extractRelevantUrls(html, baseUrl);
      console.log(`Found ${relevantUrls.length} relevant URLs`);

      // Extract navigation and footer links
      const navFooterLinks = this.extractNavFooterUrls(html, baseUrl);
      console.log(`Found ${navFooterLinks.length} navigation/footer URLs`);

      // Check sitemap for more URLs
      const sitemapUrls = await this.checkSitemap(baseUrl).catch(() => []);
      console.log(`Found ${sitemapUrls.length} URLs from sitemap`);

      // Combine all URLs and filter to same domain
      const allUrls = [...relevantUrls, ...navFooterLinks, ...sitemapUrls];

      // Filter URLs by domain, remove duplicates, and filter out URLs with skipUrlPatterns
      let skippedCount = 0;
      let invalidCount = 0;

      for (const url of allUrls) {
        // Skip invalid URLs (mailto:, tel:, etc.)
        if (!isValidUrl(url)) {
          invalidCount++;
          continue;
        }

        try {
          if (url && getDomainFromUrl(url) === domain) {
            // Skip URLs containing patterns in skipUrlPatterns
            const shouldSkip = this.options.skipUrlPatterns.some((pattern) =>
              url.toLowerCase().includes(pattern.toLowerCase())
            );

            if (shouldSkip) {
              skippedCount++;
              continue;
            }

            // Remove file extensions from URL
            const cleanedUrl = removeFileExtensions(url);
            uniqueUrls.add(cleanedUrl);
          }
        } catch (error) {
          console.warn(`Invalid URL: ${error.name}: ${error.message}`);
          invalidCount++;
        }
      }

      console.log(
        `Skipped ${skippedCount} URLs matching patterns: ${this.options.skipUrlPatterns.join(", ")}`
      );
      console.log(`Skipped ${invalidCount} invalid URLs (mailto:, tel:, etc.)`);

      // Add the base URL to ensure it's included
      uniqueUrls.add(baseUrl);

      // Convert to array and prioritize by relevance
      return this.prioritizeUrls([...uniqueUrls]);
    } catch (error) {
      console.error(`Error collecting URLs to crawl from ${baseUrl}:`, error);
      return [baseUrl]; // Return at least the base URL on error
    }
  }

  /**
   * Extract URLs that match relevant keywords
   * @param {string} html - HTML content
   * @param {string} baseUrl - Base URL for resolving relative links
   * @returns {string[]} - Array of relevant URLs
   */
  extractRelevantUrls(html, baseUrl) {
    const $ = cheerio.load(html);
    const relevantUrls = new Set();

    // Iterate over all anchor tags to find relevant URLs
    $("a").each((_, el) => {
      const href = $(el).attr("href");
      if (href) {
        try {
          // Skip mailto: links - they're not URLs to crawl
          if (href.toLowerCase().startsWith("mailto:")) {
            // Extract the email from mailto: link instead

            return;
          }

          // Skip tel: links
          if (href.toLowerCase().startsWith("tel:")) {
            return;
          }

          const fullUrl = new URL(href, baseUrl).href;
          const lowercaseUrl = fullUrl.toLowerCase();

          // Check if URL contains relevant keywords
          if (
            this.options.relevantKeywords.some((keyword) =>
              lowercaseUrl.includes(keyword)
            )
          ) {
            relevantUrls.add(fullUrl);
          }
        } catch (error) {
          // Ignore malformed URLs
        }
      }
    });

    return [...relevantUrls];
  }

  /**
   * Extract URLs from navigation and footer
   * @param {string} html - HTML content
   * @param {string} baseUrl - Base URL for resolving relative links
   * @returns {string[]} - Array of URLs from navigation and footer
   */
  extractNavFooterUrls(html, baseUrl) {
    const $ = cheerio.load(html);
    const urls = new Set();

    // Find all links in nav and footer tags
    $(
      "nav a, header a, footer a, .footer a, .navbar a, .menu a, .header a"
    ).each((_, el) => {
      const href = $(el).attr("href");
      if (href) {
        try {
          // Skip mailto: links - they're not URLs to crawl
          if (href.toLowerCase().startsWith("mailto:")) {
            // Extract the email from mailto: link instead
            const email = href
              .replace(/^mailto:/i, "")
              .split("?")[0]
              .trim()
              ?.toLowerCase();
            if (email) this.relevantUrls.add(email);
            return;
          }

          // Skip tel: links
          if (href.toLowerCase().startsWith("tel:")) {
            return;
          }

          // Construct full URL
          const fullUrl = new URL(href, baseUrl).href;
          urls.add(fullUrl);
        } catch (error) {
          // Ignore malformed URLs
        }
      }
    });

    return [...urls];
  }

  /**
   * Check sitemap.xml for additional URLs
   * @param {string} baseUrl - Base URL of the website
   * @returns {Promise<string[]>} - Array of URLs from sitemap
   */
  async checkSitemap(baseUrl) {
    try {
      const sitemapUrl = new URL("/sitemap.xml", baseUrl).href;
      const html = await fetchHtmlWithRetry(sitemapUrl, 1, false, false).catch(
        () => null
      );

      if (!html) {
        return [];
      }

      const parser = new xmlParser.XMLParser();
      const sitemap = parser.parse(html);

      // Check if sitemap has urlset and url entries
      if (sitemap.urlset && sitemap.urlset.url) {
        const urls = Array.isArray(sitemap.urlset.url)
          ? sitemap.urlset.url
          : [sitemap.urlset.url];

        // Filter URLs in sitemap that contain relevant keywords
        const relevantUrls = urls
          .filter((item) =>
            this.options.relevantKeywords.some((keyword) =>
              item.loc.toLowerCase().includes(keyword)
            )
          )
          .map((item) => item.loc);

        return relevantUrls;
      }

      return [];
    } catch (error) {
      console.error(`Error checking sitemap for ${baseUrl}:`, error);
      return [];
    }
  }

  /**
   * Prioritize URLs based on relevant keywords
   * @param {string[]} urls - Array of URLs to prioritize
   * @returns {string[]} - Prioritized array of URLs
   */
  prioritizeUrls(urls) {
    // First sort by whether the URL contains contact or about
    const highPriorityKeywords = ["contact", "about", "team", "staff"];

    return urls.sort((a, b) => {
      const aLower = a.toLowerCase();
      const bLower = b.toLowerCase();

      // High priority keywords get highest precedence
      const aHasPriority = highPriorityKeywords.some((keyword) =>
        aLower.includes(keyword)
      );
      const bHasPriority = highPriorityKeywords.some((keyword) =>
        bLower.includes(keyword)
      );

      if (aHasPriority && !bHasPriority) return -1;
      if (!aHasPriority && bHasPriority) return 1;

      // Other relevant keywords come next
      const aHasKeyword = this.options.relevantKeywords.some((keyword) =>
        aLower.includes(keyword)
      );
      const bHasKeyword = this.options.relevantKeywords.some((keyword) =>
        bLower.includes(keyword)
      );

      if (aHasKeyword && !bHasKeyword) return -1;
      if (!aHasKeyword && bHasKeyword) return 1;

      // Shorter paths come next (usually more important pages)
      const aPathLength = new URL(a).pathname.split("/").filter(Boolean).length;
      const bPathLength = new URL(b).pathname.split("/").filter(Boolean).length;

      return aPathLength - bPathLength;
    });
  }

  /**
   * Crawl a single page to extract emails
   * @param {string} pageUrl - URL of the page to crawl
   */
  async crawlPage(pageUrl) {
    // Skip invalid URL protocols like mailto: and tel:
    if (pageUrl.startsWith("mailto:") || pageUrl.startsWith("tel:")) {
      // If it's a mailto: link, extract the email directly
      if (pageUrl.startsWith("mailto:")) {
        const email = pageUrl
          .replace(/^mailto:/i, "")
          .split("?")[0]
          .trim()
          ?.toLowerCase();
      }
      console.log(`Skipping non-HTTP protocol URL: ${pageUrl}`);
      return;
    }

    // Mark URL as visited to avoid duplicates
    this.visitedUrls.add(pageUrl);

    try {
      // Fetch HTML content
      const html = await fetchHtmlWithRetry(
        pageUrl,
        2,
        this.options.useProxy,
        this.options.saveHtml,
        this.failedCount,
        this.options.maxProxyPages,
        this.addFailedUrl.bind(this)
      );

      // Extract emails from the page
      const pageEmails = extractAllEmails(html, pageUrl);
      console.log(`Found ${pageEmails.length} emails on ${pageUrl}`);
      
      if (pageEmails.length > 0) {
        // Check if any were filtered by blacklist
        const emailDomains = pageEmails.map(email => email.split('@')[1]?.toLowerCase());
        const blacklistedDomains = this.options.blacklistedDomains || [];
        const filteredDomains = emailDomains.filter(domain => blacklistedDomains.includes(domain));
        
        if (filteredDomains.length > 0) {
          console.log(`Ignored ${filteredDomains.length} blacklisted email domains: ${filteredDomains.join(', ')}`);
        }
      }

      // Add emails to the set
      for (const email of pageEmails) {
        const resultantEmail = String(email).toLowerCase();
        this.emails.add(resultantEmail);
        this.addEmailUrl(resultantEmail, pageUrl);
        // Check if we've reached the maximum
        if (this.emails.size >= this.options.maxEmails) {
          return;
        }
      }
    } catch (error) {
      console.error(`Error crawling page ${pageUrl}:`, error);
    }
  }
  addFailedUrl(url) {
    this.failedCount.count++;
    this.failedUrls.add(url);
  }
  addEmailUrl(email, url) {
    if (!this.emailUrls[email] || !this.emailUrls[email] === undefined) {
      this.emailUrls[email] = [url];
    } else {
      this.emailUrls[email].push(url);
    }
  }
}

if (require.main === module) {
  const emailExtractor = new EmailExtractor();
  const url = "http://www.lemieux.com";
  emailExtractor.extractEmails(url).then(result => {
    console.log(result);
  });
}
module.exports = EmailExtractor;
