const fs = require("fs").promises;
const { createReadStream } = require("fs");
const path = require("path");
const csv = require("csv-parse");
const { stringify } = require("csv-stringify/sync");
const { transformData } = require("../schemas/emailSchemaMap");

/**
 * Generates a CSV file from email data
 * @param {Object} emailData - Object with website as key and emails array as value
 * @param {string} outputPath - Path to save the CSV file
 * @returns {Promise<string>} - Path to the generated CSV file
 */
async function generateCSV(emailData, outputPath = null) {
  // Create filename with timestamp if not provided
  if (!outputPath) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    outputPath = path.join(
      __dirname,
      "../../",
      `website_emails_${timestamp}.csv`
    );
  }

  // Prepare records for CSV
  const records = [];
  for (const [website, info] of Object.entries(emailData)) {
    const emails = info.emails || [];
    const status = info.status || "Unknown";

    if (emails.length === 0) {
      records.push({
        website,
        email: "N/A",
        email_status: status,
      });
    } else {
      emails.forEach((email) => {
        records.push({
          website,
          email,
          email_status: status,
        });
      });
    }
  }

  try {
    // Create directory if it doesn't exist
    const dir = path.dirname(outputPath);
    await fs.mkdir(dir, { recursive: true }).catch(() => {});

    // Write CSV file
    const csv = stringify(records, {
      header: true,
      columns: ["website", "email", "email_status"],
    });

    await fs.writeFile(outputPath, csv);
    console.log(`CSV file has been generated successfully at ${outputPath}`);
    return outputPath;
  } catch (error) {
    console.error("Error generating CSV file:", error);
    throw error;
  }
}

/**
 * Reads a CSV file and transforms the data using schema mapping
 * @param {string} filePath - Path to CSV file
 * @param {Object} schemaMap - Schema mapping object
 * @returns {Promise<Array>} - Array of transformed objects
 */
async function readAndTransformCsv(filePath, schemaMap) {
  try {
    const fileContent = await fs.readFile(filePath, "utf8");
    return new Promise((resolve, reject) => {
      const results = [];

      csv
        .parse(fileContent, {
          columns: true,
          skip_empty_lines: true,
        })
        .on("data", (row) => {
          const transformedRow = transformData(row, schemaMap);
          results.push(transformedRow);
        })
        .on("end", () => {
          resolve(results);
        })
        .on("error", (error) => {
          reject(error);
        });
    });
  } catch (error) {
    console.error(`Error reading CSV file ${filePath}:`, error);
    throw error;
  }
}

/**
 * Process CSV in chunks, allowing for batch processing
 * @param {string} inputFile - Path to input CSV
 * @param {string} outputFile - Path to output CSV
 * @param {Object} schemaMap - Schema mapping
 * @param {Function} processFunction - Function to process each row
 * @param {number} startRow - Starting row index
 * @param {number} endRow - Ending row index
 * @param {number} batchSize - Number of records to process at once
 * @returns {Promise<void>}
 */
async function processCsvInBatches(
  inputFile,
  outputFile,
  schemaMap,
  processFunction,
  startRow = 0,
  endRow = Infinity,
  batchSize = 10
) {
  try {
    // Read input file
    const inputData = await fs.readFile(inputFile, "utf8");

    // Parse input CSV
    const inputRows = await new Promise((resolve, reject) => {
      const rows = [];
      csv
        .parse(inputData, {
          columns: true,
          skip_empty_lines: true,
        })
        .on("data", (row) => {
          rows.push(row);
        })
        .on("end", () => {
          resolve(rows);
        })
        .on("error", (error) => {
          reject(error);
        });
    });

    // Calculate actual end row
    const actualEndRow = Math.min(endRow, inputRows.length);
    console.log(
      `Processing rows ${startRow} to ${actualEndRow} of ${inputRows.length}`
    );

    // Initialize output data
    let outputRows = [];
    try {
      const outputData = await fs.readFile(outputFile, "utf8").catch(() => "");
      if (outputData) {
        outputRows = await new Promise((resolve, reject) => {
          const rows = [];
          csv
            .parse(outputData, {
              columns: true,
              skip_empty_lines: true,
            })
            .on("data", (row) => {
              rows.push(row);
            })
            .on("end", () => {
              resolve(rows);
            })
            .on("error", (error) => {
              reject(error);
            });
        });
      }
    } catch (error) {
      console.log(`Output file ${outputFile} does not exist yet or is empty.`);
    }

    // Process in batches
    for (let i = startRow; i < actualEndRow; i += batchSize) {
      const batchEnd = Math.min(i + batchSize, actualEndRow);
      console.log(`Processing batch: ${i} to ${batchEnd}`);

      const batch = inputRows.slice(i, batchEnd);

      // Transform data using schema map
      const transformedBatch = batch.map((row) =>
        transformData(row, schemaMap)
      );

      // Process each row in the batch
      for (let j = 0; j < transformedBatch.length; j++) {
        const currentRow = transformedBatch[j];
        const originalRow = batch[j];

        // Skip if email_status column has any value
        if (originalRow.email_status && originalRow.email_status.trim() !== '') {
          console.log(`Skipping row ${i + j + 1} (already has status: ${originalRow.email_status})`);
          continue;
        }

        try {
          // Process the row
          const result = await processFunction(currentRow, i + j);

          // Update the input row status
          inputRows[i + j].email_status = result.status || "Done";

          // Add results to output
          if (result.emails && result.emails.length > 0) {
            result.emails.forEach((email) => {
              outputRows.push({
                ...originalRow,
                scraped_websites: result.emailUrls
                  ? (result.emailUrls ? result.emailUrls[email] : []).join(
                      " | "
                    )
                  : "",
                email,
                email_status: result.status,
              });
            });
          } else {
            // Add a row even when no emails found
            outputRows.push({
              ...originalRow,
              scraped_websites: "",
              email: "N/A",
              email_status: result.status,
            });
          }
        } catch (error) {
          console.error(`Error processing row ${i + j + 1}:`, error);
          inputRows[i + j].email_status = `Error: ${error.message}`;
        }

        // Write updates to files
        await updateFiles(inputFile, outputFile, inputRows, outputRows);
      }
    }

    console.log("CSV processing completed. Files updated successfully.");
  } catch (error) {
    console.error("Error processing CSV:", error);
    throw error;
  }
}

/**
 * Update input and output files with current data
 * @param {string} inputFile - Path to input file
 * @param {string} outputFile - Path to output file
 * @param {Array} inputRows - Input data rows
 * @param {Array} outputRows - Output data rows
 * @returns {Promise<void>}
 */
async function updateFiles(inputFile, outputFile, inputRows, outputRows) {
  // Update input file
  const inputHeaders = Object.keys(inputRows[0] || {});
  const updatedInputCSV = stringify(inputRows, {
    header: true,
    columns: inputHeaders,
  });
  await fs.writeFile(inputFile, updatedInputCSV);

  // Update output file
  if (outputRows.length > 0) {
    const outputHeaders = Object.keys(outputRows[0]);
    const updatedOutputCSV = stringify(outputRows, {
      header: true,
      columns: outputHeaders,
    });
    await fs.writeFile(outputFile, updatedOutputCSV);
  }
}

module.exports = {
  generateCSV,
  readAndTransformCsv,
  processCsvInBatches,
};
