const { extractDomain } = require("../../../../utils/domainHelper");

/**
 * Normalizes a URL to ensure it has a protocol
 * Note: Does NOT convert HTTP to HTTPS by default to allow the original protocol to be tried first
 * 
 * @param {string} url - The URL to normalize
 * @param {boolean} forceHttps - Whether to force conversion to HTTPS
 * @returns {string} - The normalized URL
 */
function normalizeToHttps(url, forceHttps = false) {
  if (!url) return url;
  
  try {
    // Handle case where URL has no protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }
    
    // Replace http:// with https:// only if forceHttps is true
    if (forceHttps && url.startsWith('http://')) {
      return url.replace(/^http:\/\//i, 'https://');
    }
    
    // Otherwise keep the original URL
    return url;
  } catch (error) {
    console.error(`Error normalizing URL ${url}: ${error.message}`);
    return url; // Return original URL if there's an error
  }
}

/**
 * Cleans and standardizes a URL
 * @param {string} url - The URL to clean
 * @returns {string} - The cleaned URL
 */
function cleanUrl(url) {
  if (!url) return '';
  
  try {
    // Normalize to ensure protocol, but don't force HTTPS
    url = normalizeToHttps(url, false);
    
    // Create URL object
    const urlObj = new URL(url);
    let hostname = urlObj.hostname;
    
    // Add www. if not present
    if (!hostname.startsWith("www.") && !hostname.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/)) {
      hostname = "www." + hostname;
    }
    
    // Preserve the original protocol
    const protocol = urlObj.protocol;
    
    return `${protocol}//${hostname}${urlObj.pathname}`;
  } catch (error) {
    // If URL parsing fails, try adding https:// and try again
    if (!url.startsWith('http')) {
      try {
        return cleanUrl(`https://${url}`);
      } catch (innerError) {
        console.error(`Error cleaning URL ${url}: ${innerError.message}`);
        return url;
      }
    }
    
    console.error(`Error cleaning URL ${url}: ${error.message}`);
    return url;
  }
}

/**
 * Removes file extensions from URLs
 * @param {string} url - The URL to process
 * @returns {string} - URL without file extensions
 */
function removeFileExtensions(url) {
  if (!url) return '';
  
  try {
    const urlObj = new URL(url);
    const path = urlObj.pathname;
    
    // Check if path ends with a common file extension
    const fileExtRegex = /\.(html|htm|php|asp|aspx|jsp|pdf|doc|docx|xls|xlsx|ppt|pptx)$/i;
    if (fileExtRegex.test(path)) {
      // Remove the file extension
      const newPath = path.replace(fileExtRegex, '');
      urlObj.pathname = newPath;
      return urlObj.toString();
    }
    
    return url;
  } catch (error) {
    console.error(`Error removing file extensions from URL ${url}: ${error.message}`);
    return url;
  }
}

/**
 * Gets a URL from a domain
 * @param {string} domain - The domain to convert to URL
 * @returns {string} - Full URL
 */
function getUrlFromDomain(domain) {
  if (!domain) return '';
  // Simply add https:// to domain without further modifications
  if (!domain.startsWith('http://') && !domain.startsWith('https://')) {
    return `https://${domain}`;
  }
  return domain;
}

/**
 * Extracts domain from URL
 * @param {string} url - The URL to extract domain from
 * @returns {string} - The extracted domain
 */
function getDomainFromUrl(url) {
  try {
    return extractDomain(url);
  } catch (error) {
    console.error(`Error extracting domain from URL ${url}: ${error.message}`);
    return '';
  }
}

/**
 * Checks if a URL is valid for crawling (not mailto: or tel:)
 * @param {string} url - The URL to check
 * @returns {boolean} - Whether the URL is valid for crawling
 */
function isValidUrl(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  // Check for common non-HTTP protocols that shouldn't be crawled
  if (url.toLowerCase().startsWith('mailto:') || 
      url.toLowerCase().startsWith('tel:') ||
      url.toLowerCase().startsWith('javascript:') ||
      url.toLowerCase().startsWith('ftp:') ||
      url.toLowerCase().startsWith('file:')) {
    return false;
  }
  
  try {
    // Try to parse the URL
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

module.exports = {
  normalizeToHttps,
  cleanUrl,
  removeFileExtensions,
  getUrlFromDomain,
  getDomainFromUrl,
  isValidUrl
}; 