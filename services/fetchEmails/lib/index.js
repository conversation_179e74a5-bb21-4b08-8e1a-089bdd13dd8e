const path = require("path");
const EmailExtractor = require("./core/emailExtractor");
const { normalizeToHttps, getUrlFromDomain } = require("./utils/urlUtils");
const { processCsvInBatches } = require("./utils/csvUtils");
const { emailSchemaMap } = require("./schemas/emailSchemaMap");
const { DEFAULT_OPTIONS } = require("./constants");

/**
 * Process a single row from CSV
 * @param {Object} row - CSV row data
 * @param {number} rowIndex - Index of the row
 * @param {Object} options - Processing options
 * @returns {Promise<{emails: string[], status: string}>}
 */
async function processRow(row, rowIndex, options = {}) {
  console.log(`Processing row ${rowIndex + 1}`);

  // Merge default options with provided options
  const finalOptions = { ...DEFAULT_OPTIONS, ...options };

  // Create email extractor with options
  const extractor = new EmailExtractor(finalOptions);

  let websiteUrl;

  // Check for website URL in the row
  if (row.website && row.website.trim() !== "") {
    websiteUrl = row.website;
    console.log(`Using website URL: ${websiteUrl}`);
  }
  // Check for domain in the row
  else if (row.domain && row.domain.trim() !== "") {
    websiteUrl = getUrlFromDomain(row.domain);
    console.log(`Using domain URL: ${websiteUrl}`);
  }
  // No website or domain found
  else {
    console.log(`No website or domain found for row ${rowIndex + 1}`);
    return {
      emails: [],
      status: "No Website/Domain Found",
    };
  }

  // Normalize URL to ensure it has a protocol but don't force HTTPS
  websiteUrl = normalizeToHttps(websiteUrl, false);

  try {
    // Extract emails from the website
    const result = await extractor.extractEmails(websiteUrl);
    console.log(
      `Found ${result.emails.length} emails with status: ${result.status}`
    );
    return result;
  } catch (error) {
    console.error(`Error processing row ${rowIndex + 1}:`, error);
    return {
      emails: [],
      status: `Error: ${error.message}`,
    };
  }
}

/**
 * Process a CSV file to extract emails from websites
 * @param {string} inputFile - Path to input CSV file
 * @param {string} outputFile - Path to output CSV file
 * @param {number} startRow - Starting row index (0-based)
 * @param {number} endRow - Ending row index (exclusive)
 * @param {number} batchSize - Number of rows to process in a batch
 * @param {Object} options - Additional options for rate limiting and processing
 * @returns {Promise<void>}
 */
async function processCSV(
  inputFile,
  outputFile,
  startRow = 0,
  endRow = Infinity,
  batchSize = 1,
  options = {}
) {
  try {
    // Resolve file paths
    const resolvedInputFile = path.resolve(inputFile);
    const resolvedOutputFile = path.resolve(outputFile);
    console.log(`Processing CSV file: ${resolvedInputFile}`);
    console.log(`Output file: ${resolvedOutputFile}`);
    console.log(`Rows: ${startRow} to ${endRow}, Batch size: ${batchSize}`);

    // If rate limiting options are provided, show them
    if (options.maxConcurrent || options.minTime) {
      console.log(
        `Rate limiting: ${options.maxConcurrent || 2} concurrent requests, ${options.minTime || 1000}ms between requests`
      );
    }
    console.log(resolvedInputFile, "h");
    // Process CSV in batches
    await processCsvInBatches(
      resolvedInputFile,
      resolvedOutputFile,
      emailSchemaMap,
      (row, rowIndex) => processRow(row, rowIndex, options),
      startRow,
      endRow,
      batchSize
    );

    console.log("CSV processing completed successfully.");
  } catch (error) {
    console.error("Error processing CSV:", error);
    throw error;
  }
}

/**
 * Extract emails from a single website
 * @param {string} websiteUrl - Website URL or domain
 * @param {Object} options - Options for email extraction including rate limiting
 * @returns {Promise<{emails: string[], status: string}>}
 */
async function extractEmails(websiteUrl, options = {}) {
  // Merge default options with provided options
  const finalOptions = { ...DEFAULT_OPTIONS, ...options };

  // Create email extractor with options
  const extractor = new EmailExtractor(finalOptions);

  // Normalize URL to ensure it has a protocol but don't force HTTPS
  websiteUrl = normalizeToHttps(websiteUrl, false);

  try {
    // Extract emails from the website
    return await extractor.extractEmails(websiteUrl);
  } catch (error) {
    console.error(`Error extracting emails from ${websiteUrl}:`, error);
    return {
      emails: [],
      status: `Error: ${error.message}`,
    };
  }
}

/**
 * Process emails to remove numbered duplicates
 * @param {Set<string>} emailSet - Set of email addresses
 * @returns {Set<string>} - Processed set of email addresses
 */
function processNumberedEmails(emailSet) {
  const emailMap = new Map();
  const toRemove = new Set();

  // First pass: Build map of email bases
  for (const email of emailSet) {
    const [localPart, domain] = email.split('@');
    const match = localPart.match(/^(\d+)(.+)$/);
    
    if (match) {
      // If email starts with number, store the base part
      const [, , basePart] = match;
      emailMap.set(`${basePart}@${domain}`, true);
    } else {
      // Store non-numbered emails
      emailMap.set(email, false);
    }
  }

  // Second pass: Mark numbered emails for removal if base exists
  for (const email of emailSet) {
    const [localPart, domain] = email.split('@');
    const match = localPart.match(/^(\d+)(.+)$/);
    
    if (match) {
      const [, , basePart] = match;
      const baseEmail = `${basePart}@${domain}`;
      
      if (emailMap.has(baseEmail)) {
        toRemove.add(email);
      }
    }
  }

  // Remove marked emails from the set
  for (const email of toRemove) {
    emailSet.delete(email);
  }

  return emailSet;
}


module.exports = {
  processCSV,
  extractEmails,
  EmailExtractor,
};
