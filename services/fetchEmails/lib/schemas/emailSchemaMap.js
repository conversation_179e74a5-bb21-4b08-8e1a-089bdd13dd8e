const emailSchemaMap = {
  website: ["Website", "website", "website_url", "urls", "domain", "Domain", "company_website"],
  email: ["Email", "email", "eMail", "Contact Email", "contact_email"],
  email_status: ["Email Status", "email_status", "eMail Status", "Contact Status", "status"],
  domain: ["Domain", "domain", "TLD", "tld"],
  amazon_seller_id: [
    "seller_id",
    "Amazon Seller ID",
    "amazon_seller_id",
    "Seller ID",
    "amazonSellerId",
  ],
  company_name: ["Company Name", "company_name", "Name", "name", "Seller Name", "Business Name", "business_name"],
  source: ["Source", "source", "lookup_source", "Lookup Source", "Data Source"],
};

// Function to transform data using the schema map
function transformData(data, schemaMap) {
  const transformedData = {};
  for (const [key, possibleHeaders] of Object.entries(schemaMap)) {
    for (const header of possibleHeaders) {
      if (data[header] !== undefined) {
        transformedData[key] = data[header];
        break;
      }
    }
  }
  return transformedData;
}

module.exports = {
  emailSchemaMap,
  transformData,
}; 