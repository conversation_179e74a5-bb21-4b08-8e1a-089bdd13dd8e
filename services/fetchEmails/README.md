# Email Extraction Service

This service extracts email addresses from websites by crawling their pages and looking for valid email patterns.

## Features

- Process CSV files with website URLs and output emails with status
- Use schema mapping for flexible input/output formats
- Automatic HTTP to HTTPS conversion
- Smart URL prioritization for relevant pages (contact, about, etc.)
- Extract emails from both text content and mailto: links
- Handle large CSV files with batch processing
- Filter invalid domains and prioritize popular ones
- Check site accessibility and status (blocked, error, etc.)
- Remove file extensions from URLs for better crawling
- Fallback system: URL → HTTPS → Proxy

## Folder Structure

```
fetchEmails/
├── index.js                   # Main entry point
├── lib/                       # Core functionality
│   ├── index.js               # Library entry point
│   ├── constants.js           # Centralized configuration constants
│   ├── core/                  # Core extraction logic
│   │   └── emailExtractor.js  # Email extraction class
│   ├── schemas/               # Schema mappings
│   │   └── emailSchemaMap.js  # Email schema mapping
│   ├── utils/                 # Utility functions
│   │   ├── csvUtils.js        # CSV handling utilities
│   │   ├── emailUtils.js      # Email extraction utilities
│   │   ├── httpUtils.js       # HTTP request utilities
│   │   └── urlUtils.js        # URL processing utilities
│   └── helpers/               # Helper functions
│       └── domainHelper.js    # Domain-related helpers
├── htmlData/                  # Storage for HTML files (created at runtime)
├── package.json               # Dependencies
└── README.md                  # Documentation
```

## Usage

### Process a CSV file

```bash
node index.js input.csv output.csv [startRow] [endRow] [batchSize] [skipUrlPatterns]
```

### Arguments

- `input.csv`: Path to the input CSV file (required)
- `output.csv`: Path to the output CSV file (required)
- `startRow`: Starting row index (optional, default: 0)
- `endRow`: Ending row index (optional, default: Infinity)
- `batchSize`: Number of rows to process in a batch (optional, default: 10)
- `skipUrlPatterns`: Comma-separated list of URL patterns to skip (optional, default: "/products,/items")

### Input CSV Format

The input CSV should contain at least one of the following columns:
- `website`: Website URL
- `domain`: Domain name (alternative to website)

### Output CSV Format

The output CSV will contain the following columns:
- All original columns from the input CSV
- `email`: Extracted email address
- `email_status`: Status of the extraction (Done, No Emails Found, Blocked, etc.)

## Configuration Options

The email extractor can be configured with the following options, which are centralized in `lib/constants.js`:

- `maxEmails`: Maximum number of emails to extract per website (default: 10)
- `maxPages`: Maximum pages to crawl per website (default: 100)
- `useProxy`: Whether to use proxy as fallback (default: true)
- `saveHtml`: Whether to save HTML content for debugging (default: false)
- `skipUrlPatterns`: URL patterns to skip when crawling (includes '/products', '/items', '/shop', etc.)
- `relevantKeywords`: Keywords to prioritize when collecting URLs (includes 'about', 'contact', etc.)
- `maxConcurrent`: Maximum concurrent requests (default: 25)
- `minTime`: Minimum time between requests in ms (default: 0)
- `reservoirRefreshAmount`: Number of requests allowed per interval (default: 100)
- `reservoirRefreshInterval`: Interval for refreshing request allowance in ms (default: 10000)

### Skip URL Patterns

The service comes with a predefined list of URL patterns to skip (in `lib/constants.js`):

```javascript
const SKIP_URL_PATTERNS = [
  '/products', '/items', '/product', '/shop', '/cart', 
  '/order', '/checkout', '/login', '/register', 
  // ... and many more ...
];
```

You can override these patterns when calling the service via command line or API.

## API Usage

```javascript
const { processCSV, extractEmailsFromWebsite } = require('./services/fetchEmails');

// Process a CSV file
await processCSV('input.csv', 'output.csv', 0, 100, 10, {
  // Custom options override the defaults in constants.js
  skipUrlPatterns: ['/products', '/items', '/shop', '/category'],
  maxPages: 150, // Increase from default 100
  maxConcurrent: 30 // Increase concurrent requests
});

// Extract emails from a single website
const result = await extractEmailsFromWebsite('https://example.com', {
  // Custom options override the defaults in constants.js
  maxEmails: 20, // Increase from default 10
  useProxy: true,
  saveHtml: true
});
console.log(result.emails, result.status);
```

All configuration options are centrally defined in `lib/constants.js`. You can override any specific option when calling the APIs.

## Schema Mapping

Similar to schema.js, this module includes schema mapping functionality that normalizes field names. The mapping is defined in `lib/schemas/emailSchemaMap.js` and can be extended as needed. 