const cron = require('node-cron');
const MXRecordChecker = require('./index');

class MXRecordScheduler {
  constructor() {
    this.mxChecker = new MXRecordChecker();
    this.isRunning = false;
    this.lastRunTime = null;
    this.nextRunTime = null;
    this.stats = {
      totalRuns: 0,
      successfulRuns: 0,
      failedRuns: 0,
      lastError: null
    };
  }

  /**
   * Start the cron job scheduler
   */
  start() {
    // Don't start scheduler in test environment or when NODE_ENV is not set
    const nodeEnv = process.env.NODE_ENV;
    
    if (!nodeEnv || nodeEnv === 'test') {
      console.log(`🚫 MX Record Checker scheduler disabled (NODE_ENV: ${nodeEnv || 'not set'})`);
      console.log('   Scheduler will only run in production, staging, or development environments');
      console.log('   To enable scheduler, set NODE_ENV to production, staging, or development');
      return;
    }

    console.log(`🚀 Starting MX Record Checker scheduler (NODE_ENV: ${nodeEnv})...`);
    
    // Run daily at 2:00 AM
    this.cronJob = cron.schedule('0 2 * * *', async () => {
      await this.runMXRecordCheck();
    }, {
      scheduled: true,
      timezone: "UTC"
    });

    // Optional: Run immediately on startup for testing
    // Uncomment the line below if you want to run immediately
    // this.runMXRecordCheck();
    
    this.updateNextRunTime();
    console.log(`✅ MX Record Checker scheduled to run daily at 2:00 AM UTC`);
    console.log(`📅 Next run: ${this.nextRunTime}`);
    console.log(`🔧 To disable scheduler, set NODE_ENV=test or unset NODE_ENV`);
  }

  /**
   * Stop the cron job scheduler
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      console.log('MX Record Checker scheduler stopped');
    }
  }

  /**
   * Update the next run time
   */
  updateNextRunTime() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(2, 0, 0, 0); // 2:00 AM
    
    // If it's already past 2:00 AM today, schedule for tomorrow
    if (now.getHours() >= 2) {
      this.nextRunTime = tomorrow;
    } else {
      // Schedule for today at 2:00 AM
      const today = new Date(now);
      today.setHours(2, 0, 0, 0);
      this.nextRunTime = today;
    }
  }

  /**
   * Run the MX record check process
   */
  async runMXRecordCheck() {
    if (this.isRunning) {
      console.log('MX Record check is already running, skipping...');
      return;
    }

    this.isRunning = true;
    this.stats.totalRuns++;
    console.log(`\n=== MX Record Check Started at ${new Date().toISOString()} ===`);
    
    try {
      // Get processing stats before starting
      const beforeStats = await this.mxChecker.getProcessingStats();
      console.log('Before processing stats:', JSON.stringify(beforeStats, null, 2));
      
      // Run the MX record checking process
      await this.mxChecker.processAllRecords();
      
      // Get processing stats after completion
      const afterStats = await this.mxChecker.getProcessingStats();
      console.log('After processing stats:', JSON.stringify(afterStats, null, 2));
      
      this.stats.successfulRuns++;
      this.stats.lastError = null;
      this.lastRunTime = new Date();
      
      console.log(`=== MX Record Check Completed at ${new Date().toISOString()} ===\n`);
      
    } catch (error) {
      this.stats.failedRuns++;
      this.stats.lastError = error.message;
      console.error('Error in MX Record check:', error);
      
      // You might want to send notifications here
      // await this.sendErrorNotification(error);
      
    } finally {
      this.isRunning = false;
      this.updateNextRunTime();
    }
  }

  /**
   * Run MX record check immediately (for testing or manual triggers)
   */
  async runNow() {
    console.log('Running MX Record check immediately...');
    await this.runMXRecordCheck();
  }

  /**
   * Check if scheduler is enabled for current environment
   */
  isEnabled() {
    const nodeEnv = process.env.NODE_ENV;
    return nodeEnv && nodeEnv !== 'test';
  }

  /**
   * Get scheduler status and statistics
   */
  getStatus() {
    return {
      enabled: this.isEnabled(),
      nodeEnv: process.env.NODE_ENV || 'not set',
      isRunning: this.isRunning,
      isScheduled: this.cronJob ? this.cronJob.scheduled : false,
      lastRunTime: this.lastRunTime,
      nextRunTime: this.nextRunTime,
      stats: this.stats
    };
  }

  /**
   * Send error notification (placeholder for future implementation)
   */
  async sendErrorNotification(error) {
    // Implement your notification logic here
    // For example, send to Slack, email, etc.
    console.log('Error notification:', error.message);
  }

  /**
   * Health check endpoint
   */
  async healthCheck() {
    const status = this.getStatus();
    const timeSinceLastRun = status.lastRunTime ? Date.now() - status.lastRunTime.getTime() : null;
    const maxAllowedGap = 25 * 60 * 60 * 1000; // 25 hours
    
    return {
      healthy: timeSinceLastRun ? timeSinceLastRun < maxAllowedGap : true,
      status,
      timeSinceLastRun,
      maxAllowedGap
    };
  }
}

// Export singleton instance
const scheduler = new MXRecordScheduler();

if (require.main === module) {
  scheduler.runMXRecordCheck();
}

module.exports = scheduler; 