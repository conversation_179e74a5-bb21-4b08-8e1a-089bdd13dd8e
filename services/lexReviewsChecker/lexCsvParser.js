const prisma = require("../../database/prisma/getPrismaClient");
const csvtojson = require("csvtojson");
const Joi = require("joi");

const rowSchema = Joi.object({
  ASIN: Joi.string().allow("").optional(),
  asin: Joi.string().allow("").optional(),
  "Seller Name": Joi.string().allow("").optional(),
  Seller: Joi.string().allow("").optional(),
  "Review ID": Joi.string().required(),
  "Review URL": Joi.string().uri().required(),
});

async function validateCSVSchema(jsonArray) {
  const errors = [];

  for (let i = 0; i < jsonArray.length; i++) {
    const { error } = rowSchema.validate(jsonArray[i], { allowUnknown: true });
    if (error) {
      errors.push({
        row: i + 1,
        error: error.details[0].message
      });
    }
  }

  return errors;
}

async function processLexCSV(csvFilePath, userId, job) {
  try {
    // Convert CSV to JSON array
    const jsonArray = await csvtojson().fromFile(csvFilePath);

    // Validate entire CSV before processing
    const validationErrors = await validateCSVSchema(jsonArray);
    if (validationErrors.length > 0) {
      throw {
        type: 'VALIDATION_ERROR',
        errors: validationErrors,
        message: 'CSV validation failed'
      };
    }

    // Process each row if validation passes
    for (let i = 0; i < jsonArray.length; i++) {
      console.log("Processing row:", i + 1);
      await processJsonData(jsonArray[i], userId, job);
    }

    console.log(
      "++++++++++++CSV Parsing and Insertion complete++++++++++++++++++"
    );
  } catch (e) {
    console.log("Error in processCSV", e);
    throw e;
  }
}

async function processJsonData(row, userId, job) {
  // Validate the row
  const { error, value } = rowSchema.validate(row, { allowUnknown: true });
  if (error) {
    console.warn("Invalid row, skipping:", error.message);
    return error;
  }

  // Use fallback/defaults
  const asin = row["ASIN"] || row["asin"] || "UNKNOWN_ASIN";
  const brandName = row["Seller Name"] || row["Seller"] || "UNKNOWN_BRAND";
  const reviewId = row["Review ID"] || "UNKNOWN_REVIEW_ID";
  const reviewUrl = row["Review URL"] || "";
  const reviewer = row["Reviewer"] || "UNKNOWN_REVIEWER";
  const reviewerLink = row["Reviewer Link"] || "UNKNOWN_REVIEWER_LINK";

  const reviewJobId = job.id;
  const inputData = row;

  // Set default run frequency to 1 day if not specified
  const run_frequency = row["Run Frequency"] ? parseInt(row["Run Frequency"]) : 1;

  // Set next_run to tomorrow by default
  const next_run = new Date();
  next_run.setDate(next_run.getDate() + 1);

  try {
    let review = null;
    if (reviewId !== "UNKNOWN_REVIEW_ID") {
      review = await prisma.review.findUnique({
        where: { reviewId },
      });
    }

    if (!review) {
      review = await prisma.review.create({
        data: {
          asin,
          brandName,
          reviewId,
          reviewUrl,
          inputData,
          reviewer,
          reviewerLink,
          reviewJobId,
          next_run,
          run_frequency,
        },
      });

    } else {
      review = await prisma.review.update({
        where: { reviewId },
        data: {
          asin,
          brandName,
          reviewUrl,
          inputData,
          reviewer,
          reviewerLink,
          run_frequency,
        },
      });
    };

    await prisma.reviewOutputData.create({
      data: {
        revId: review.id,
        reviewUrl,
        reviewJobId,
      },
    });

  } catch (e) {
    console.log("Error in inserting review data", e);
  }
}
module.exports = { processLexCSV, processJsonData };
