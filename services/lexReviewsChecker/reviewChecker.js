const prisma = require("../../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { ReviewStatus } = require("@prisma/client");
const { processReview, sendLexSlackNotification } = require("./utils");
const ResurrectedReviewHandler = require("./resurrectedReviewHandler");
require("dotenv").config();

const Bottleneck = require("bottleneck");

// Add random delay function
function getRandomDelay(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Configure Bottleneck for concurrency and rate limiting
const limiter = new Bottleneck({
  maxConcurrent: 5, // Reduced from 40 to 5
  minTime: 500, // Increased from 100ms to 500ms (2 requests per second)
});

//every day at 12:00 AM
cron.schedule(
  "0 21 * * *",
  async () => {
    try {
      // await reviewChecker();
    } catch (error) {
      console.error("Error in scheduled review check:", error);
      await sendLexSlackNotification(
        null,
        "PING",
        "❌ Error in daily review check: " + error.message
      );
    }
  },
  {
    timezone: "Asia/Kolkata", // or your desired timezone
    scheduled: true,
    runOnInit: false, // Set to true if you want it to run immediately when the server starts
  }
);

async function reviewChecker() {
  console.log("Starting daily review check...");

  try {
    // Calculate the last minute of today
    const lastTimestampToday = new Date();
    lastTimestampToday.setHours(23, 59, 59, 999);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let numOfFailedChecks = 0

    // Fetch reviews that are due for checking based on next_run date
    // Include removed reviews to run daily regardless of their next_run date
    const reviews = await prisma.review.findMany({
      where: {
        OR: [
          {
            // Regular reviews that are due based on next_run
            status: {
              not: ReviewStatus.PENDING,
            },
            next_run: {
              lte: lastTimestampToday,
            },
          },
          {
            // Removed reviews - always include them for daily checking
            status: ReviewStatus.REMOVED,
          },
        ],
      },
      orderBy: [
        { next_run: "asc" }, // Process the oldest scheduled reviews first
        { updatedAt: "asc" }, // Then by last update time for those without next_run
      ],
    });

    console.log(`Found ${reviews.length} reviews due for checking (including removed reviews for daily monitoring).`);
    await sendLexSlackNotification(
      null,
      "PING",
      "Starting daily review check...",
      reviews.length
    );
    const removedReviews = [];

    const tasks = reviews.map((review) =>
      limiter.schedule(async () => {
        const reviewCheck = await prisma.review.findUnique({
          where: {
            id: review.id,
          },
          select: {
            next_run: true,
          },
        });

        // Skip regular reviews if they've already been checked today
        // But always process removed reviews regardless of next_run date
        if (review.status !== ReviewStatus.REMOVED && reviewCheck.next_run && reviewCheck.next_run > lastTimestampToday) {
          console.log(`Skipping review ${review.id}, already checked today.`);
          return;
        }
        try {
          // Add random delay to appear more human-like
          const randomDelay = getRandomDelay(1000, 3000); // 1-3 seconds
          await new Promise(resolve => setTimeout(resolve, randomDelay));

          const newStatus = await processReview(review.reviewUrl, 1, false, true); // Added batch processing flag

          // Check if status transition is needed for resurrected reviews
          const updatedReview = await ResurrectedReviewHandler.checkStatusTransition(review, newStatus);

          // Calculate next run date based on run_frequency
          const nextRunDate = new Date();

          // For removed reviews, always schedule for next day (daily)
          // For other reviews, use the run_frequency if available, otherwise default to 7 days
          let frequency;
          if (review.status === ReviewStatus.REMOVED || updatedReview.status === ReviewStatus.REMOVED) {
            frequency = 1; // Daily checks for removed reviews
          } else {
            frequency = review.run_frequency || 7;
          }
          nextRunDate.setDate(nextRunDate.getDate() + frequency);

          // Update review with new status and scheduling info
          await prisma.review.update({
            where: { id: review.id },
            data: {
              status: updatedReview.status,
              next_run: nextRunDate,
              // Increment totalRuns
              totalRuns: review.totalRuns + 1,
              removedAt: updatedReview.removedAt,
              removedHistory: updatedReview.removedHistory || review.removedHistory,
              returnedHistory: updatedReview.returnedHistory || review.returnedHistory,
              comments: updatedReview.comments || review.comments,
            },
          });

          const frequencyText = (review.status === ReviewStatus.REMOVED || updatedReview.status === ReviewStatus.REMOVED) ? "daily" : `every ${frequency} days`;
          console.log(
            `Checked review ${review.id}: ${newStatus}, next check scheduled for ${nextRunDate.toISOString()} (${frequencyText})`
          );
          if (
            updatedReview.status === ReviewStatus.REMOVED &&
            review.status === ReviewStatus.PRESENT
          ) {
            removedReviews.push(updatedReview);
          }
        } catch (error) {
          console.error(`Error checking review ${review.id}:`, error.message);

          // For failed reviews, schedule a retry in 1 day
          const retryDate = new Date();
          retryDate.setDate(retryDate.getDate() + 1);

          // Update status to FAILED and set next_run to retry in 1 hour
          await prisma.review
            .update({
              where: { id: review.id },
              data: {
                status: ReviewStatus.FAILED,
                next_run: retryDate,
                // Increment totalRuns
                totalRuns: review.totalRuns + 1,
              },
            })
            .catch((err) => {
              console.error("Error updating review status:", err.message);
            });
          numOfFailedChecks++;

          console.log(
            `Failed to check review ${review.id}, scheduled retry for ${retryDate.toISOString()}`
          );
        }
      })
    );

    await Promise.all(tasks);

    await sendLexSlackNotification(
      removedReviews,
      "DAILY",
      null,
      reviews.length,
      numOfFailedChecks
    );
    console.log("Daily review check completed.");

    await sendLexSlackNotification(
      null,
      "PING",
      "Daily review check completed successfully!",
      reviews.length
    );
  } catch (error) {
    console.error("Error during daily review check:", error.message);
  }
}
if (require.main === module) {
  // reviewChecker();
}
module.exports = { reviewChecker };
