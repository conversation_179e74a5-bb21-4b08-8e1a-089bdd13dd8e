// reviewChecker.js
const axios = require("axios");
const cheerio = require("cheerio");
require("dotenv").config();
// Environment variables for ScraperAPI configuration
const SCRAPER_API_KEY = process.env.SCRAPER_API_KEY;
const SCRAPER_API_RETRY_404 = process.env.SCRAPER_API_RETRY_404 !== 'false'; // Default to true
const { ReviewStatus } = require("@prisma/client");

const fs = require("fs");
const path = require("path");

const MAX_RETRIES = 3;
const MAX_REMOVED_FILES = 10;
const DEBUG_DIR = path.join(__dirname, "debug_removed_html");

if (!fs.existsSync(DEBUG_DIR)) {
  fs.mkdirSync(DEBUG_DIR);
}

async function processReview(url, attempt = 1, isReverify = false, isBatchProcessing = false) {
  try {
    console.log(
      `Checking [Attempt ${attempt}]${isReverify ? " [Reverify]" : ""}${isBatchProcessing ? " [Batch]" : ""}: ${url}`
    );

    // Log the ScraperAPI configuration being used
    if (attempt === 1) {
      console.log(`�� ScraperAPI Config: Smart 404 Detection Mode (initial request + retry_404=true on 404)`);
    }

    let response;

    // Strategy: First try without retry_404=true, then with it if we get a 404
    // This helps detect fake 404s and saves API credits
    if (attempt === 1) {
      // Initial request without retry_404=true
      const initialUrl = `https://api.scraperapi.com/?api_key=${SCRAPER_API_KEY}&url=${encodeURIComponent(url)}`;

      response = await axios.get(initialUrl, {
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          "Accept-Language": "en-US,en;q=0.9",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
          "Accept-Encoding": "gzip, deflate, br",
          "Cache-Control": "no-cache",
          "Pragma": "no-cache",
          "DNT": "1",
          "Connection": "keep-alive",
          "Upgrade-Insecure-Requests": "1",
        },
        timeout: 30000, // 30 second timeout
        validateStatus: () => true,
      });
    } else {
      // Retry with retry_404=true to detect fake 404s
      const retryUrl = `https://api.scraperapi.com/?api_key=${SCRAPER_API_KEY}&retry_404=true&url=${encodeURIComponent(url)}`;

      response = await axios.get(retryUrl, {
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          "Accept-Language": "en-US,en;q=0.9",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
          "Accept-Encoding": "gzip, deflate, br",
          "Cache-Control": "no-cache",
          "Pragma": "no-cache",
          "DNT": "1",
          "Connection": "keep-alive",
          "Upgrade-Insecure-Requests": "1",
        },
        timeout: 30000, // 30 second timeout
        validateStatus: () => true,
      });
    }

    const html = response.data;
    const status = response.status;

    // Handle 404 responses with intelligent retry logic
    if (status === 404) {
      if (attempt === 1) {
        // First 404 - could be fake, retry with retry_404=true
        console.log(`404 detected on ${url} - retrying with retry_404=true to detect fake 404...`);
        await sleep(1000 * attempt);
        return processReview(url, attempt + 1, isReverify, isBatchProcessing);
      } else {
        // Second 404 with retry_404=true - likely a real 404
        console.log(`404 confirmed after retry with retry_404=true: ${url} -> REMOVED`);

        // For batch processing, add extra verification
        if (isBatchProcessing && attempt < MAX_RETRIES) {
          console.log("Batch processing 404 - final verification attempt...");
          await sleep(3000 * attempt);
          return processReview(url, attempt + 1, isReverify, isBatchProcessing);
        }

        await saveRemovedHTML(url, html);
        return ReviewStatus.REMOVED;
      }
    }

    if (status !== 200) {
      console.warn(`Non-200 status (${status}) on ${url}`);
      if (attempt < MAX_RETRIES) {
        console.log("Retrying...");
        await sleep(1000 * attempt);
        return processReview(url, attempt + 1, isReverify, isBatchProcessing);
      }
      return ReviewStatus.FAILED;
    }

    if (/captcha|Robot Check|Enter the characters you see below/i.test(html)) {
      console.warn(`Captcha detected on ${url}`);
      if (attempt < MAX_RETRIES) {
        console.log("Retrying due to captcha...");
        await sleep(2000 * attempt);
        return processReview(url, attempt + 1, isReverify, isBatchProcessing);
      }
      return ReviewStatus.FAILED;
    }

    // Check for Amazon 404 page patterns (these should be less common now with our retry strategy)
    const isAmazon404Page = /Page Not Found|Sorry! We couldn't find that page|error\/logo\._TTD_\.png|api-services-support@amazon\.com/i.test(html);

    if (isAmazon404Page) {
      console.log(`Amazon 404 page detected: ${url} -> REMOVED`);
      if (!isReverify) {
        console.log("Re-verifying because Amazon 404 detected...");
        return await processReview(url, attempt, true, isBatchProcessing);
      }
      await saveRemovedHTML(url, html);
      return ReviewStatus.REMOVED;
    }

    // Check for review content indicators (simpler, more permissive approach)
    const hasReviewList = html.includes("cm_cr-review_list");
    const hasReviewContent = html.includes("review-content") || html.includes("review-text");
    const hasReviewTitle = html.includes("review-title") || html.includes("review-heading");
    const hasReviewerInfo = html.includes("reviewer") || html.includes("a-profile-name");
    const hasStarRating = html.includes("a-star-rating") || html.includes("star-rating");
    const hasReviewDate = html.includes("review-date") || html.includes("review-timestamp");

    // Check for negative indicators
    const noReviewsText = /No customer reviews|has no reviews|no reviews found/i.test(html);
    const emptyReviewSection = /cm_cr-review_list[^>]*>\s*<\/div>/.test(html);

    // A review is considered present if it has ANY review-specific content
    // This is more permissive than the previous strict logic
    const hasAnyReviewContent = hasReviewList || hasReviewContent || hasReviewTitle ||
      hasReviewerInfo || hasStarRating || hasReviewDate;

    const detectedRemoved = !hasAnyReviewContent || noReviewsText || emptyReviewSection;

    // Debug logging for review detection
    if (attempt === 1) {
      console.log(`Review detection debug for ${url}:`);
      console.log(`  - hasReviewList: ${hasReviewList}`);
      console.log(`  - hasReviewContent: ${hasReviewContent}`);
      console.log(`  - hasReviewTitle: ${hasReviewTitle}`);
      console.log(`  - hasReviewerInfo: ${hasReviewerInfo}`);
      console.log(`  - hasStarRating: ${hasStarRating}`);
      console.log(`  - hasReviewDate: ${hasReviewDate}`);
      console.log(`  - hasAnyReviewContent: ${hasAnyReviewContent}`);
      console.log(`  - noReviewsText: ${noReviewsText}`);
      console.log(`  - emptyReviewSection: ${emptyReviewSection}`);
      console.log(`  - detectedRemoved: ${detectedRemoved}`);
    }

    if (detectedRemoved && attempt < MAX_RETRIES && !isReverify) {
      console.log("Suspicious REMOVED detected — retrying to confirm...");
      await sleep(1500 * attempt);
      return processReview(url, attempt + 1, isReverify, isBatchProcessing);
    }

    if (detectedRemoved) {
      if (!isReverify) {
        console.log("Re-verifying because REMOVED detected...");
        return await processReview(url, attempt, true, isBatchProcessing);
      }
      console.log("Confirmed REMOVED after reverify.");
      await saveRemovedHTML(url, html);
      return ReviewStatus.REMOVED;
    }

    console.log(`ReviewLink: ${url}, Final Decision: ${ReviewStatus.PRESENT}`);
    return ReviewStatus.PRESENT;
  } catch (error) {
    console.error(`Error on attempt ${attempt} for ${url}:`, error.message);
    if (attempt < MAX_RETRIES) {
      console.log("Retrying due to error...");
      await sleep(1000 * attempt);
      return processReview(url, attempt + 1, isReverify, isBatchProcessing);
    }
    return ReviewStatus.FAILED;
  }
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function saveRemovedHTML(url, html) {
  const reviewId = extractReviewId(url);
  if (!reviewId) {
    console.warn(`Could not extract Review ID from URL: ${url}`);
    return;
  }

  const fileName = `${reviewId}.html`;
  const filePath = path.join(DEBUG_DIR, fileName);

  try {
    await fs.promises.writeFile(filePath, html);
    console.log(`Saved REMOVED review HTML to: ${filePath}`);
    await enforceMaxRemovedFiles();
  } catch (err) {
    console.error(`Failed to save HTML for ${url}:`, err.message);
  }
}

function extractReviewId(url) {
  // Look for Amazon review ID pattern: R followed by alphanumeric characters
  const match = url.match(/\/R([A-Z0-9]+)(?:[\/?#]|$)/);
  return match ? `R${match[1]}` : null;
}

async function enforceMaxRemovedFiles() {
  try {
    const files = await fs.promises.readdir(DEBUG_DIR);

    const fileStats = await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(DEBUG_DIR, file);
        const stats = await fs.promises.stat(filePath);
        return { file, time: stats.birthtimeMs || stats.ctimeMs };
      })
    );

    fileStats.sort((a, b) => a.time - b.time);

    if (fileStats.length > MAX_REMOVED_FILES) {
      const filesToDelete = fileStats.slice(
        0,
        fileStats.length - MAX_REMOVED_FILES
      );
      for (const { file } of filesToDelete) {
        const filePath = path.join(DEBUG_DIR, file);
        await fs.promises.unlink(filePath);
        console.log(`Deleted old REMOVED review HTML: ${filePath}`);
      }
    }
  } catch (err) {
    console.error("Failed to enforce max removed files limit:", err.message);
  }
}

async function example() {
  const res = await extractReviewId(
    "https://www.amazon.com/gp/customer-reviews/R3N9UTP90U4FIB"
  );
  console.log(res);
}
// example();


async function sendLexSlackNotification(
  reviews,
  reportType,
  customMessage = "Starting Script",
  totalCheckedToday,
  numOfFailedChecks,
  customSlackMessage = null
) {
  const webhookUrl = process.env.LEX_NOTI_SLACK_WEBHOOK_URL;
  const TAGGED_USERS = ["<@U088F8THDT6>", "<@U0765PL0W4V>", "<@U08JABT9WD9>"];

  if (!webhookUrl) {
    console.warn("Slack webhook URL not set");
    return;
  }

  let message = {};

  // If custom message is provided, use it directly
  if (customSlackMessage) {
    message = customSlackMessage;
  } else {
    switch (reportType) {
      case "DAILY":
        // Handle daily report with multiple reviews
        const reviewsText = reviews
          .map(
            (review) =>
              `*Review Details:*\n` +
              `• *Review ID:* ${review.reviewId}\n` +
              `• *Review Job ID:* ${review.reviewJobId}\n` +
              `• *ASIN:* \`${review.asin}\`\n` +
              `• *Brand:* ${review.brandName}\n` +
              `• *Status:* "REMOVED"\n` +
              `• *Review Link:* <${review.reviewUrl}|View Review>\n` +
              `• *Created At:* ${new Date(review.createdAt).toLocaleString()}\n` +
              `• *Updated At:* ${new Date(review.updatedAt).toLocaleString()}\n`
          )
          .join("\n");

        message = {
          text:
            `:clipboard: *Daily Lex Review Checker Report* ${TAGGED_USERS?.join(" ")}\n\n` +
            `*Total Reviews Checked Today:* ${totalCheckedToday}\n` +
            `*Removed Reviews Found:* ${reviews.length}\n\n` +
            `*Failed Checks:* ${numOfFailedChecks}\n\n` +
            `${reviews.length > 0 ? reviewsText : "No removed reviews found."}`,
        };
        break;

      case "PING":
        // Handle ping messages for script status
        message = {
          text:
            `:bell: *Lex Review Checker Status* ${TAGGED_USERS?.join(" ")}\n` +
            `*Total Reviews Found :* ${totalCheckedToday}\n` +
            `${customMessage}`,
        };
        break;

      case "RESURRECTED":
        // Handle resurrected review notifications
        message = {
          text: customSlackMessage?.text || "Resurrected review notification",
        };
        break;

      default:
        // Handle single review notification
        message = {
          text:
            `:rotating_light: *Review Removed!* ${TAGGED_USERS?.join(" ")}\n\n` +
            `*Review ID:* ${reviews.reviewId}\n` +
            `*Review Job ID:* ${reviews.reviewJobId}\n` +
            `*ASIN:* \`${reviews.asin}\`\n` +
            `*Brand:* ${reviews.brandName}\n` +
            `*Status:* "REMOVED"\n` +
            `*Review Link:* <${reviews.reviewUrl}|View Review>\n` +
            `*Created At:* ${new Date(reviews.createdAt).toLocaleString()}\n` +
            `*Updated At:* ${new Date(reviews.updatedAt).toLocaleString()}`,
        };
    }
  }
  // Send the message to Slack
  try {
    if (process.env.NODE_ENV !== "test") {
      await axios.post(webhookUrl, message);
    }
    console.log("SLACK MESSAGE", message);
    console.log(`Slack notification sent`);
  } catch (error) {
    console.error("Error sending Slack notification:", error);
  }
}

// Test function to manually check a review URL
async function testReviewUrl(url) {
  console.log(`\n=== Testing Review URL: ${url} ===`);
  try {
    const status = await processReview(url);
    console.log(`\nFinal Status: ${status}`);
    return status;
  } catch (error) {
    console.error(`Error testing URL: ${error.message}`);
    return ReviewStatus.FAILED;
  }
}

// Function to manually verify a review URL without proxy (for debugging)
async function verifyReviewUrlDirectly(url) {
  console.log(`\n=== Direct Verification of Review URL: ${url} ===`);
  try {
    const response = await axios.get(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
      },
      timeout: 30000,
      validateStatus: () => true,
    });

    const html = response.data;
    const status = response.status;

    console.log(`HTTP Status: ${status}`);
    console.log(`Content Length: ${html.length}`);
    console.log(`Contains 'Page Not Found': ${html.includes('Page Not Found')}`);
    console.log(`Contains 'cm_cr-review_list': ${html.includes('cm_cr-review_list')}`);
    console.log(`Contains 'review-content': ${html.includes('review-content')}`);
    console.log(`Contains 'review-title': ${html.includes('review-title')}`);

    // Save the HTML for inspection
    const reviewId = extractReviewId(url);
    if (reviewId) {
      const fileName = `direct_${reviewId}.html`;
      const filePath = path.join(DEBUG_DIR, fileName);
      await fs.promises.writeFile(filePath, html);
      console.log(`Saved direct response HTML to: ${filePath}`);
    }

    return {
      status: response.status,
      hasReviewContent: html.includes('cm_cr-review_list') || html.includes('review-content'),
      is404Page: html.includes('Page Not Found'),
      contentLength: html.length
    };
  } catch (error) {
    console.error(`Error in direct verification: ${error.message}`);
    return { error: error.message };
  }
}

module.exports = { processReview, sendLexSlackNotification, testReviewUrl, verifyReviewUrlDirectly }
