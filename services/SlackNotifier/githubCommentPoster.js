const fs = require("fs");
const jwt = require("jsonwebtoken");
const axios = require("axios");
require("dotenv").config();

// === CONFIG ===
const APP_ID = process.env.GITHUB_APP_ID;
const GITHUB_USERNAME = process.env.GITHUB_USERNAME;

// Try to read the private key, but don't fail if it doesn't exist
let PRIVATE_KEY = null;
let GITHUB_FEATURE_ENABLED = false;

try {
  if (fs.existsSync("private-key.pem")) {
    PRIVATE_KEY = fs.readFileSync("private-key.pem", "utf8");
    GITHUB_FEATURE_ENABLED = true;
    console.log("✅ GitHub integration enabled - private-key.pem found");
  } else {
    console.log("⚠️ GitHub integration disabled - private-key.pem not found");
  }
} catch (error) {
  console.log("⚠️ GitHub integration disabled - error reading private-key.pem:", error.message);
}

// === Generate JWT ===
function generateJWT() {
  if (!GITHUB_FEATURE_ENABLED) {
    throw new Error("GitHub integration is disabled - private key not available");
  }
  
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iat: now,
    exp: now + 600,
    iss: APP_ID,
  };
  
  if (!PRIVATE_KEY) {
    throw new Error("PRIVATE_KEY is not set");
  }
  
  return jwt.sign(payload, PRIVATE_KEY, { algorithm: "RS256" });
}

// === Get Installation ID ===
async function getInstallationId(jwt) {
  try {
    const url = "https://api.github.com/app/installations";
    const res = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${jwt}`,
        Accept: "application/vnd.github+json",
      },
    });

    const installation = res.data.find(
      (install) => install.account && install.account.login === GITHUB_USERNAME
    );

    if (!installation) {
      throw new Error(`Installation not found for user ${GITHUB_USERNAME}`);
    }

    return installation.id;
  } catch (err) {
    throw new Error(`Error fetching installation ID: ${err.message}`);
  }
}

// === Get Access Token ===
async function getAccessToken(jwt, installationId) {
  try {
    const url = `https://api.github.com/app/installations/${installationId}/access_tokens`;
    const res = await axios.post(
      url,
      {},
      {
        headers: {
          Authorization: `Bearer ${jwt}`,
          Accept: "application/vnd.github+json",
        },
      }
    );

    return res.data.token;
  } catch (err) {
    throw new Error(`Error fetching access token: ${err.message}`);
  }
}

// === Post Comment ===
async function postComment(token, repo, issueNumber, body) {
  try {
    const url = `https://api.github.com/repos/${repo}/issues/${issueNumber}/comments`;

    const res = await axios.post(
      url,
      { body },
      {
        headers: {
          Authorization: `token ${token}`,
          Accept: "application/vnd.github+json",
        },
      }
    );

    if (res.status === 201) {
      console.log(`Comment posted to ${repo}#${issueNumber}!`);
    } else {
      throw new Error(`Failed to post comment: ${res.statusText}`);
    }
  } catch (err) {
    throw new Error(`Error posting comment: ${err.message}`);
  }
}

// === Main export function ===
async function postSlackMessageAsGitHubComment(repo, issueNumber, commentBody) {
  // Check if GitHub integration is enabled
  if (!GITHUB_FEATURE_ENABLED) {
    console.log("⚠️ GitHub comment posting skipped - GitHub integration is disabled (private-key.pem not found)");
    return { success: false, message: "GitHub integration disabled" };
  }

  try {
    const jwtToken = generateJWT();
    const installationId = await getInstallationId(jwtToken);
    const accessToken = await getAccessToken(jwtToken, installationId);
    await postComment(accessToken, repo, issueNumber, commentBody);
    return { success: true, message: "Comment posted successfully" };
  } catch (err) {
    console.log("⚠️ GitHub comment posting failed:", err.message);
    if (err.response) {
      throw new Error(
        `GitHub API Error: ${err.response.status} - ${JSON.stringify(err.response.data)}`
      );
    } else {
      throw new Error(`Error posting comment: ${err.message}`);
    }
  }
}

module.exports = { 
  postSlackMessageAsGitHubComment,
  GITHUB_FEATURE_ENABLED
};
