const csvParser = require("fast-csv");
const path = require("path");
const fs = require("fs");
const {
  removeSpecialCharacters,
} = require("../../utils/removeSpecialCharacters");
const {
  storeLeadsSchemaMap,
  transformDataSync,
} = require("../../middlewares/schema");

const prisma = require("../../database/prisma/getPrismaClient");

/**
 * Fetch company data and create a name-to-amazon_seller_id map
 */
async function fetchCompanyData() {
  const startTime = process.hrtime();
  console.log(`Fetching Company Table.....`);

  try {
    const companies = await prisma.company.findMany({
      select: {
        amazon_seller_id: true,
        name: true,
      },
    });

    const nameToSellerIdMap = new Map();
    companies.forEach((company) => {
      const cleanedName = removeSpecialCharacters(company.name.toLowerCase());
      nameToSellerIdMap.set(cleanedName, company.amazon_seller_id);
    });

    const endTime = process.hrtime(startTime);
    const executionTime = (endTime[0] * 1000 + endTime[1] / 1000000).toFixed(2);
    console.log(
      `Companies details fetched from DB and added to map in ${executionTime}ms`,
    );

    return { nameToSellerIdMap, companies };
  } catch (error) {
    console.log("Error fetching company data:", error);
    throw new Error("Failed to fetch company data from database");
  }
}

/**
 * Match names and collect matched Amazon seller IDs
 */
function getMatchedAmazonIds(rows, nameToSellerIdMap) {
  const startTime = process.hrtime();
  const matchedAmazonIDs = [];

  try {
    rows.forEach((row) => {
      if (!row.merchantName) return;

      const cleanedMerchantName = removeSpecialCharacters(
        row.merchantName.toLowerCase(),
      );
      const amazonSellerId = nameToSellerIdMap.get(cleanedMerchantName);
      if (amazonSellerId) {
        matchedAmazonIDs.push(amazonSellerId);
      }
    });

    const endTime = process.hrtime(startTime);
    const executionTime = (endTime[0] * 1000 + endTime[1] / 1000000).toFixed(2);
    console.log(
      `Got MatchedID of storeleads csv and sellerDB in ${executionTime}ms`,
    );

    return matchedAmazonIDs;
  } catch (error) {
    console.log("Error matching Amazon IDs:", error);
    throw new Error("Failed to match Amazon seller IDs");
  }
}

/**
 * Fetch complete data for matched Amazon seller IDs
 */
async function fetchMatchedData(matchedAmazonIDs) {
  const startTime = process.hrtime();

  try {
    // Fetch companies
    const companies = await prisma.company.findMany({
      where: {
        amazon_seller_id: {
          in: matchedAmazonIDs,
        },
      },
    });

    // Fetch matching seller country data
    const sellerCountryMatches = await prisma.sellerCountryMatching.findMany({
      where: {
        amazon_seller_id: {
          in: matchedAmazonIDs,
        },
      },
      select: {
        amazon_seller_id: true,
        smartscout_country: true,
        seller_url: true,
      },
    });

    // Combine the data
    const matchedData = companies.map((company) => ({
      ...company,
      SellerCountryMatching: sellerCountryMatches.filter(
        (match) => match.amazon_seller_id === company.amazon_seller_id,
      ),
    }));

    const nameToDataMap = new Map();
    matchedData.forEach((data) => {
      const cleanedName = removeSpecialCharacters(data.name.toLowerCase());
      nameToDataMap.set(cleanedName, data);
    });

    const endTime = process.hrtime(startTime);
    const executionTime = (endTime[0] * 1000 + endTime[1] / 1000000).toFixed(2);
    console.log(`Fetched matched data from DB in ${executionTime}ms`);

    return { nameToDataMap, matchedData };
  } catch (error) {
    console.log("Error fetching matched data:", error);
    throw new Error("Failed to fetch matched data from database");
  }
}

/**
 * Process a row with a matched company
 */
function processMatchedRow(row, company) {
  const rows = [];

  if (
    company.SellerCountryMatching &&
    company.SellerCountryMatching.length > 0
  ) {
    // If we have seller country matches, create a row for each match
    company.SellerCountryMatching.forEach((seller) => {
      // Create new row with original row data
      const newRow = { ...row };

      // Add company data - only prefix id and createdAt
      Object.entries(company).forEach(([key, value]) => {
        if (key === "id") {
          newRow["company_id"] = value;
        } else if (key === "createdAt") {
          newRow["company_createdAt"] = value;
        } else if (key !== "SellerCountryMatching") {
          newRow[key] = value;
        }
      });

      // Add seller data with SellerCountryMatching_ prefix
      Object.entries(seller).forEach(([key, value]) => {
        newRow[`SellerCountryMatching_${key}`] = value;
      });

      rows.push(newRow);
    });
  } else {
    // If no seller country matches, add row with just company data
    const newRow = { ...row };

    // Add company data - only prefix id and createdAt
    Object.entries(company).forEach(([key, value]) => {
      if (key === "id") {
        newRow["company_id"] = value;
      } else if (key === "createdAt") {
        newRow["company_createdAt"] = value;
      } else if (key !== "SellerCountryMatching") {
        newRow[key] = value;
      }
    });

    rows.push(newRow);
  }

  return rows;
}

/**
 * Process a row with no matched company
 */
function processUnmatchedRow(row, sampleCompany, sampleSeller) {
  const newRow = { ...row };

  // Add empty company fields using the sample company as template
  if (sampleCompany) {
    Object.keys(sampleCompany)
      .filter((key) => key !== "SellerCountryMatching")
      .forEach((key) => {
        if (key === "id") {
          newRow["company_id"] = "";
        } else if (key === "createdAt") {
          newRow["company_createdAt"] = "";
        } else {
          newRow[key] = "";
        }
      });
  }

  // Add empty seller fields using the sample seller as template
  if (sampleSeller) {
    Object.keys(sampleSeller).forEach((key) => {
      newRow[`SellerCountryMatching_${key}`] = "";
    });
  }

  return newRow;
}

/**
 * Find a sample company and seller to use as template for empty fields
 */
function findSampleData(companyMap) {
  let sampleCompany = null;
  let sampleSeller = null;

  for (const value of companyMap.values()) {
    sampleCompany = value;
    if (value.SellerCountryMatching && value.SellerCountryMatching.length > 0) {
      sampleSeller = value.SellerCountryMatching[0];
      break;
    }
  }

  return { sampleCompany, sampleSeller };
}

/**
 * Process CSV data and append company and seller data
 */
async function appendCsv(inputFilePath, companyMap, res, originalFilename) {
  const rows = [];
  let matchedCount = 0;
  let totalCount = 0;
  const { sampleCompany, sampleSeller } = findSampleData(companyMap);

  try {
    fs.createReadStream(inputFilePath)
      .pipe(csvParser.parse({ headers: true }))
      .on("error", (error) => {
        console.log("Error parsing CSV:", error);
        res.status(500).send("Error processing the CSV file");
      })
      .on("data", (row) => {
        totalCount++;
        // Clean the merchant name for comparison
        const cleanedMerchantName = removeSpecialCharacters(
          row.merchantName?.toLowerCase() || "",
        );

        // Try to find the company in our map - direct lookup
        const company = companyMap.get(cleanedMerchantName);

        if (company) {
          matchedCount++;
          const processedRows = processMatchedRow(row, company);
          rows.push(...processedRows);
        } else {
          // No match found, add original row with empty fields
          const unmatchedRow = processUnmatchedRow(
            row,
            sampleCompany,
            sampleSeller,
          );
          rows.push(unmatchedRow);
        }
      })
      .on("end", () => {
        console.log(`Total records processed: ${totalCount}`);
        console.log(`Total matches found: ${matchedCount}`);

        const fileNameWithoutExt = path.basename(
          originalFilename,
          path.extname(originalFilename),
        );

        // Write directly to response stream instead of file
        res.setHeader("Content-Type", "text/csv");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="matched_results_${fileNameWithoutExt}_${Date.now()}.csv"`,
        );

        csvParser.write(rows, { headers: true }).pipe(res);

        // Clean up temporary file
        fs.unlink(inputFilePath, (err) => {
          if (err) console.log(`Failed to delete temporary file: ${err}`);
        });
      });
  } catch (error) {
    console.log("Error processing CSV data:", error);
    res.status(500).send("Error processing the CSV file");

    // Ensure cleanup even on error
    fs.unlink(inputFilePath, (err) => {
      if (err) console.log(`Failed to delete temporary file: ${err}`);
    });
  }
}

/**
 * Main function to process the uploaded CSV file
 */
async function processCSV(req, res) {
  const startTime = process.hrtime();

  try {
    // Verify that file exists
    if (!req.file || !req.file.path) {
      return res.status(400).send("No file uploaded");
    }

    const { nameToSellerIdMap } = await fetchCompanyData();

    const rows = [];

    // Process the CSV file
    const stream = fs.createReadStream(req.file.path);

    stream.on("error", (error) => {
      console.log("Error reading file:", error);
      res.status(500).send("Error reading the uploaded file");

      // Clean up temporary file on error
      fs.unlink(req.file.path, (err) => {
        if (err) console.log(`Failed to delete temporary file: ${err}`);
      });
    });

    csvParser
      .parseStream(stream, { headers: true })
      .on("error", (error) => {
        console.log("Error parsing CSV:", error);
        res.status(500).send("Error processing the CSV file");

        // Clean up temporary file on error
        fs.unlink(req.file.path, (err) => {
          if (err) console.log(`Failed to delete temporary file: ${err}`);
        });
      })
      .on("data", (row) => {
        const transformedRow =  transformDataSync(row, storeLeadsSchemaMap);
        delete transformedRow.errors;
        rows.push(transformedRow);
      })
      .on("end", async () => {
        try {
          const matchedAmazonIDs = getMatchedAmazonIds(rows, nameToSellerIdMap);
          const { nameToDataMap } = await fetchMatchedData(matchedAmazonIDs);

          await appendCsv(
            req.file.path,
            nameToDataMap,
            res,
            req.file.originalname,
          );

          const endTime = process.hrtime(startTime);
          const executionTime = (
            endTime[0] * 1000 +
            endTime[1] / 1000000
          ).toFixed(2);
          console.log(`Full process completed in ${executionTime}ms`);
        } catch (error) {
          console.log("Error in CSV processing:", error);
          res.status(500).send("Error processing the CSV file");

          // Clean up temporary file on error
          fs.unlink(req.file.path, (err) => {
            if (err) console.log(`Failed to delete temporary file: ${err}`);
          });
        }
      });
  } catch (error) {
    console.log("Unexpected error in processCSV:", error);
    res.status(500).send("Server error while processing CSV");

    // Clean up temporary file on error
    if (req.file && req.file.path) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.log(`Failed to delete temporary file: ${err}`);
      });
    }
  }
}

module.exports = processCSV;
