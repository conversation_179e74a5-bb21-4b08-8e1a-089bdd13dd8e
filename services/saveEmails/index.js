const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const fs = require("fs");
const csv = require("csvtojson");
const { getCampaignData } = require("./getCampaignData");
const { getLeadsData } = require("./getLeadsData");
const { fetchAndStoreEmails } = require("./getMessageData");
const { getLeadCategory } = require("./getLeadCategory");
const { saveClientsFromSmartLeads } = require("./saveClientsFromSmartLeads");
async function saveClientsFromCSV(csvPath) {
  try {
    console.log("Starting to save clients from CSV...");
    const jsonArray = await csv().fromFile(csvPath);

    for (const row of jsonArray) {
      await prisma.client.upsert({
        where: { clientId: parseInt(row.client_id) },
        update: {
          name: row.name,
          clientId: parseInt(row.client_id),
        },
        create: {
          name: row.name,
          clientId: parseInt(row.client_id),
        },
      });
      console.log(`Saved/Updated client: ${row.name}`);
    }
    console.log("✅ All clients saved successfully");
  } catch (error) {
    console.error("Error saving clients:", error);
    throw error;
  }
}

async function main() {
  try {
    console.log("🚀 Starting email data processing...");

    // Step 1: Save clients from CSV
    console.log("\n📋 Step 1: Saving clients from CSV or Smartleads...");
    // await saveClientsFromCSV('./sample/smartleads_clients.csv');
    await saveClientsFromSmartLeads();

    // Step 2: Get campaign data for all clients
    console.log("\n📊 Step 2: Fetching campaign data and lead category...");
    await getCampaignData();
    await getLeadCategory();

    // Step 3: Get leads data for campaigns
    console.log("\n👥 Step 3: Fetching leads data...");
    const campaignIds = await prisma.campaign.findMany({
      orderBy: {
        updatedAt:"desc"
      },
      select: {
        campaignId: true,
      },
    });

    console.log("TOTAL CAMPAIGN FOUND:", campaignIds.length);
    let threads = [];
    for (const campaignId of campaignIds) {
      console.log("++++++++++++++++++++++++++++++++++++");
      console.log("CAMPAIGN ID:", campaignId.campaignId);
      console.log("++++++++++++++++++++++++++++++++++++");
      await getLeadsData(campaignId.campaignId);
      // Step 4: Generate email threads
      console.log("\n📧 Step 4: Generating email threads...");
      await fetchAndStoreEmails(campaignId.campaignId);
    }

    console.log("\n✅ All operations completed successfully!");
    return threads;
  } catch (error) {
    console.error("❌ Error in main process:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the main function
main()
  .then((threads) => {
    console.log(`\n📊
    - Process completed successfully`);
  })
  .catch((error) => {
    console.error("Fatal error:", error);
    // Do NOT exit the process
    console.log("❌ Error occurred but process will continue running.");
  });

module.exports = { main };
