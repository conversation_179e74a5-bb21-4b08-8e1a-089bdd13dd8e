const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");

require("dotenv").config();
const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;

async function saveClientsFromSmartLeads() {
  try {
    const clientUrl = `https://server.smartlead.ai/api/v1/client/?api_key=${SMART_LEADS_API_KEY}`;
    const response = await axios.get(clientUrl);
    const clients = response.data;

    if (!Array.isArray(clients)) {
      console.error("Invalid clients data", clients);
      return;
    }

    console.log("Clients:", JSON.stringify(clients, null, 2));
    const dataToInsert = clients.map((client) => ({
      name: client.name,
      clientId: client.id,
      businessName: client.logo || client.name,
    }));

    for (const client of dataToInsert) {
      const existingClient = await prisma.client.findUnique({
        where: { clientId: client.clientId },
      });
      if (existingClient) {
        console.log("Client already exists:", client.name);
        await prisma.client.update({
          where: { clientId: client.clientId },
          data: client,
        });
        continue;
      } 
      console.log("Client does not exist:", client.name);
      await prisma.client.create({
        data: client
      });
    }

    console.log("All Clients Added Successfully!");
  } catch (error) {
    console.error(
      "Error fetching clients:",
      error.response?.data || error.message || error
    );
    throw error;
  }
}

// saveClientsFromSmartLeads();

module.exports = { saveClientsFromSmartLeads };
