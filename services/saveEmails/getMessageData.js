const { PrismaClient, SmartLeadStatus } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");
const { cleanReply } = require("../../utils/replyMailParser");

require("dotenv").config();
const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;

const RATE_LIMIT_TOTAL = 200; // total requests allowed per window
const RATE_LIMIT_WINDOW = 60; // seconds

async function adaptiveSleep(headers) {
  const remaining = parseInt(headers["x-ratelimit-remaining"] || "1");
  const resetEpoch = parseInt(headers["x-ratelimit-reset"] || "0"); // epoch seconds

  const nowEpoch = Math.floor(Date.now() / 1000);
  let timeUntilReset = resetEpoch - nowEpoch;

  if (timeUntilReset < 0) {
    // Reset window passed, no need to wait
    timeUntilReset = 0;
  }

  if (remaining <= 1) {
    // We reached the limit, wait until reset + buffer
    const sleepMs = timeUntilReset * 1000 + 100;
    console.log(
      `⏳ Rate limit reached. Sleeping for ${(sleepMs / 1000).toFixed(2)}s`
    );
    await new Promise((res) => setTimeout(res, sleepMs));
    return;
  }

  // Calculate how many requests we've used in this window
  const used = RATE_LIMIT_TOTAL - remaining;

  // Calculate elapsed time since window start
  const elapsed = RATE_LIMIT_WINDOW - timeUntilReset;

  // Calculate average interval per request to spread requests evenly
  const avgInterval = RATE_LIMIT_WINDOW / RATE_LIMIT_TOTAL; // in seconds

  // Calculate how fast we can send next request to not exceed average rate
  const expectedElapsed = used * avgInterval;

  let delay = (expectedElapsed - elapsed) * 1000; // ms

  // If delay is negative, send immediately
  if (delay < 0) delay = 0;

  // Cap max delay to 1 second to avoid slowing too much
  if (delay > 1000) delay = 1000;

  console.log(
    `⏳ Remaining: ${remaining}, Delay: ${(delay / 1000).toFixed(2)}s`
  );
  await new Promise((res) => setTimeout(res, delay));
}


async function fetchAndStoreEmails(campaignId) {
  try {
    const campaign = await prisma.campaign.findFirst({ where: { campaignId } });
    if (!campaign) return console.log("❌ Campaign not found!");

    const client = await prisma.client.findFirst({
      where: { campaigns: { some: { campaignId } } },
    });
    if (!client) return console.log("❌ Client not found!");

    const leads = await prisma.smartLead_Lead.findMany({
      where: {
        campaignId: campaign.id,
        OR: [{ status: "PENDING" }, { status: "FAILED" }],
      },
    });
    

    if (!leads.length) return console.log("No leads found for this campaign.");

    console.log(`📥 Fetched ${leads.length} leads.`);

    for (const lead of leads) {
      const leadId = lead.leadId;
      const messageHistoryUrl = `https://server.smartlead.ai/api/v1/campaigns/${campaign.campaignId}/leads/${leadId}/message-history?api_key=${SMART_LEADS_API_KEY}`;

      try {
        const res = await axios.get(messageHistoryUrl);
        const headers = res.headers;
        const messageHistory = res.data;

        const history = messageHistory.history || [];
        if (!history.length) {
          console.log(`No emails for Lead ID: ${leadId}`);
          await prisma.smartLead_Lead.update({
            where: {
              id: lead.id,
            },
            data: {
              status: SmartLeadStatus.SUCCESS,
            },
          });
          await adaptiveSleep(headers);
          continue;
        }

        for (const email of history) {
          const matchId = `${client.clientId}_${campaign.campaignId}_${leadId}`;
          await prisma.email.upsert({
            where: {
              messageId_time: {
                messageId: email.message_id,
                time: new Date(email.time),
              },
            },
            update: {
              subject: email.subject?.trim() || "Re: Previous Conversation",
              body: email.email_body?.trim()
                ? cleanReply(email.email_body)
                : "This email has no body content.",
              type: email.type || "Undefined",
              toEmailID: email.to || "Undefined",
              fromEmailID: email.from || "Undefined",
              campaingId: campaignId,
              email_seq_number: email.email_seq_number,
              open_count: email.open_count,
              click_count: email.click_count,
              click_details: email.click_details,
            },
            create: {
              matchId,
              leadId: lead.id,
              messageId: email.message_id,
              subject: email.subject?.trim() || "Re: Previous Conversation",
              body: email.email_body?.trim()
                ? cleanReply(email.email_body)
                : "This email has no body content.",
              type: email.type || "Undefined",
              toEmailID: email.to || "Undefined",
              fromEmailID: email.from || "Undefined",
              campaingId: campaignId,
              email_seq_number: email.email_seq_number,
              open_count: email.open_count,
              click_count: email.click_count,
              click_details: email.click_details,
              time: email.time ? new Date(email.time) : new Date(),
            },
          });

          await prisma.smartLead_Lead.update({
            where: {
              id: lead.id
            }, data: {
              status: SmartLeadStatus.SUCCESS
            }
          });

          console.log(`✅ Upserted email: ${email.message_id}`);
        }

        await adaptiveSleep(headers);
      }
      catch (error) {
        if (error.response && error.response.status === 404) {
          console.warn(`⚠️ Lead ID ${leadId} is MISSING (404). Marking as MISSING.`);
          await prisma.smartLead_Lead.update({
            where: { id: lead.id },
            data: { status: SmartLeadStatus.MISSING },
          });
        } else {
          console.error(
            `⚠️ Error fetching history for Lead ID ${leadId}: ${error.message}`
          );
      
          // Mark lead as FAILED in DB
          await prisma.smartLead_Lead.update({
            where: { id: lead.id },
            data: { status: SmartLeadStatus.FAILED },
          });
        }
      
        // Handle rate-limit respecting sleep even on error
        if (error.response && error.response.headers) {
          await adaptiveSleep(error.response.headers);
        } else {
          const fallbackDelayMs = 1000; // 1 second
          console.log(
            `⏳ No rate-limit headers in error. Sleeping for ${fallbackDelayMs / 1000}s`
          );
          await new Promise((res) => setTimeout(res, fallbackDelayMs));
        }
      }      
    }

    console.log("✅ All emails processed!");
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  } finally {
    await prisma.$disconnect();
  }
}

// fetchAndStoreEmails(1841892);

module.exports = { fetchAndStoreEmails };
