const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");

require("dotenv").config();
const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;
const API_DELAY = 1000;
async function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function getLeadCategory() {
    try {
        const leadCategoryApiRes = await axios.get(
          `https://server.smartlead.ai/api/v1/leads/fetch-categories?api_key=${SMART_LEADS_API_KEY}`
        );

        const leadCategories = leadCategoryApiRes.data;
        console.log("Fetched categories:", leadCategories);

        // Create many records instead of a single one
        await prisma.smartLead_Lead_Category.createMany({
            data: leadCategories.map(category => ({
                id: category.id,
                name: category.name,
                created_at: new Date(category.created_at)
            })),
            skipDuplicates: true // Skip if record with same id already exists
        });

        console.log("✅ Lead categories saved successfully");
        await sleep(API_DELAY);
    } catch (error) {
        console.error("Error in getLeadCategory:", error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

// getLeadCategory();

module.exports = { getLeadCategory };