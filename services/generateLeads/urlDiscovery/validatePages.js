const { getOrFetchHtml } = require("../../../utils/fileCache");
const { validateAddress } = require("../validaterMethods/validateAddress");
const { checkHtml } = require("../matchMethods/checkHtml");

// Environment variable flags
const DISABLE_ADDRESS_VALIDATION =
  process.env.DISABLE_ADDRESS_VALIDATION === "true";
const DISABLE_NUMBER_VALIDATION =
  process.env.DISABLE_NUMBER_VALIDATION === "true";
const DISABLE_EMAIL_VALIDATION =
  process.env.DISABLE_EMAIL_VALIDATION === "true";

/**
 * Validate discovered pages for address and phone numbers
 * @param {Object} urlObj - URL object with discoveredPages property
 * @param {Object} lead - Lead object containing address and numbers
 * @returns {Object} - Enhanced URL object with validation results in discoveredPages
 */
async function validateDiscoveredPages(urlObj, lead) {
  // Skip if all validations are disabled
  if (
    DISABLE_ADDRESS_VALIDATION &&
    DISABLE_NUMBER_VALIDATION &&
    DISABLE_EMAIL_VALIDATION
  ) {
    console.log("All discovered page validations are disabled");
    return urlObj;
  }

  // Skip if no address or numbers to validate
  if (
    !lead.address &&
    (!lead.numbers || lead.numbers.length === 0) &&
    (!lead.scraped_emails || lead.scraped_emails.length === 0)
  ) {
    console.log(
      "No address, phone numbers, or emails to validate for discovered pages"
    );
    return urlObj;
  }

  // Create a copy of the URL object to avoid mutations
  const enhancedUrlObj = JSON.parse(JSON.stringify(urlObj));

  // Initialize validation results inside discoveredPages
  enhancedUrlObj.discoveredPages.validationResults = {
    address: { valid: false, matchedPages: [] },
    phone: { valid: false, matchedPages: [] },
    email: { valid: false, matchedPages: [] },
  };

  try {
    // Extract all pages to validate including the original URL
    const pagesToValidate = [];

    // Add discovered pages
    if (enhancedUrlObj.discoveredPages) {
      // Handle the main discoveredPages structure
      if (enhancedUrlObj.discoveredPages.discoveredPages) {
        // Process nested discovered pages (home, about, privacy, contact, etc.)
        Object.entries(enhancedUrlObj.discoveredPages.discoveredPages).forEach(
          ([pageType, pageUrls]) => {
            if (Array.isArray(pageUrls)) {
              pageUrls.forEach((url) => {
                pagesToValidate.push({ url, type: pageType });
              });
            } else if (typeof pageUrls === "string") {
              pagesToValidate.push({ url: pageUrls, type: pageType });
            }
          }
        );
      }

      // Also process any direct page URLs in the discoveredPages object
      Object.entries(enhancedUrlObj.discoveredPages).forEach(
        ([pageType, pageUrls]) => {
          if (
            pageType !== "validationResults" &&
            pageType !== "discoveredPages"
          ) {
            if (Array.isArray(pageUrls)) {
              pageUrls.forEach((url) => {
                pagesToValidate.push({ url, type: pageType });
              });
            } else if (typeof pageUrls === "string") {
              pagesToValidate.push({ url: pageUrls, type: pageType });
            }
          }
        }
      );
    }

    console.log("Pages to validate:", JSON.stringify(pagesToValidate, null, 2));
    // Run validations on each page
    for (const page of pagesToValidate) {
      // console.log(
      //   `Validating discovered page: ${page.url} Page Type: ${page.type}`,
      // );

      try {
        // Run address validation if enabled and address exists
        if (!DISABLE_ADDRESS_VALIDATION && lead.address) {
          const addressResult = await validateAddress(
            { originalUrl: page.url },
            lead.address
          );
          // console.log(
          //   `Address result: ${JSON.stringify(addressResult, null, 2)}`,
          // );

          // If address validation passed with good confidence
          if (addressResult && addressResult.bestConfidence > 0) {
            enhancedUrlObj.discoveredPages.validationResults.address.valid = true;

            // Add to matched pages with score and matched segments
            const pageEntry = {
              type: page.type,
              confidence: addressResult.bestConfidence,
              matchedSegments: addressResult.matchedSegments,
            };
            enhancedUrlObj.discoveredPages.validationResults.address.matchedPages.push(
              pageEntry
            );

            // console.log(
            //   `Address matched on ${page.type} page with confidence: ${addressResult.bestConfidence} and segments: ${addressResult.matchedSegments}`,
            // );
          }
        }

        // Run number validation if enabled and numbers exist
        if (
          !DISABLE_NUMBER_VALIDATION &&
          lead.numbers &&
          lead.numbers.length > 0
        ) {
          // Normalize phone numbers by removing spaces and special characters
          const normalizedNumbers = lead.numbers.map((num) =>
            num.replace(/[\s\-\(\)]/g, "")
          );

          const numberResult = await checkHtml(
            { originalUrl: page.url },
            normalizedNumbers,
            true // Remove spaces for better matching
          );
          // console.log(
          //   `Number result: ${JSON.stringify(numberResult, null, 2)}`,
          // );

          // If number validation passed with matched keywords
          if (numberResult && numberResult.matchedKeywords.length > 0) {
            enhancedUrlObj.discoveredPages.validationResults.phone.valid = true;

            // Store matched page with confidence based on number of matches
            const pageEntry = {
              type: page.type,
              confidence:
                numberResult.matchedKeywords.length / lead.numbers.length,
              matchedNumbers: numberResult.matchedKeywords,
            };
            enhancedUrlObj.discoveredPages.validationResults.phone.matchedPages.push(
              pageEntry
            );

            // console.log(
            //   `Phone number matched on ${page.type} page with ${numberResult.matchedKeywords.length} matches`,
            // );
          }
        }

        // Run email validation if enabled and emails exist
        if (
          !DISABLE_EMAIL_VALIDATION &&
          lead.scraped_emails &&
          lead.scraped_emails.length > 0
        ) {
          const emailResult = await checkHtml(
            { originalUrl: page.url },
            lead.scraped_emails,
            true,
            "lowered_spaced"
          );

          // If email validation passed with matched keywords
          if (emailResult && emailResult.matchedKeywords.length > 0) {
            enhancedUrlObj.discoveredPages.validationResults.email.valid = true;

            // Store matched page with confidence based on number of matches
            const pageEntry = {
              type: page.type,
              confidence:
                emailResult.matchedKeywords.length / lead.scraped_emails.length,
              matchedEmails: emailResult.matchedKeywords,
            };
            enhancedUrlObj.discoveredPages.validationResults.email.matchedPages.push(
              pageEntry
            );

            console.log(
              `Email matched on ${page.type} page with ${emailResult.matchedKeywords.length} matches`
            );
          }
        }
      } catch (error) {
        console.error(`Error validating page ${page.url}: ${error.message}`);
        // Continue with next page even if this one fails
      }
    }

    return enhancedUrlObj;
  } catch (error) {
    console.error(`Error in validateDiscoveredPages: ${error.message}`);
    return urlObj; // Return original on error
  }
}
async function main() {
  // Sample URL and lead objects based on the provided data
  const urlObj = {
    id: 116,
    leadId: 6,
    url: "https://sagabeverage.no/",
    data: null,
    keywords: [
      "Saga Beverages Inc.",
      "Saga Beverages Inc",
      "3310 W. Braker Ln, Bldg. II Ste 300-535, AUSTIN, TX, 78758, US",
    ],
    domain: "https://sagabeverage.no",
    useDomain: true,
    googlePosition: 5,
    filterPosition: 2,
    confidence: 11,
    discoveredPages: {
      home: ["https://sagabeverage.no"],
      about: [
        "https://sagabeverage.no/about",
        "https://sagabeverage.no/about-vinmonopolet",
      ],
      contact: ["https://sagabeverage.no/contact"],
      privacy: [],
      domainUrl: "https://sagabeverage.no",
      originalUrl: "https://sagabeverage.no/",
    },
    mode: "serp",
  };

  const lead = {
    id: 6,
    jobId: 2,
    sellerName: "Saga Beverages Inc",
    useDomain: true,
    mode: "serp",
    businessName: "Saga Beverages Inc.",
    address: "3310 W. Braker Ln, Bldg. II Ste 300-535, AUSTIN, TX, 78758, US",
    numbers: ["47 99294855"],
    scraped_emails: ["<EMAIL>", "<EMAIL>"],
    sellerUrl: "https://www.amazon.com/sp?seller=A3MOSGNTYER11D",
    country: "US",
  };

  console.log("Starting page validation for:", urlObj.url);

  // Call validateDiscoveredPages with the URL and lead objects
  const result = await validateDiscoveredPages(urlObj, lead);

  console.log("Page Validation Results:", JSON.stringify(result, null, 2));
}

// Run main function if this module is executed directly
if (require.main === module) {
  main();
}

module.exports = { validateDiscoveredPages };
