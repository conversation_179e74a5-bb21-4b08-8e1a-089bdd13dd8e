const Bottleneck = require("bottleneck");
const stringSimilarity = require("string-similarity");
const { extractDomain } = require("../../../utils/domainHelper");
const {
  removeSpecialCharacters,
} = require("../../../utils/removeSpecialCharacters");

async function checkFuzzyDomain(organicDatum, keywords) {
  // console.log("   Checking for Fuzzy Domain: ", keywords);
  const url = organicDatum.originalUrl;
  const filteredKeywords = new Set(
    keywords
      .slice(0, 2)
      .flatMap((keyword) => {
        return keyword.split(" ");
      })
      .concat(keywords.slice(0, 2)),
  );

  // Add matched keywords split by spaces to matchedKeywords array for each keyword match with the domain
  try {
    const domain = new URL(url).hostname;
    const cleanDomain = extractDomain(domain);
    const matchedKeywords = [];

    for (let keyword of filteredKeywords) {
      // Calculate similarity score
      const similarity = stringSimilarity.compareTwoStrings(
        cleanDomain,
        removeSpecial<PERSON>haracters(keyword),
      );
      // console.log("similarity", similarity, keyword)
      // Define threshold for matching
      const SIMILARITY_THRESHOLD = 0.6;
      const isMatch = similarity >= SIMILARITY_THRESHOLD;
      if (isMatch) {
        matchedKeywords.push(keyword);
      }
    }

    return {
      finalScore: matchedKeywords.length * checkFuzzyDomain.config.score,
      matchDetails: {
        cleanDomain,
        matchedKeywords,
        keywords,
      },
      url,
    };
  } catch (error) {
    console.error(`Error in checkFuzzyDomain: ${error.message}`);
    return {
      finalScore: 0,
      url,
      keywords,
      error: error.message,
    };
  }
}

checkFuzzyDomain.config = {
  score: 3, // High score as domain matching is a strong signal
  key: "checkFuzzyDomain",
};

// function main() {
//   checkFuzzyDomain({ originalUrl: "https://www.suncupjuice.com/" }, ["sun cup juice", "gregory packaging inc"]).then(console.log)
// }
// main()
module.exports = { checkFuzzyDomain };
