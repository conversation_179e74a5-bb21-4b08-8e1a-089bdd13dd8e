const newrelic = require("newrelic");
const prisma = require("../../../database/prisma/getPrismaClient");
const { processUrlFiltering } = require("./urlFilterWorker");
const { processUrlScoring } = require("./scoringWorker");
const { processUrlValidation } = require("./validationWorker");
const { resetStaleLeads } = require("../resetLeadStatus");
require("./errorHandler");

// Generate a unique process ID using timestamp and random number
const WORKER_PROCESS_ID = `worker_${Date.now()}_${Math.random()
  .toString(36)
  .substr(2, 9)}`;

async function processNextLeads(batchSize = 3) {
  return newrelic.startBackgroundTransaction('processNextLeads', 'LeadWorker', async function() {
    const transaction = newrelic.getTransaction();
    
    try {
      resetStaleLeads();
      // First check if any leads are still processing with this worker's process ID
      const processingLeads = await prisma.lead.count({
        where: {
          status: { in: ["processing", "scoring", "validating"] },
          processId: WORKER_PROCESS_ID,
        },
      });

      if (processingLeads > 0) {
        transaction.end();
        return null; // Skip if we still have processing leads
      }

      // Get and lock multiple leads within a transaction
      const leads = await prisma.$transaction(async (tx) => {
        const leads = await tx.lead.findMany({
          where: {
            status: "srp_scraped",
            processId: null, // Only get leads that aren't being processed
          },
          take: batchSize,
          orderBy: {
            createdAt: "desc",
          },
          include: {
            job: true,
          },
        });

        console.log(`Processing ${leads.length} leads`);
        if (leads.length > 0) {
          // Mark batch with our process ID
          await tx.lead.updateMany({
            where: {
              id: {
                in: leads.map((l) => l.id),
              },
            },
            data: {
              processId: WORKER_PROCESS_ID,
              status: "processing",
            },
          });
        }
        return leads;
      });

      if (leads.length === 0) {
        transaction.end();
        return null;
      }

      await Promise.all(
        leads.map(async (lead) => {
          await processUrlFiltering(lead);
          await processUrlScoring(lead);
          await processUrlValidation(lead);
          await prisma.lead.update({
            where: { id: lead.id },
            data: {
              status: "completed",
              processId: null,
            },
          });
        }),
      );
      
      transaction.end();
      return leads;
    } catch (error) {
      transaction.end();
      throw error;
    }
  });
}
async function startWorker() {
  console.log(`Starting worker with process ID: ${WORKER_PROCESS_ID}`);

  await processNextLeads(4);
  // Run worker every 1 minute
  setInterval(async () => {
    try {
      const results = await processNextLeads(4);
      if (results) {
        console.log(`Processed batch of leads successfully`);
      }
    } catch (error) {
      console.error("Lead processing error:", error);
    }
  }, 10000); // 60000ms = 1 minute
}
startWorker();
module.exports = {
  startWorker,
  processNextLeads,
};
