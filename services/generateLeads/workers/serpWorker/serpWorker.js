const prisma = require("../../../../database/prisma/getPrismaClient");
const { processAsyncBatches } = require("../../SERP/getSerpRequest");
const { generateSearchString } = require("../../utils/searchPatternGenerator");

const WORKER_PROCESS_ID = `serp_worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

async function processSerpRequests() {
  // First find active async SERP jobs
  const activeAsyncJobs = await prisma.leadJob.findMany({
    where: {
      asyncSerp: true,
      status: "pending",
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  if (activeAsyncJobs.length === 0) {
    return null;
  }

  const activeJobIds = activeAsyncJobs.map((job) => job.id);

  // Get batch of pending leads for active async jobs
  const pendingLeads = await prisma.$transaction(async (tx) => {
    const leads = await tx.lead.findMany({
      where: {
        status: "pending",
        processId: null,
        jobId: {
          in: activeJobIds,
        },
      },
      take: 90,
      orderBy: {
        createdAt: "asc",
      },
      include: {
        job: true,
      },
    });

    if (leads.length > 0) {
      // Generate search strings for each lead based on job pattern
      const leadsWithSearchStrings = leads.map((lead) => ({
        ...lead,
        searchString: generateSearchString(
          lead.sellerName,
          lead.businessName,
          lead.job.searchPattern,
          lead
        ),
      }));

      // Update leads with search strings and processing status
      await Promise.all(
        leadsWithSearchStrings.map((lead) =>
          tx.lead.update({
            where: { id: lead.id },
            data: {
              processId: WORKER_PROCESS_ID,
              status: "processing",
              searchString: lead.searchString,
            },
          })
        )
      );

      return leadsWithSearchStrings;
    }
    return leads;
  });

  if (pendingLeads.length > 0) {
    try {
      await processAsyncBatches(pendingLeads);
      return { success: true, batchSize: pendingLeads.length };
    } catch (error) {
      await prisma.lead.updateMany({
        where: {
          id: {
            in: pendingLeads.map((l) => l.id),
          },
        },
        data: {
          status: "pending",
          processId: null,
          // error: error.message
        },
      });
      return { success: false, error: error.message };
    }
  }

  return null;
}

module.exports = {
  processSerpRequests,
};
