const prisma = require("../../../database/prisma/getPrismaClient");
const { checkHtml } = require("../matchMethods/checkHtml");
const checkSerpOrganicSnippet = require("../matchMethods/checkSnippets");
const { checkFuzzyDomain } = require("../matchMethods/checkFuzzyDomain");

const WORKER_PROCESS_ID = `serp_worker_${Date.now()}_${Math.random()
  .toString(36)
  .substr(2, 9)}`;

// Helper functions defined at the module level
async function runHtmlCheck(organicData, keywords) {
  console.log("Running HTML Check");
  const result = await checkHtml(organicData, keywords);
  return { htmlCheckResult: result };
}

async function runSnippetCheck(organicData, keywords) {
  console.log("Running Snippet Check");
  const result = await checkSerpOrganicSnippet(organicData, keywords);
  return { snippetCheckResult: result };
}

async function runFuzzyDomainCheck(organicData, keywords) {
  console.log("Running Fuzzy Domain Check");
  const result = await checkFuzzyDomain(organicData, keywords);
  return { fuzzyDomainResult: result };
}

async function processUrlScoring(lead) {

  const urls = await prisma.leadUrl.findMany({
    where: { leadId: lead.id },
  });

  for (const url of urls) {
    const keywords = url.keywords;
    console.log("--------------------------------------------------");
    url.organicData = url.organicData || {};
    url.organicData.originalUrl = url.useDomain ? url.domain : url.url;
    console.log(`Processing URL Check: ${url.organicData.originalUrl}`);

    const updateData = {};
    const scoringTasks = [];

    // Only add a scoring task if the corresponding check result is empty.
    if (!url.htmlCheckResult || Object.keys(url.htmlCheckResult).length === 0) {
      scoringTasks.push(runHtmlCheck(url.organicData, keywords));
    }
    if (
      !url.snippetCheckResult ||
      Object.keys(url.snippetCheckResult).length === 0
    ) {
      scoringTasks.push(runSnippetCheck(url.organicData, keywords));
    }
    if (
      !url.fuzzyDomainResult ||
      Object.keys(url.fuzzyDomainResult).length === 0
    ) {
      scoringTasks.push(runFuzzyDomainCheck(url.organicData, keywords));
    }

    if (scoringTasks.length > 0) {
      const results = await Promise.all(scoringTasks);
      results.forEach((res) => Object.assign(updateData, res));

      // Calculate confidence only from available results (both new and previous)
      const scores = [
        updateData.htmlCheckResult,
        updateData.snippetCheckResult,
        updateData.fuzzyDomainResult,
        url.htmlCheckResult,
        url.snippetCheckResult,
        url.fuzzyDomainResult,
      ].filter(Boolean);

      const confidence = scores.reduce(
        (acc, score) => acc + (score?.finalScore || 0),
        0,
      );
      updateData.confidence = confidence;

      console.log(
        `Final Confidence for ${url.organicData.originalUrl}: ${confidence}`,
      );
      await prisma.leadUrl.update({
        where: { id: url.id },
        data: updateData,
      });
      console.log("Updated scoring results:", updateData);
    }
    console.log("------------------------------------------------");
  }

  await updateLeadStatus(lead.id);
}

async function updateLeadStatus(leadId) {
  await prisma.lead.update({
    where: { id: leadId },
    data: {
      status: "validating",
    },
  });
}

module.exports = { processUrlScoring };
