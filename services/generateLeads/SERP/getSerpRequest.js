const axios = require("axios");
const prisma = require("../../../database/prisma/getPrismaClient");
const { createSerpHash } = require("./generateSerpHash");
const Bottleneck = require("bottleneck");

const SERP_API_KEYS = {
  RISHABH: "dEjfWTBVYjMFOhhucQfddrbGyItXTwjmeqM55FYjOChPrg2dhSnZZiQFGuT2",
  ARYAN: "P7om6XO3Tq54CQyRyldwxKPzD4aUishYmlLBYzSeTrjzAXvoLIqYhdu37unj",
};

const ACTIVE_SERP_KEY = SERP_API_KEYS.RISHABH;
const BATCH = 90;
const countryMap = {
  us: { lang: "en", loc: "Abernathy,Texas,United States" },
};

async function getRelevantResultsSnippet(searchString, country = "us") {
  const { lang, loc } = countryMap[country] || countryMap.us;

  const dataOptions = {
    domain: "google.com",
    lang: lang,
    q: searchString,
    loc: loc,
    device: "desktop",
    serp_type: "web",
  };

  const options = {
    method: "POST",
    url: "https://api.serphouse.com/serp/live",
    headers: {
      accept: "application/json",
      "content-type": "application/json",
      authorization: `Bearer ${ACTIVE_SERP_KEY}`,
    },
    data: {
      data: dataOptions,
    },
  };

  const cacheKey = createSerpHash(JSON.stringify(dataOptions));

  try {
    const cachedResult = await prisma.serp_Cache.findUnique({
      where: { hash: cacheKey },
    });

    if (cachedResult) {
      console.log("SERP result found in cache.");
      return cachedResult.apiResponse; // Direct Json access, no parsing needed
    }

    const response = await axios(options);
    const apiResponse = response.data;

    await prisma.serp_Cache.create({
      data: {
        hash: cacheKey,
        apiResponse: apiResponse, // Direct Json storage
      },
    });

    return apiResponse;
  } catch (error) {
    console.error("Error making SERP request:", error.message);
    // throw error;
    return null;
  }
}

const limiterSerpScheduler = new Bottleneck({
  maxConcurrent: 2,
  minTime: 12000,
});

const resultLimiter = new Bottleneck({
  maxConcurrent: 6,
  minTime: 1200,
});

async function scheduleAsyncSearchBatch(leads, batchSize = 90) {
  const searchData = leads.map((lead) => ({
    domain: "google.com",
    lang: "en",
    q: lead.searchString,
    loc: "Abernathy,Texas,United States",
    device: "desktop",
    serp_type: "web",
  }));

  const options = {
    method: "POST",
    url: "https://api.serphouse.com/serp/schedule",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${ACTIVE_SERP_KEY}`,
      Accept: "application/json",
    },
    data: { data: searchData },
  };

  const response = await axios(options);
  const results = response.data.results;

  // Transaction with row locking

  async function processAsyncResult(lead) {
    try {
      const result = await resultLimiter.schedule(() =>
        getAsyncSearchResult(lead.srp_task_id),
      );
      const { loc, lang } = countryMap[lead.country || "us"];

      const cacheKey = createSerpHash(
        JSON.stringify({
          searchString: lead.searchString,
          loc: loc,
          lang: lang,
        }),
      );

      await Promise.all([
        prisma.serp_Cache.create({
          data: {
            hash: cacheKey,
            apiResponse: result, // Direct Json storage
          },
        }),
        prisma.lead.update({
          where: { id: lead.id },
          data: {
            apiResponse: result, // Direct Json storage
            status: "srp_scraped",
          },
        }),
      ]);

      return result;
    } catch (error) {
      await prisma.lead.update({
        where: { id: lead.id },
        data: { status: "srp_failed" },
      });
      // throw error;
      return null;
    }
  }
  await prisma.$transaction(
    leads
      .map((lead, index) => [
        // First query locks the row
        prisma.lead.findUnique({
          where: { id: lead.id },
          select: { id: true },
        }),
        // Second query updates the locked row
        prisma.lead.update({
          where: { id: lead.id },
          data: {
            srp_task_id: results[index].id,
            status: "srp_requested",
          },
        }),
      ])
      .flat(),
  );

  return results;
}
async function getAsyncSearchResult(taskId) {
  const options = {
    method: "GET",
    url: `https://api.serphouse.com/serp/get?id=${taskId}`,
    headers: {
      Authorization: `Bearer ${ACTIVE_SERP_KEY}`,
      Accept: "application/json",
    },
  };

  const response = await axios(options);
  return response.data;
}

async function findProcessableLeads() {
  return await prisma.$transaction(async (tx) => {
    const leads = await tx.lead.findMany({
      where: {
        status: "srp_requested",
        srp_task_id: { not: null },
        updatedAt: {
          lte: new Date(Date.now() - 60000),
        },
      },
    });

    // Lock these leads for processing
    if (leads.length > 0) {
      await tx.lead.updateMany({
        where: {
          id: {
            in: leads.map((l) => l.id),
          },
        },
        data: {
          status: "processing",
        },
      });
    }

    return leads;
  });
}

async function processAsyncBatches(leads, batchSize = BATCH) {
  console.log("Starting batch processing...");
  const totalBatches = Math.ceil(leads.length / batchSize);
  const results = [];

  for (let i = 0; i < totalBatches; i++) {
    const batch = leads.slice(i * batchSize, (i + 1) * batchSize);

    const batchSearchStrings = batch.map((lead) => {
      const { loc, lang } = countryMap[lead.country || "us"];
      return createSerpHash(
        JSON.stringify({
          searchString: lead.searchString,
          loc: loc,
          lang: lang,
        }),
      );
    });

    await prisma.$transaction(async (tx) => {
      const cachedResults = await tx.serp_Cache.findMany({
        where: {
          hash: {
            in: batchSearchStrings,
          },
        },
      });

      const cachedHashes = new Set(cachedResults.map((r) => r.hash));
      const uncachedLeads = batch.filter(
        (lead, index) => !cachedHashes.has(batchSearchStrings[index]),
      );

      if (uncachedLeads.length > 0) {
        const batchResults = await limiterSerpScheduler.schedule(() =>
          scheduleAsyncSearchBatch(uncachedLeads),
        );
        results.push(...batchResults);
      }

      const cachedLeads = batch.filter((lead, index) =>
        cachedHashes.has(batchSearchStrings[index]),
      );

      if (cachedLeads.length > 0) {
        await tx.lead.updateMany({
          where: {
            id: {
              in: cachedLeads.map((lead) => lead.id),
            },
          },
          data: {
            status: "srp_scraped",
            apiResponse: JSON.parse(
              cachedResults.find(
                (r) => r.hash === batchSearchStrings[cachedLeads.indexOf(lead)],
              ).apiResponse,
            ),
          },
        });
      }
    });
  }

  return results;
}
async function processAsyncResult(lead) {
  try {
    const result = await resultLimiter.schedule(() =>
      getAsyncSearchResult(lead.srp_task_id),
    );
    const { loc, lang } = countryMap[lead.country || "us"];

    const cacheKey = createSerpHash(
      JSON.stringify({
        searchString: lead.searchString,
        loc: loc,
        lang: lang,
      }),
    );

    await Promise.all([
      prisma.serp_Cache.create({
        data: {
          hash: cacheKey,
          apiResponse: JSON.stringify(result),
        },
      }),
      prisma.lead.update({
        where: { id: lead.id },
        data: {
          apiResponse: result,
          status: "srp_scraped",
        },
      }),
    ]);

    return result;
  } catch (error) {
    await prisma.lead.update({
      where: { id: lead.id },
      data: { status: "srp_failed" },
    });
    // throw error;
    return null;
  }
}

module.exports = {
  getRelevantResultsSnippet,
  scheduleAsyncSearchBatch,
  findProcessableLeads,
  processAsyncResult,
  processAsyncBatches,
};
