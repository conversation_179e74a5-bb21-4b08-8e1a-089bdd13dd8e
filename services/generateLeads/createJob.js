const prisma = require("../../database/prisma/getPrismaClient");
const csv = require("csv-parser");
const fs = require("fs");
const {
  leadSchemaMap,
  transformDataSync,
} = require("../../middlewares/schema");
const { extractDomain } = require("../../utils/domainHelper");

const BATCH_SIZE = 100;

async function createInputDomainLeads(leads, job, dedupMap, mode) {
  const leadsArray = Array.from(dedupMap.values());
  await prisma.lead.createMany({
    data: leadsArray.map((lead) => {
      lead.status = "processing";
      const { urls, ...leadData } = lead;
      return leadData;
    }),
  });

  const createdLeadsData = await prisma.lead.findMany({
    where: { jobId: job.id },
  });

  const urlsToCreate = createdLeadsData.flatMap((lead) => {
    const originalLead = leadsArray.find(
      (l) =>
        l.sellerName === lead.sellerName && l.businessName === lead.businessName
    );
    let keywords = [];
    if (lead?.businessName?.length > 0) {
      keywords.push(lead.businessName);
    }
    if (lead?.sellerName?.length > 0) {
      keywords.push(lead.sellerName);
    }
    if (lead?.address?.length > 0) {
      keywords.push(lead.address);
    }

    // url = http://www.brain-bean.com
    // domainString = brain-bean.com
    // domain = http://www.brain-bean.com
    return originalLead.urls.map((url, index) => {
      let processedUrl = url;

      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        processedUrl = "https://" + url;
      }

      const domainString = extractDomain(processedUrl);
      const domain = processedUrl.split(domainString)[0] + domainString;

      return {
        domain: domain,
        leadId: lead.id,
        url: processedUrl,
        mode: lead.mode,
        googlePosition: -1,
        keywords,
        filterPosition: index + 1,
        useDomain: lead.useDomain,
      };
    });
  });

  for (const urlData of urlsToCreate) {
    try {
      await prisma.leadUrl.create({
        data: urlData,
      });
    } catch (error) {
      console.log(`Error creating lead URL: ${error}`);
    }
  }

  await prisma.lead.updateMany({
    where: { jobId: job.id },
    data: {
      status: !mode || mode === "serp" ? "pending" : "srp_scraped",
    },
  });
}

async function createSerpLeads(leads, totalProcessed) {
  await prisma.lead.createMany({
    data: leads,
  });
  return leads.length;
}

async function createLeadJob(csvFilePath, job, useDomain = false, mode) {
  let leads = [];
  let totalProcessed = 0;
  let dedupMap = new Map();

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on("data", async (row) => {
        const transformedData = transformDataSync(row, leadSchemaMap);
        const key = `${transformedData.sellerName}_${transformedData.businessName}`;
        const url = transformedData.url;
        delete transformedData.url;
        delete transformedData.errors;

        const lead = {
          ...transformedData,
          jobId: job.id,
          metadata: row,
          mode: mode,
          useDomain: useDomain,
          urls: mode == "input_domain" ? [url] : undefined,
        };

        if (mode == "input_domain") {
          if (dedupMap.has(key) && url) {
            dedupMap.get(key).urls.push(url);
            return;
          }
          dedupMap.set(key, lead);
        } else {
          leads.push(lead);
        }
      })
      .on("error", reject)
      .on("end", async () => {
        try {
          if (mode === "input_domain") {
            await createInputDomainLeads(leads, job, dedupMap, mode);
          } else if (leads.length > 0) {
            totalProcessed += await createSerpLeads(leads, totalProcessed);
          }
          console.log(`Completed processing ${totalProcessed} total leads`);
          resolve(job);
        } catch (error) {
          reject(error);
        }
      });
  });
}
async function processLeadGeneration(
  csvFilePath,
  useDomain = false,
  mode,
  data,
  fileName,
  searchPattern = "original"
) {
  try {
    const jobName = `${fileName}-${Date.now()}`;
    let job = await prisma.leadJob.create({
      data: {
        name: jobName,
        inputCsvPath: csvFilePath || "direct-input",
        inputCsvName: fileName,
        useDomain: useDomain,
        status: "pending",
        mode: mode,
        searchPattern: searchPattern,
      },
    });

    if (csvFilePath) {
      job = await createLeadJob(csvFilePath, job, useDomain, mode);
    } else {
      let dedupMap = new Map();
      const { sellerName, businessName, url, ...otherData } = data;
      const key = `${sellerName}_${businessName}`;

      const lead = {
        sellerName,
        businessName,
        ...otherData,
        jobId: job.id,
        metadata: data,
        mode: mode,
        useDomain: useDomain,
        urls: url || [],
      };

      dedupMap.set(key, lead);
      await createInputDomainLeads([], job, dedupMap, mode);
    }

    return job;
  } catch (error) {
    console.error("Error processing leads:", error);
    throw error;
  }
}
module.exports = {
  createLeadJob,
  processLeadGeneration,
};
