const { func } = require("joi");
const { filterDomains } = require("./filterDomains");
const { checkHtml } = require("./matchMethods/checkHtml");
const checkSerpOrganicSnippet = require("./matchMethods/checkSnippets");
const computeConfidence = require("./computeConfidence");
const { checkFuzzyDomain } = require("./matchMethods/checkFuzzyDomain");
const Bottleneck = require("bottleneck");
const { validateAddress } = require("./validaterMethods/validateAddress");
require("dotenv");
const limiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 200,
});

const generateLeadsThrottled = limiter.wrap(
  async function generateLeads(sellerData, keywords, csvWriter) {
    const { apiResponse: organicResults } = sellerData;
    const context = {};
    const sellerLogo = await getSellerImage(sellerData.sellerUrl);
    context.sellerLogo = sellerLogo;

    if (!organicResults) {
      const result = [
        {
          maxConfidenceUrls: "",
          maxConfidence: -1,
          keywords: keywords.join(" | "),
        },
      ];
      await csvWriter.writeRecords(result);
      return result;
    }

    const filteredDomains = filterDomains(organicResults);
    let passedDomains = [];

    //  Call ndividual Methods to ge the actual websites

    // Call Check WebHtml for with keywords

    for (const domainDatum of filteredDomains) {
      const { originalUrl, domain, organicData } = domainDatum;
      // console.log("----checking for url ", originalUrl);
      // const result = await checkHtml(originalUrl, keywords);
      const output = await evaluateChecks(
        [checkFuzzyDomain, checkSerpOrganicSnippet, checkHtml],
        domainDatum,
        keywords,
        context,
      );
      const finalScore = Object.values(output.individual).reduce(
        (a, b) => a + b,
        0,
      );
      if (finalScore > 1) {
        output.finalScore = finalScore;

        passedDomains.push(output);
      }
    }
    passedDomains = passedDomains
      .sort((a, b) => {
        if (b.finalScore !== a.finalScore) {
          return b.finalScore - a.finalScore;
        }
        return a.position - b.position;
      })
      .slice(0, 5)
      .map((elem, index) => {
        elem.sortedIndex = index + 1;
        return elem;
      });

    const processedDomains = await validateDomines(
      passedDomains,
      keywords,
      sellerLogo,
    );

    const results = processedDomains.map((d) => {
      return {
        keywords: keywords.join(" | "),
        confidence: d.finalScore,
        url: d.originalUrl,
        sellerUrl: sellerData.sellerUrl,
        individual: JSON.stringify(d.individual),
        companyAddress: d.companyAddress,
        googlePosition: d.position,
        filterDomainPosition: d.sortedIndex,
        customStatus: customStatusChecker(d),
        validationResult: JSON.stringify({
          textValidationResult: d.textValidationResult,
          imageValidationResult: d.imageValidationResult,
          addressValidationResult: d.addressValidationResult,
          // Add Address Validation HERE
        }),
      };
    });
    await csvWriter.writeRecords(results);
    return results;
  },
);

function customStatusChecker(data) {
  if (
    data &&
    data.textValidationResult === "True" &&
    data.imageValidationResult === "True"
  ) {
    return "All Validation Approved";
  }
  if (data && data.textValidationResult === "True" && data.position === 1) {
    return "Text Validation + Google Top Site";
  }
  if (data && data.textValidationResult === "True" && data.sortedIndex === 1) {
    return "Text Validation + Filtered Top Site";
  }

  return "Not Applicable";
}

async function validateDomines(passedDomains, keywords, sellerLogo) {
  const finalResults = [];
  console.log(passedDomains);
  for (const domain of passedDomains) {
    const validationResult = await validateHtmlText(
      domain.originalUrl,
      keywords,
    );
    domain.textValidationResult = validationResult.message;
    if (sellerLogo) {
      const websiteSS = await getScreenshotFromDomain(domain.originalUrl);
      if (websiteSS) {
        domain.imageValidationResult = await validateImage(
          websiteSS,
          sellerLogo,
          domain.originalUrl,
          keywords,
        );
        console.log("Image validation result", domain.imageValidationResult);
      }
    }
    console.log(domain, domain.companyAddress, "Company Address");
    domain.addressValidationResult = await validateAddress(domain, keywords[2]);
    finalResults.push(domain);
  }
  return finalResults;
}

async function evaluateChecks(callbacks, organicDatum, keywords, context) {
  for (let callback of callbacks) {
    const data = await callback(organicDatum, keywords, context);
    const result = computeConfidence(data, callback.config);
    console.log(
      `Result For :--- ${callback.config.key} : ${result.finalScore}`,
    );
    organicDatum = { ...organicDatum, ...result };
    console.log("------------------------------------");
    console.log({ organicDatum });
  }

  return organicDatum;
}
// Call

const csv = require("csv-parser");
const fs = require("fs");
const { getSellerImage } = require("../../utils/getSellerImage");
const validateHtmlText = require("./validaterMethods/validateHtmlText");
const { getScreenshotFromDomain } = require("../../utils/getSSFromDomain");
const { validateImage } = require("./validaterMethods/validateImage");
const puppeteer = require("../../utils/puppeteer");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;

const output = [];

const readCSV = async () => {
  let index = 0;
  return new Promise((resolve, reject) => {
    fs.createReadStream(`${__dirname}/_Output.csv`)
      .pipe(csv())
      .on("data", (row) => {
        // if (
        //   row.Name === "EVIVA" ||
        //   row.Name === "ReelFlyRod" ||
        //   row.Name === "SunCup Juice"
        // ) {
        console.log("Index", index, row.Name);
        // console.log(row["Seller Url"],Object.keys(row))
        // if (index >= 367) {

        output.push({
          name: row.Name,
          businessName: row["Business Name"],
          apiResponse: row.apiResponse,
          companyAddress: row["Company Address"] || row["sellerAddress"] || "",
          sellerUrl:
            row["Seller URL"] ||
            row["SellerUrl"] ||
            row["Seller Url"] ||
            row["Seller URL"] ||
            row["Seller URL"] ||
            "",
          googlePosition: row.Position,
        });
        index++;
        // }
      })
      .on("error", (error) => {
        console.error("Error reading CSV:", error);
        reject(error);
      })
      .on("end", () => {
        resolve(output);
      });
  });
};
const csvWriter = createCsvWriter({
  path: `${__dirname}/leads-${Date.now()}.csv`,
  header: [
    { id: "keywords", title: "Keywords" },
    { id: "confidence", title: "Confidence" },
    { id: "url", title: "Confident Url" },
    { id: "sellerUrl", title: "Seller Url" },
    { id: "individual", title: "Individual" },
    { id: "companyAddress", title: "Company Address" },
    { id: "validationResult", title: "Validation Results" },
    { id: "googlePosition", title: "Google Position" },
    { id: "filterDomainPosition", title: "Filter Domain Position" },
    { id: "customStatus", title: "Custom Status" },
  ],
});
const ACTIVE_INDEXES_FILE = `${__dirname}/active-indexes.json`;

const loadActiveIndexes = async () => {
  try {
    const data = await fs.promises.readFile(ACTIVE_INDEXES_FILE);
    return new Set(JSON.parse(data));
  } catch {
    return new Set();
  }
};

const saveActiveIndexes = async (activeIndexes) => {
  await fs.promises.writeFile(
    ACTIVE_INDEXES_FILE,
    JSON.stringify([...activeIndexes]),
  );
};

async function main() {
  let results = [];
  const promises = [];
  await readCSV();

  const startTime = Date.now();
  const CONTINUE = process.env.CONTINUE;

  let activeIndexes = await loadActiveIndexes();
  if (!CONTINUE) {
    activeIndexes = new Set();
  }
  for (let [index, item] of output.entries()) {
    if (process.env.CONTINUE && !activeIndexes.has(index)) {
      continue;
    }

    if (item.apiResponse && item.apiResponse.toLowerCase() !== "undefined") {
      const apiResponse = JSON.parse(item.apiResponse);
      const keywords = [
        item.name,
        item.businessName,
        item.companyAddress,
      ].filter((k) => k);
      activeIndexes.add(index);
      await saveActiveIndexes(activeIndexes);

      promises.push(
        generateLeadsThrottled(
          { ...item, apiResponse },
          keywords,
          csvWriter,
        ).finally(async () => {
          activeIndexes.delete(index);
          await saveActiveIndexes(activeIndexes);
        }),
      );
    } else {
      const result = {
        keywords: `${item.name} + ${item.businessName}`,
        confidence: -1,
        url: "",
      };
      await csvWriter.writeRecords([result]);
      results = [...results, result];
      activeIndexes.delete(index);
      await saveActiveIndexes(activeIndexes);
    }
  }
  const responses = await Promise.all(promises);
  // Close Pupeeter instance
  await puppeteer.closeBrowser();
  results = results.concat(...responses);
  console.log("Start Time:", new Date(startTime).toLocaleString());
  console.log("End Time:", new Date(Date.now()).toLocaleString());
}
main();
