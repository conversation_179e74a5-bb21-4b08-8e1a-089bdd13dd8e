const prisma = require("../database/prisma/getPrismaClient");
const {
  findParentGroup,
  createEntity,
  mergeGroups,
  choose<PERSON><PERSON><PERSON>,
  processSeller,
  clearSellerTreeData
} = require("../utils/enhancedSellerGroupUnionFind");

const BATCH_SIZE = 1000;
const VALID_WEBSITE_STATUSES = ['Final Correct'];

/**
 * Simple seller group migration using batch processing
 */
async function enhancedMigrateSellerGroups(options = {}) {
  const {
    dryRun = false,
    batchSize = BATCH_SIZE,
    verbose = true,
    onlyValidDomains = true,
    validWebsiteStatuses = VALID_WEBSITE_STATUSES,
    clearExisting = true
  } = options;

  console.log(`Starting seller group migration (dry run: ${dryRun})`);
  console.log(`Batch size: ${batchSize}, Valid statuses: ${validWebsiteStatuses.join(', ')}`);

  const startTime = Date.now();
  
  try {
    // Step 1: Clear existing data if requested
    if (clearExisting) {
      // await clearSellerTreeData({ dryRun });
    }

    if (dryRun) {
      console.log("DRY RUN: Would process sellers in batches");
      return { success: true, message: "Dry run completed", processingTime: 0 };
    }

    // Step 2: Process all sellers in batches
    const result = await processSellersInBatches({
      batchSize,
      verbose,
      onlyValidDomains,
      validWebsiteStatuses
    });

    const processingTime = (Date.now() - startTime) / 1000;
    
    console.log("\n=== Migration Summary ===");
    console.log(`Total sellers processed: ${result.totalSellers}`);
    console.log(`Entities created: ${result.entitiesCreated}`);
    console.log(`Groups merged: ${result.groupsMerged}`);
    console.log(`Processing time: ${processingTime.toFixed(2)} seconds`);

    return {
      success: true,
      statistics: result,
      processingTime
    };

  } catch (error) {
    console.error("Migration failed:", error);
    
    if (!dryRun && clearExisting) {
      console.log("Rolling back changes...");
      try {
        await clearSellerTreeData({ dryRun: false });
        console.log("Rollback completed");
      } catch (rollbackError) {
        console.error("Rollback failed:", rollbackError);
      }
    }

    throw error;
  }
}

/**
 * Process sellers in batches
 */
async function processSellersInBatches(options) {
  const {
    batchSize,
    verbose,
    onlyValidDomains,
    validWebsiteStatuses
  } = options;

  let skip = 0;
  let totalProcessed = 0;
  let entitiesCreated = 0;
  let groupsMerged = 0;
  let batchNumber = 0;

  console.log(`Processing sellers in batches of ${batchSize}...`);

  while (true) {
    // Fetch next batch
    const batch = await fetchSellerBatch(skip, batchSize, onlyValidDomains, validWebsiteStatuses);
    
    if (batch.length === 0) {
      console.log("✅ No more sellers to process");
      break;
    }

    batchNumber++;
    totalProcessed += batch.length;
    skip += batchSize;

    console.log(`\n📦 Processing batch ${batchNumber} (${batch.length} sellers) - Total: ${totalProcessed}`);

    // Process each seller in the batch
    for (const seller of batch) {
      try {
        const validationOptions = {
          validWebsiteStatuses: validWebsiteStatuses
        };
        const result = await processSeller(seller, validationOptions);
        
        entitiesCreated += result.created.length;
        if (result.merged) {
          groupsMerged++;
        }

        if (verbose && result.action !== 'already_grouped') {
          console.log(`  ${seller.amazon_seller_id}: ${result.action} (created: ${result.created.join(', ') || 'none'})`);
        }
      } catch (error) {
        console.error(`  Error processing seller ${seller.amazon_seller_id}:`, error.message);
      }
    }
  }

  return {
    totalSellers: totalProcessed,
    entitiesCreated,
    groupsMerged,
    batchCount: batchNumber
  };
}

/**
 * Fetch a single batch of sellers
 */
async function fetchSellerBatch(skip, batchSize, onlyValidDomains, validWebsiteStatuses) {
  // Remove filtering - process ALL sellers
  // Domain validation will be done in processSeller function
  return await prisma.amazonSeller.findMany({
    select: {
      id: true,
      amazon_seller_id: true,
      domain: true,
      seller_group_id: true,
      website_status: true
    },
    take: batchSize,
    skip: skip
  });
}

/**
 * Command line interface
 */
async function main() {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]);
        break;
      case '--quiet':
        options.verbose = false;
        break;
      case '--allow-invalid-domains':
        options.onlyValidDomains = false;
        break;
      case '--keep-existing':
        options.clearExisting = false;
        break;
      case '--valid-statuses':
        options.validWebsiteStatuses = args[++i].split(',').map(s => s.trim());
        break;
      case '--help':
        console.log(`
Seller Group Migration Script

Usage: node enhancedMigrateSellerGroups.js [options]

Options:
  --dry-run                    Simulate the migration without making changes
  --batch-size <number>        Number of records to process in each batch (default: 1000)
  --quiet                      Reduce output verbosity
  --allow-invalid-domains      Process domains regardless of website_status
  --keep-existing              Don't clear existing seller tree data
  --valid-statuses <list>      Valid website statuses (comma-separated)
  --help                       Show this help message

Examples:
  node enhancedMigrateSellerGroups.js --dry-run
  node enhancedMigrateSellerGroups.js --batch-size 500 --allow-invalid-domains
        `);
        return;
    }
  }

  console.log("Starting seller group migration...");
  const result = await enhancedMigrateSellerGroups(options);
  
  if (result.success) {
    console.log("\n✅ Migration completed successfully!");
    if (options.dryRun) {
      console.log("This was a dry run. Run without --dry-run to apply changes.");
    }
    console.log(`📊 Processing took ${result.processingTime.toFixed(2)} seconds`);
  }
}

// Run if called directly
if (require.main === module) {
  main()
    .catch(error => {
      console.error("Migration script failed:", error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

module.exports = {
  enhancedMigrateSellerGroups
};