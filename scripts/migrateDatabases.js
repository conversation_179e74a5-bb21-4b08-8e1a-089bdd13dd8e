const sellerDb = require("../database/prisma/getPrismaClient");

async function cleanDatabase() {
  // First delete LeadUrls since they reference Leads
  await leadDb.leadUrl.deleteMany({});
  console.log("Deleted all LeadUrls");

  // Then delete Leads since they reference LeadJobs
  await leadDb.lead.deleteMany({});
  console.log("Deleted all Leads");

  // Finally delete LeadJobs since they're the parent table
  await leadDb.leadJob.deleteMany({});
  console.log("Deleted all LeadJobs");
  const result = await leadDb.serp_Cache.deleteMany({});
  console.log(`Deleted ${result.count} Serp_Cache entries`);
}
async function migrateUrls(lead, newLeadId) {
  const urls = await leadDb.leadUrl.findMany({
    where: { leadId: lead.id },
  });

  const totalUrls = urls.length;
  let count = 1;
  for (const url of urls) {
    // Remove data field and create URL with new leadId
    console.log(`Processing URL ${count}/${totalUrls}`);
    count++;
    const presentUrl = await sellerDb.leadUrl.findMany({
      where: { url: url.url, leadId: lead.leadId },
    });
    if (presentUrl.length > 0) {
      console.log(`URL ${url.url} already exists for lead ${lead.id}`);
      continue;
    }
    const { data, ...urlData } = url;
    await sellerDb.leadUrl.create({
      data: {
        ...urlData,
        leadId: newLeadId,
      },
    });
    console.log(`Created URL for lead ${newLeadId}`);
  }
}

async function migrateLeads(job, newJobId) {
  const leads = await leadDb.lead.findMany({
    where: { jobId: job.id },
  });

  const totalLeads = leads.length;
  let count = 1;
  for (const lead of leads) {
    // Create lead with new jobId
    // console.log(lead);
    console.log(`Processing lead ${count}/${totalLeads}`);
    count++;
    const presentLead = await sellerDb.lead.findMany({
      where: { sellerUrl: lead.sellerUrl, jobId: job.id },
    });
    if (presentLead.length > 0) {
      console.log(`Lead ${lead.id} already exists in seller database`);
      console.log(`Skipping lead ${lead.id}`);
      await migrateUrls(lead, presentLead[0].id);
      continue;
    }
    const newLead = await sellerDb.lead.create({
      data: {
        ...lead,
        jobId: newJobId,
      },
    });
    console.log(`Created lead ${newLead.id} for job ${newJobId}`);

    // Migrate URLs for this lead
    await migrateUrls(lead, newLead.id);
  }
}

async function migrate() {
  try {
    // 1. Migrate all jobs first
    const jobs = await leadDb.leadJob.findMany();
    console.log(`Found ${jobs.length} jobs to migrate`);

    const totalJobs = jobs.length;
    let count = 1;
    for (const job of jobs) {
      // Create job
      console.log(`Processing job ${count}/${totalJobs}`);
      count++;
      const presentJob = await sellerDb.leadJob.findMany({
        where: { name: job.name },
      });
      console.log(presentJob);
      if (presentJob.length > 0) {
        console.log(`Job ${job.id} already exists in seller database`);
        await migrateLeads(job, presentJob[0].id);
        continue;
      }
      const newJob = await sellerDb.leadJob.create({
        data: job,
      });
      console.log(`Created job ${newJob.id}`);

      // Migrate all leads for this job
      await migrateLeads(job, newJob.id);
    }

    // 2. Migrate SERP cache at the end
    // const serpCache = await leadDb.serp_Cache.findMany();
    // for (const cache of serpCache) {
    //   const presentCache = await sellerDb.serp_Cache.findMany({
    //     where: {
    //       hash: cache.hash,
    //     },
    //   });
    //   if (presentCache.length > 0) {
    //     console.log(`SERP cache entry ${cache.id} already exists in seller database`);
    //     continue;
    //   }
    // await sellerDb.serp_Cache.create({
    //   data: cache,
    // });
    // }
    // console.log(`Migrated ${serpCache.length} SERP cache entries`);

    console.log("Migration completed successfully");
  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  } finally {
    await leadDb.$disconnect();
    await sellerDb.$disconnect();
  }
}

// cleanDatabase().catch(console.error);
