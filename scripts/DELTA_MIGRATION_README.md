# Delta Migration Scripts

This directory contains scripts for incrementally migrating and updating data between the legacy tables (`Company`, `Prospect`) and the new Amazon-focused tables (`AmazonSeller`, `SellerGroup`, `AmazonProspect`).

## Overview

The delta migration system allows you to:
- **Incrementally process only changed/new records** since the last migration run
- **Fill missing relationships** and data in existing records
- **Update existing records** with new information from source tables
- **Run safely multiple times** without duplicating data

## Scripts

### 1. `deltaMigration.js` - Main Migration Script

The primary delta migration script that processes changes and updates data.

**What it does:**
- Identifies `Company` records modified since last run and updates corresponding `AmazonSeller`/`SellerGroup` records
- Identifies `Prospect` records modified since last run and updates corresponding `AmazonProspect` records
- Fills missing relationships (e.g., `AmazonSellers` without `seller_group_id`)
- Tracks migration state to enable incremental runs

**Usage:**
```bash
# Run the delta migration
node scripts/deltaMigration.js
```

**Migration Process:**
1. **State Tracking**: Checks `JobRun` table for last migration timestamp
2. **Company Processing**: 
   - Finds companies created/updated since last run
   - Creates/updates corresponding `SellerGroup` records
   - Creates/updates `AmazonSeller` records with proper marketplace mapping
3. **Prospect Processing**:
   - Finds prospects created/updated since last run
   - Creates/updates corresponding `AmazonProspect` records
   - Links to appropriate `AmazonSeller` records
   - Updates `ClientsProspectsMatching` relationships
4. **Missing Data Fill**:
   - Links orphaned `AmazonSellers` to `SellerGroups`
   - Links orphaned `AmazonProspects` to `AmazonSellers`
5. **State Update**: Records completion timestamp for next run

### 2. `deltaMigrationCheck.js` - Dry Run & Analysis

A read-only script that shows what would be processed without making changes.

**What it shows:**
- Number of records that would be processed
- Examples of recent changes
- Missing data that would be filled
- Current data statistics and migration coverage

**Usage:**
```bash
# Check what would be migrated (dry run)
node scripts/deltaMigrationCheck.js
```

**Sample Output:**
```
=== Delta Migration Check (Dry Run) ===

Last migration run: 2024-01-01T00:00:00.000Z
Current check time: 2024-01-15T10:30:00.000Z

📊 Company -> AmazonSeller/SellerGroup Changes:
  Total companies to process: 150
  Recent examples:
    1. [NEW] ID: 12345, Seller: A1B2C3D4E5F6G7, Name: Example Store
    2. [UPDATED] ID: 12344, Seller: H7G6F5E4D3C2B1, Name: Another Store
    ... and 148 more

📊 Prospect -> AmazonProspect Changes:
  Total prospects to process: 75
  Recent examples:
    1. [NEW] ID: 54321, Email: <EMAIL>, Seller: A1B2C3D4E5F6G7
    ... and 74 more

📊 Missing Data to Fill:
  AmazonSellers without seller group: 25
  AmazonProspects without seller link: 10

📈 Current Data Summary:
  Companies: 10,500
  SellerGroups: 8,200
  AmazonSellers: 9,100
  Prospects: 25,000
  AmazonProspects: 22,000

📋 Migration Coverage Analysis:
  Companies with Amazon Seller ID: 9,500/10,500 (90.5%)
  Prospects with email/LinkedIn: 24,500/25,000 (98.0%)
```

## Key Features

### Incremental Processing
- Only processes records modified since the last successful run
- Uses `createdAt` and `updatedAt` timestamps for change detection
- Tracks migration state in `JobRun` table

### Smart Deduplication
- Uses find-first-then-create-or-update pattern to handle existing records
- Maintains unique constraints on (`amazon_seller_id`, `marketplace`)
- Links records based on email/LinkedIn for prospects

### Marketplace Handling
- Maps seller country matchings to appropriate marketplaces
- Prioritizes marketplaces: US → CA → UK → null
- Creates separate `AmazonSeller` records per marketplace

### Relationship Management
- Maintains all foreign key relationships
- Updates `ClientsProspectsMatching` to point to new `AmazonProspect` records
- Links `AmazonProspects` to appropriate `AmazonSellers`

### Error Handling
- Continues processing on individual record failures
- Logs errors with specific record IDs
- Provides detailed progress reporting

## Data Flow

```mermaid
graph TD
    A[Company Table] --> D[AmazonSeller]
    A --> E[SellerGroup]
    B[Prospect Table] --> F[AmazonProspect]
    C[SellerCountryMatching] --> D
    D --> F
    E --> D
    F --> G[ClientsProspectsMatching]
    
    subgraph "Delta Migration Process"
        H[Check Last Run Time] --> I[Find Changed Records]
        I --> J[Process Companies]
        I --> K[Process Prospects]
        J --> L[Fill Missing Data]
        K --> L
        L --> M[Update Run Timestamp]
    end
```

## Safety Considerations

### Before Running
1. **Always run the check script first** to understand what will be processed
2. **Backup your database** especially before the first run
3. **Test in development environment** first
4. **Check for any ongoing data modifications** that might conflict

### Running Safely
1. **Use during low-traffic periods** to minimize conflicts
2. **Monitor the process** as it provides detailed progress logs
3. **Have rollback plan ready** in case of issues
4. **Validate results** after completion

### After Running
1. **Verify record counts** match expectations
2. **Check relationships** are correctly established
3. **Test application functionality** with new data structure
4. **Monitor performance** of queries against new tables

## Troubleshooting

### Common Issues

**"No previous migration run found"**
- Normal on first run - will process all records
- Script creates initial state tracking automatically

**"Seller group creation failed"**
- May indicate duplicate group names or constraint violations
- Script handles this gracefully and continues

**"Upsert constraint violation"**
- Usually due to concurrent modifications
- Script uses find-then-update pattern to avoid most issues

**Memory issues with large datasets**
- Adjust `BATCH_SIZE` constant in script (default: 1000)
- Process during low-memory usage periods

### Monitoring Progress
- Script provides detailed logging with progress indicators
- Shows processing rates and estimated completion times
- Logs individual errors without stopping overall process

### Recovery
- Script is idempotent - safe to re-run
- Failed records can be processed again on next run
- State tracking ensures no duplicate processing

## Configuration

### Batch Sizes
```javascript
const BATCH_SIZE = 1000;           // Records per batch
const MAX_TRANSACTION_SIZE = 500;  // Records per transaction
```

### State Tracking
- Migration state stored in `JobRun` table
- Key: `'DELTA_MIGRATION_LAST_RUN'`
- Can be manually reset if needed

## Integration

### Scheduled Runs
Consider setting up automated runs via cron:
```bash
# Run delta migration daily at 2 AM
0 2 * * * cd /path/to/sellerbot && node scripts/deltaMigration.js >> logs/delta-migration.log 2>&1
```

### Monitoring
- Check return status codes for success/failure
- Monitor log files for errors
- Set up alerts for failed migrations
- Track processing times and record counts

## Support

For issues or questions:
1. Check this README first
2. Run the check script to understand current state
3. Review error logs for specific failures
4. Contact the development team with specific error messages and context 