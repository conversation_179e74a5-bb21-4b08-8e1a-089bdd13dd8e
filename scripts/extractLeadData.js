const prisma = require('../database/prisma/getPrismaClient');
const converter = require('json-2-csv');
const path = require('path');
const fs = require('fs');

async function extractLeadData() {
  console.log("Starting lead extraction...");

  try {
    // Step 1: Get the leadUrl id and lead id where textValidation is empty and googlePosition is < 3
    const basicUrlData = await prisma.leadUrl.findMany({
      where: {
        leadId: {
          gte: 67698
        },
        googlePosition: {
          lte: 3
        },
        hasAddressMatch: false,
        hasEmailMatch: false,
        hasImageMatch: false,
        hasTextMatch: false,
        hasNumberMatch: false,
      },
      select: {
        id: true,
        leadId: true,
        url: true,
        domain: true,
        googlePosition: true
      }
    });

    console.log(`Found ${basicUrlData.length} URLs matching the position and discoveredPages criteria`);

    // Get unique lead IDs from the URLs
    const uniqueLeadIds = [...new Set(basicUrlData.map(url => url.leadId))];
    console.log(`Found ${uniqueLeadIds.length} unique leads from these URLs`);

    // Step 2: Group URLs by lead ID for later use
    const urlsByLeadId = basicUrlData.reduce((acc, url) => {
      if (!acc[url.leadId]) {
        acc[url.leadId] = [];
      }
      acc[url.leadId].push(url);
      return acc;
    }, {});

    // Step 3: Fetch lead details in batches
    const batchSize = 100;
    const validLeads = [];

    for (let i = 0; i < uniqueLeadIds.length; i += batchSize) {
      const batchIds = uniqueLeadIds.slice(i, i + batchSize);
      console.log(`Processing batch ${i / batchSize + 1}/${Math.ceil(uniqueLeadIds.length / batchSize)}, size: ${batchIds.length}`);

      const leadBatch = await prisma.lead.findMany({
        where: {
          id: {
            in: batchIds
          }
        },
        select: {
          jobId: true,
          metadata: true
        }
      });

      // Step 4: Filter leads by job ID range and non-CN
      for (const lead of leadBatch) {
        if (lead.jobId >= 45 && lead.jobId <= 51 && (!lead.country || lead.country.toUpperCase() !== 'CN')) {
          validLeads.push(lead.metadata);
        }
      }
    }

    console.log(`Found ${validLeads.length} leads after filtering for job IDs 45-51 and excluding CN`);

    // Step 5: Save to CSV and JSON (implement these functions as needed)
    await saveToCSV(validLeads);
    // saveToJSON(validLeads);

    return validLeads;
  } catch (error) {
    console.error("Error extracting lead data:", error);
    throw error;
  } finally {
    // Disconnect from the database
    await prisma.$disconnect();
  }
}

async function saveToCSV(leads) {
  try {
    const csvContent = await converter.json2csv(leads);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const csvFilePath = path.join(__dirname, `leads-${timestamp}.csv`);
    fs.writeFileSync(csvFilePath, csvContent);
    console.log(`Data saved to ${csvFilePath}`);
  } catch (error) {
    console.error('Error saving CSV:', error);
    throw error;
  }
}



// Run the extraction process
extractLeadData()
  .then(leads => {
    console.log(`Extraction complete. Found ${leads.length} leads.`);
  })
  .catch(error => {
    console.error("Failed to extract lead data:", error);
  });