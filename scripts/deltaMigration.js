const prisma = require("../database/prisma/getPrismaClient");
const { 
  migrateCompanyToSeller, 
  fetchCompanyBatch, 
  mapCompanyToSellerData 
} = require("./migrateCompanyToSeller");
const { 
  upsertAmazonProspect, 
  updateClientsProspectsMatching 
} = require("./migrateProspectToAmazonProspect");

// Configuration
const BATCH_SIZE = 2000; // Use same batch size as original scripts
const MAX_TRANSACTION_SIZE = 500;

/**
 * Conversion utility functions (same as original scripts)
 */
function convertToFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
}

function convertToInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const num = parseInt(value, 10);
  return isNaN(num) ? null : num;
}

function convertToDate(value) {
  if (value === null || value === undefined || value === '') return null;
  const date = new Date(value);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Fetch companies modified since lastRunTime (filtered version of fetchCompanyBatch)
 */
async function fetchChangedCompanies(lastRunTime) {
  return prisma.company.findMany({
    where: {
      OR: [
        { createdAt: { gte: lastRunTime } },
        { updatedAt: { gte: lastRunTime } }
      ]
    },
    orderBy: { id: 'asc' }
  });
}

/**
 * Fetch prospects modified since lastRunTime  
 */
async function fetchChangedProspects(lastRunTime) {
  return prisma.prospect.findMany({
    where: {
      OR: [
        { createdAt: { gte: lastRunTime } },
        { updatedAt: { gte: lastRunTime } }
      ]
    },
    include: {
      clientsProspects: true,
    },
    orderBy: {
      prospect_id: "asc",
    }
  });
}

/**
 * Delta migration using EXACT same logic as migrateCompanyToSeller.js
 * Just with filtered input data
 */
async function deltaCompanyToSeller(lastRunTime) {
  console.log('Starting delta migration for Company -> AmazonSeller/SellerGroup...');
  
  // Get changed companies instead of all companies
  const companies = await fetchChangedCompanies(lastRunTime);
  console.log(`Found ${companies.length} changed companies to process`);
  
  if (companies.length === 0) {
    console.log('No changed companies found');
    return { companiesProcessed: 0, sellerGroupsCreated: 0, sellersCreated: 0, sellersUpdated: 0 };
  }

  // Use EXACT same logic from migrateCompanyToSeller.js
  // Preload ALL seller country matchings into memory (reduce DB hits)
  const allSellerCountryMatchings = await prisma.sellerCountryMatching.findMany();
  const matchingsBySellerID = allSellerCountryMatchings.reduce((acc, match) => {
    if (!acc[match.amazon_seller_id]) acc[match.amazon_seller_id] = [];
    acc[match.amazon_seller_id].push(match);
    return acc;
  }, {});
  
  // Stats tracking
  let companiesProcessed = 0;
  let sellerGroupsCreated = 0;
  let sellersCreated = 0;
  let sellersUpdated = 0;
  
  // Process in batches (same as original)
  let processedSoFar = 0;
  
  while (processedSoFar < companies.length) {
    // Get a batch of companies
    const batchEnd = Math.min(processedSoFar + BATCH_SIZE, companies.length);
    const batch = companies.slice(processedSoFar, batchEnd);
    
    companiesProcessed += batch.length;
    
    // 1. CREATE ALL SELLER GROUPS IN ONE TRANSACTION (same logic)
    const sellerGroupData = batch.map(company => ({
      name: company.name || `Group-${company.amazon_seller_id}`
    }));
    
    // Check for existing seller groups before creating new ones
    const existingGroups = await prisma.sellerGroup.findMany({
      where: { name: { in: batch.map(c => c.name || `Group-${c.amazon_seller_id}`) } }
    });

    const newSellerGroupData = sellerGroupData.filter(data => {
      const existingGroup = existingGroups.find(group => group.name === data.name);
      return !existingGroup;
    });

    const newSellerGroups = await prisma.$transaction(
      newSellerGroupData.map(data => prisma.sellerGroup.create({ data }))
    );
    
    sellerGroupsCreated += newSellerGroups.length;
    
    // Create lookup for companies and their seller groups
    const sellerGroupsByCompanyId = {};
    batch.forEach((company) => {
      const groupName = company.name || `Group-${company.amazon_seller_id}`;
      
      // First check if group already exists
      const existingGroup = existingGroups.find(group => group.name === groupName);
      if (existingGroup) {
        sellerGroupsByCompanyId[company.id] = existingGroup.id;
      } else {
        // Find the newly created group
        const newGroup = newSellerGroups.find(group => group.name === groupName);
        if (newGroup) {
          sellerGroupsByCompanyId[company.id] = newGroup.id;
        }
      }
    });
    
    // 2. CREATE/UPDATE SELLERS IN BULK TRANSACTIONS (exact same logic)
    const sellerCreates = [];
    const sellerUpdates = [];
    
    // Get all existing sellers for this batch to avoid duplicates
    const sellerIds = batch.map(c => c.amazon_seller_id).filter(Boolean);
    const existingSellers = await prisma.amazonSeller.findMany({
      where: { amazon_seller_id: { in: sellerIds } }
    });
    
    // Map existing sellers by ID and marketplace for quick lookup
    const existingSellerMap = {};
    existingSellers.forEach(seller => {
      const key = `${seller.amazon_seller_id}_${seller.marketplace}`;
      existingSellerMap[key] = seller;
    });
    
    // Prepare seller operations (exact same logic)
    batch.forEach(company => {
      const countryMatches = matchingsBySellerID[company.amazon_seller_id] || [];
      const sellerGroupId = sellerGroupsByCompanyId[company.id];
      
      // If no country matches, still create a seller with null marketplace
      if (countryMatches.length === 0) {
        const sellerData = mapCompanyToSellerData(company, null, sellerGroupId);
        sellerCreates.push(sellerData);
      } else {
        countryMatches.forEach(match => {
          const sellerData = mapCompanyToSellerData(company, match.smartscout_country, sellerGroupId);
          const lookupKey = `${company.amazon_seller_id}_${match.smartscout_country}`;
          
          if (existingSellerMap[lookupKey]) {
            sellerUpdates.push({
              where: { id: existingSellerMap[lookupKey].id },
              data: sellerData
            });
          } else {
            sellerCreates.push(sellerData);
          }
        });
      }
    });
    
    // Execute seller creates in batches with error handling (exact same logic)
    for (let i = 0; i < sellerCreates.length; i += MAX_TRANSACTION_SIZE) {
      const createBatch = sellerCreates.slice(i, i + MAX_TRANSACTION_SIZE);
      try {
        // Use createMany with skipDuplicates option
        await prisma.amazonSeller.createMany({
          data: createBatch,
          skipDuplicates: true
        });
        sellersCreated += createBatch.length;
      } catch (error) {
        // If createMany fails, process items individually (same as original)
        console.log(`Batch operation failed, processing individually: ${error.message}`);
        for (const data of createBatch) {
          try {
            // First check if this seller already exists to avoid the constraint error
            const existing = await prisma.amazonSeller.findFirst({
              where: {
                amazon_seller_id: data.amazon_seller_id,
                marketplace: data.marketplace
              }
            });
            
            if (!existing) {
              await prisma.amazonSeller.create({ data });
              sellersCreated++;
            } else {
              // Update instead of create
              await prisma.amazonSeller.update({
                where: { id: existing.id },
                data
              });
              sellersUpdated++;
            }
          } catch (itemError) {
            console.error(`Error processing seller ${data.amazon_seller_id}/${data.marketplace}: ${itemError.message}`);
          }
        }
      }
    }
    
    // Execute seller updates in batches (same as original)
    for (let i = 0; i < sellerUpdates.length; i += MAX_TRANSACTION_SIZE) {
      const updateBatch = sellerUpdates.slice(i, i + MAX_TRANSACTION_SIZE);
      await prisma.$transaction(updateBatch.map(op => prisma.amazonSeller.update(op)));
      sellersUpdated += updateBatch.length;
    }
    
    console.log(`Processed batch: ${batch.length} companies, created ${newSellerGroups.length} new seller groups (${existingGroups.length} already existed), created ${sellerCreates.length} sellers, updated ${sellerUpdates.length} sellers`);
    
    processedSoFar = batchEnd;
  }
  
  return { companiesProcessed, sellerGroupsCreated, sellersCreated, sellersUpdated };
}

/**
 * Delta migration using EXACT same logic as migrateProspectToAmazonProspect.js
 * Just with filtered input data
 */
async function deltaProspectToAmazonProspect(lastRunTime) {
  console.log('Starting delta migration for Prospect -> AmazonProspect...');
  
  // Get changed prospects instead of all prospects
  const prospects = await fetchChangedProspects(lastRunTime);
  console.log(`Found ${prospects.length} changed prospects to process`);
  
  if (prospects.length === 0) {
    console.log('No changed prospects found');
    return { prospectsProcessed: 0, successCount: 0, errorCount: 0, skippedCount: 0, matchingUpdatedCount: 0 };
  }

  // Stats tracking (same as original)
  let processedCount = 0;
  let successCount = 0;
  let errorCount = 0;
  let skippedCount = 0;
  let matchingUpdatedCount = 0;

  // Process prospects in batches (same logic as original)
  let processedSoFar = 0;
  
  while (processedSoFar < prospects.length) {
    const batchEnd = Math.min(processedSoFar + BATCH_SIZE, prospects.length);
    const batch = prospects.slice(processedSoFar, batchEnd);
    
    console.log(`Processing batch of ${batch.length} prospects...`);

    // Process each prospect in the batch (exact same logic)
    for (const prospect of batch) {
      processedCount++;

      try {
        // Skip prospects without email or LinkedIn (same logic)
        if (!prospect.email && !prospect.person_linkedin) {
          console.log(`Skipping prospect ${prospect.prospect_id} - no email or LinkedIn`);
          skippedCount++;
          continue;
        }

        // Find associated AmazonSeller (same logic)
        let seller_id = null;
        if (prospect.amazon_seller_id) {
          const seller = await prisma.amazonSeller.findMany({
            where: { amazon_seller_id: prospect.amazon_seller_id },
            orderBy: {  
              marketplace: "asc",
            },
          });

          if (seller && seller.length > 0) {
            const sortedSellers = seller.sort((a, b) => {
              const marketplaceOrder = {
                US: 0,
                CA: 1,
                UK: 2,
                undefined: 3,
              };  
              return marketplaceOrder[a.marketplace] - marketplaceOrder[b.marketplace];
            });
            
            seller_id = sortedSellers[0].id;
          }
        }

        // Use EXACT same upsert logic from original script
        const amazonProspect = await upsertAmazonProspect(prospect, seller_id);

        // Update all ClientsProspectsMatching records (same logic)
        const updatedCount = await updateClientsProspectsMatching(
          prospect.clientsProspects,
          amazonProspect.prospect_id
        );
        
        matchingUpdatedCount += updatedCount;
        successCount++;
      } catch (error) {
        errorCount++;
        console.error(`Error processing prospect ${prospect.prospect_id}:`, error.message);
      }
    }

    console.log(`Batch progress: ${Math.min(processedSoFar + BATCH_SIZE, prospects.length)}/${prospects.length}`);
    processedSoFar = batchEnd;
  }

  return { prospectsProcessed: processedCount, successCount, errorCount, skippedCount, matchingUpdatedCount };
}

/**
 * Main delta migration function
 */
async function runDeltaMigration() {
  console.log('Starting delta migration...');
  
  try {
    const startTime = new Date();
    const lastRunTime = new Date('2025-07-07');
    
    console.log(`Last migration run: ${lastRunTime}`);
    console.log(`Current migration run: ${startTime}`);

    // Run delta migrations using exact same methods as full migrations
    const companyResults = await deltaCompanyToSeller(lastRunTime);
    const prospectResults = await deltaProspectToAmazonProspect(lastRunTime);

    // Print summary
    console.log('\n=== Delta Migration Summary ===');
    console.log('Company -> AmazonSeller/SellerGroup:');
    console.log(`  Companies processed: ${companyResults.companiesProcessed}`);
    console.log(`  Seller groups created: ${companyResults.sellerGroupsCreated}`);
    console.log(`  Sellers created: ${companyResults.sellersCreated}`);
    console.log(`  Sellers updated: ${companyResults.sellersUpdated}`);
    
    console.log('\nProspect -> AmazonProspect:');
    console.log(`  Prospects processed: ${prospectResults.prospectsProcessed}`);
    console.log(`  Successful migrations: ${prospectResults.successCount}`);
    console.log(`  Skipped: ${prospectResults.skippedCount}`);
    console.log(`  Errors: ${prospectResults.errorCount}`);
    console.log(`  Client matchings updated: ${prospectResults.matchingUpdatedCount}`);
    
    const endTime = new Date();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    console.log(`\nTotal migration time: ${duration} seconds`);
    
  } catch (error) {
    console.error('Delta migration failed:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runDeltaMigration()
    .then(() => {
      console.log('Delta migration completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('Delta migration failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

// Export functions for testing or use in other modules
module.exports = {
  runDeltaMigration,
  deltaCompanyToSeller,
  deltaProspectToAmazonProspect,
  fetchChangedCompanies,
  fetchChangedProspects
}; 