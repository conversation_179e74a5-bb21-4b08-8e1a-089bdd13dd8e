const prisma = require("../database/prisma/getPrismaClient");

// Configuration
const BATCH_SIZE = 1000;
const MAX_TRANSACTION_SIZE = 500;

// Helper functions for data conversion
function convertToFloat(value) {
  if (value === null || value === undefined || value === "") return null;
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
}

function convertToInt(value) {
  if (value === null || value === undefined || value === "") return null;
  const num = parseInt(value, 10);
  return isNaN(num) ? null : num;
}

function convertToDate(value) {
  if (value === null || value === undefined || value === "") return null;
  const date = new Date(value);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Migrate only new companies to sellers
 */
async function migrateNewCompanies() {
  console.log("Starting migration of new companies to sellers...");

  // Preload ALL seller country matchings into memory
  const allSellerCountryMatchings =
    await prisma.sellerCountryMatching.findMany();
  const matchingsBySellerID = allSellerCountryMatchings.reduce((acc, match) => {
    if (!acc[match.amazon_seller_id]) acc[match.amazon_seller_id] = [];
    acc[match.amazon_seller_id].push(match);
    return acc;
  }, {});

  let skip = 0;
  let hasMore = true;
  let totalProcessed = 0;
  let totalCreated = 0;
  let totalSkipped = 0;

  while (hasMore) {
    // Get a batch of companies
    const companies = await prisma.company.findMany({
      skip,
      take: BATCH_SIZE,
      orderBy: { id: "asc" },
    });

    if (companies.length === 0) {
      hasMore = false;
      continue;
    }

    // Get all seller IDs for this batch
    const sellerIds = companies.map((c) => c.amazon_seller_id).filter(Boolean);

    // Get existing sellers to skip them
    const existingSellers = await prisma.amazonSeller.findMany({
      where: { amazon_seller_id: { in: sellerIds } },
      select: { amazon_seller_id: true, marketplace: true },
    });

    // Create a lookup map for existing sellers
    const existingSellerMap = new Map();
    existingSellers.forEach((seller) => {
      const key = `${seller.amazon_seller_id}_${seller.marketplace}`;
      existingSellerMap.set(key, true);
    });

    // Filter companies that need new seller groups
    const newCompanies = companies.filter((company) => {
      const countryMatches =
        matchingsBySellerID[company.amazon_seller_id] || [];
      return countryMatches.some((match) => {
        const key = `${company.amazon_seller_id}_${match.smartscout_country}`;
        return !existingSellerMap.has(key);
      });
    });

    if (newCompanies.length > 0) {
      // Create seller groups for new companies
      const sellerGroupData = newCompanies.map((company) => ({
        name: company.name || `Group-${company.amazon_seller_id}`,
      }));

      const sellerGroups = await prisma.$transaction(
        sellerGroupData.map((data) => prisma.sellerGroup.create({ data }))
      );

      // Map companies to their new seller groups
      const sellerGroupsByCompanyId = {};
      newCompanies.forEach((company, index) => {
        sellerGroupsByCompanyId[company.id] = sellerGroups[index].id;
      });

      // Prepare seller creates for new records
      const sellerCreates = [];
      newCompanies.forEach((company) => {
        const countryMatches =
          matchingsBySellerID[company.amazon_seller_id] || [];
        const sellerGroupId = sellerGroupsByCompanyId[company.id];

        countryMatches.forEach((match) => {
          const key = `${company.amazon_seller_id}_${match.smartscout_country}`;
          if (!existingSellerMap.has(key)) {
            sellerCreates.push(
              mapCompanyToSellerData(
                company,
                match.smartscout_country,
                sellerGroupId
              )
            );
          }
        });
      });

      // Create new sellers in batches
      for (let i = 0; i < sellerCreates.length; i += MAX_TRANSACTION_SIZE) {
        const batch = sellerCreates.slice(i, i + MAX_TRANSACTION_SIZE);
        await prisma.amazonSeller.createMany({
          data: batch,
          skipDuplicates: true,
        });
      }

      totalCreated += sellerCreates.length;
    }

    totalProcessed += companies.length;
    totalSkipped += companies.length - newCompanies.length;

    console.log(
      `Processed ${totalProcessed} companies, Created ${totalCreated} new sellers, Skipped ${totalSkipped} existing sellers`
    );
    skip += BATCH_SIZE;
  }
}

/**
 * Migrate only new prospects to amazon prospects
 */
async function migrateNewProspects() {
  console.log("Starting migration of new prospects to amazon prospects...");

  let skip = 0;
  let hasMore = true;
  let totalProcessed = 0;
  let totalCreated = 0;
  let totalSkipped = 0;

  while (hasMore) {
    // Fetch a batch of prospects
    const prospects = await prisma.prospect.findMany({
      skip,
      take: BATCH_SIZE,
      include: {
        clientsProspects: true,
        sellerGroup: true,
      },
      orderBy: {
        prospect_id: "asc",
      },
    });

    if (prospects.length === 0) {
      hasMore = false;
      continue;
    }

    // Get unique identifiers for checking existence
    const uniqueEmails = [
      ...new Set(prospects.map((p) => p.email).filter(Boolean)),
    ];
    const uniqueLinkedIns = [
      ...new Set(prospects.map((p) => p.person_linkedin).filter(Boolean)),
    ];

    // Check for existing amazon prospects
    const existingAmazonProspects = await prisma.amazonProspect.findMany({
      where: {
        OR: [
          { email: { in: uniqueEmails } },
          { person_linkedin: { in: uniqueLinkedIns } },
        ],
      },
      select: {
        email: true,
        person_linkedin: true,
      },
    });

    // Create lookup maps
    const existingEmails = new Set(
      existingAmazonProspects.map((p) => p.email).filter(Boolean)
    );
    const existingLinkedIns = new Set(
      existingAmazonProspects.map((p) => p.person_linkedin).filter(Boolean)
    );

    // Filter new prospects
    const newProspects = prospects.filter((prospect) => {
      return !(
        (prospect.email && existingEmails.has(prospect.email)) ||
        (prospect.person_linkedin &&
          existingLinkedIns.has(prospect.person_linkedin))
      );
    });

    if (newProspects.length > 0) {
      // Create new amazon prospects in batches
      for (let i = 0; i < newProspects.length; i += MAX_TRANSACTION_SIZE) {
        const batch = newProspects.slice(i, i + MAX_TRANSACTION_SIZE);
        const createData = batch.map((prospect) => ({
          person_name: prospect.person_name,
          person_linkedin: prospect.person_linkedin,
          email: prospect.email,
          job_title: prospect.job_title,
          source: prospect.source,
          sources: prospect.sources,
          email_status: prospect.email_status,
          seller_group_id: prospect.seller_group_id,
        }));

        const created = await prisma.amazonProspect.createMany({
          data: createData,
          skipDuplicates: true,
        });

        totalCreated += created.count;
      }
    }

    totalProcessed += prospects.length;
    totalSkipped += prospects.length - newProspects.length;

    console.log(
      `Processed ${totalProcessed} prospects, Created ${totalCreated} new amazon prospects, Skipped ${totalSkipped} existing prospects`
    );
    skip += BATCH_SIZE;
  }
}

// Helper function to map company data to seller data
function mapCompanyToSellerData(company, marketplace, sellerGroupId) {
  return {
    name: company.name || null,
    amazon_seller_id: company.amazon_seller_id || null,
    marketplace: marketplace || null,
    seller_group_id: sellerGroupId,
    primary_category: company.primary_category || null,
    primary_sub_category: company.primary_sub_category || null,
    estimate_sales: convertToFloat(company.estimate_sales),
    avg_price: convertToFloat(company.avg_price),
    percent_fba: convertToFloat(company.percent_fba),
    number_reviews_lifetime: convertToInt(company.number_reviews_lifetime),
    number_reviews_30days: convertToInt(company.number_reviews_30days),
    number_winning_brands: convertToInt(company.number_winning_brands),
    number_asins: convertToInt(company.number_asins),
    number_top_asins: convertToInt(company.number_top_asins),
    business_name: company.business_name || null,
    number_brands_1000: convertToInt(company.number_brands_1000),
    mom_growth: convertToFloat(company.mom_growth),
    mom_growth_count: convertToInt(company.mom_growth_count),
    started_selling_date: convertToDate(company.started_selling_date),
    website: company.website || null,
    domain: company.domain || null,
    website_status: company.website_status || null,
    lookup_source: company.lookup_source || null,
    lookup_sources: company.lookup_sources || {},
    street: company.company_address || null,
    city: company.company_location || null,
    adr_state: company.state || null,
    adr_country: company.country || null,
    adr_zip_code: company.company_pincode || null,
  };
}

// Main function to run both migrations
async function migrateNewRecords() {
  try {
    console.log("Starting migration of new records...");

    // First migrate companies to sellers
    await migrateNewCompanies();

    // Then migrate prospects to amazon prospects
    await migrateNewProspects();

    console.log("Migration of new records completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateNewRecords()
    .then(() => console.log("Migration completed."))
    .catch((err) => {
      console.error("Migration failed:", err);
      process.exit(1);
    });
}

// Export functions for testing or use in other modules
module.exports = {
  migrateNewRecords,
  migrateNewCompanies,
  migrateNewProspects,
};
