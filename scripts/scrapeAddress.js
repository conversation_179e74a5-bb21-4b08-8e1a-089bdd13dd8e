const cheerio = require("cheerio");
const { getHtmlByProxy } = require("../utils/getHtmlByProxy");
const { createKeyFromAmazonUrl } = require("../utils/s3");
const { removeSpecialCharacters } = require("../utils/removeSpecialCharacters");

async function scrapeAddress(url) {
  try {
    // Fetch the HTML content
    const sellerPageHtml = await getHtmlByProxy(
      url,
      1,
      2,
      true,
      24 * 30,
      createKeyFromAmazonUrl(url)
    );
    const $ = cheerio.load(sellerPageHtml);

    // Extract business address using the same selectors from processSellerPage.js
    const businessAddress = [];
    $('.a-text-bold:contains("Business Address:")')
      .parent()
      .nextAll(".indent-left")
      .each(function () {
        businessAddress.push($(this).text().trim());
      });

    // Get pincode from the second-to-last element
    const sellerPincode = businessAddress[businessAddress.length - 2];
    const sellerContactNumbers = [];
    const phoneRegex =
      /(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})/g;
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;

    // Check entire page for phone numbers and emails
    const pageText = $("body").text();
    const matches = pageText?.match(phoneRegex);
    // if (matches) {
    //   matches.forEach((match) => {
    //     // Push original number
    //     sellerContactNumbers.push(removeSpecialCharacters(match));
    //     // Push number without country code
    //     const numberWithoutCountry = removeSpecialCharacters(
    //       match.replace(/^\+?\d{1,3}/, "")
    //     );
    //     if (numberWithoutCountry.length >= 10) {
    //       sellerContactNumbers.push(numberWithoutCountry);
    //     }
    //   });
    // }
    const sellerEmails = [];
    const emailMatches = pageText?.match(emailRegex);
    if (emailMatches) {
      emailMatches.forEach((email) => {
        sellerEmails.push(email);
      });
    }

    // Also check specific seller contact section
    $("#seller-contact-phone").each(function () {
      const contactNumber = $(this).text().trim();
      if (contactNumber) {
        const normalizedNumber = removeSpecialCharacters(contactNumber);
        sellerContactNumbers.push(normalizedNumber);

        const numberWithoutCountry = normalizedNumber.replace(
          /^\+?\d{1,3}/,
          ""
        );

        if (numberWithoutCountry.length >= 10) {
          sellerContactNumbers.push(numberWithoutCountry);
        }
      }
    });
    // Remove duplicates using Set
    const uniqueSellerContactNumbers = [...new Set(sellerContactNumbers)];
    const uniqueSellerEmails = [...new Set(sellerEmails)];

    return {
      address: businessAddress.join(", "),
      numbers: uniqueSellerContactNumbers,
      pinCode: sellerPincode || "",
      emails: uniqueSellerEmails,
    };
  } catch (error) {
    console.error("Error scraping address:", error);
    return {
      address: "",
      pincode: "",
      numbers: [],
      emails: [],
    };
  }
}

// Example usage:
async function main() {
  const url = process.argv[2]; // Get URL from command line argument
  if (!url) {
    console.log("Please provide a URL as an argument");
    process.exit(1);
  }

  const result = await scrapeAddress(url);
  console.log("Scraped Address:", result);
}

if (require.main === module) {
  main();
}

// const sellerContactNumbers = [];
// const phoneRegex =
//   /(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})/g;

// // Check entire page for phone numbers
// const pageText = `+91-7976865251 ******-220-6436`;
// const matches = pageText.match(phoneRegex);
// if (matches) {
//   matches.forEach((match) => {
//     // Remove country code if present and special characters
//     const number = removeSpecialCharacters(match.replace(/^\+?\d{1,3}/, ""));
//     if (number.length >= 10) {
//       sellerContactNumbers.push(number);
//     }
//   });
// }

// // Also check specific seller contact section

// // Remove duplicates using Set
// console.log([...new Set(sellerContactNumbers)]);

module.exports = scrapeAddress;
