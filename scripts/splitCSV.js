const fs = require('fs');
const path = require('path');

/**
 * Split CSV file into smaller chunks
 * @param {string} inputFile - Path to the input CSV file
 * @param {number} chunkSize - Number of rows per chunk (excluding header)
 * @param {string} outputDir - Directory to save the split files
 */
function splitCSV(inputFile, chunkSize = 1500, outputDir = 'output') {
    try {
        // Read the entire file
        const data = fs.readFileSync(inputFile, 'utf8');
        const lines = data.split('\n');
        
        // Get the header row
        const header = lines[0];
        
        // Remove header from data lines
        const dataLines = lines.slice(1).filter(line => line.trim() !== '');
        
        console.log(`Total data rows: ${dataLines.length}`);
        console.log(`Chunk size: ${chunkSize} rows`);
        console.log(`Number of chunks: ${Math.ceil(dataLines.length / chunkSize)}`);
        
        // Create output directory if it doesn't exist
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        // Split into chunks
        const chunks = [];
        for (let i = 0; i < dataLines.length; i += chunkSize) {
            chunks.push(dataLines.slice(i, i + chunkSize));
        }
        
        // Write each chunk to a separate file
        chunks.forEach((chunk, index) => {
            const chunkNumber = index + 1;
            const fileName = `chunk_${chunkNumber.toString().padStart(2, '0')}.csv`;
            const filePath = path.join(outputDir, fileName);
            
            // Combine header with chunk data
            const chunkContent = [header, ...chunk].join('\n');
            
            fs.writeFileSync(filePath, chunkContent);
            console.log(`Created ${fileName} with ${chunk.length} rows`);
        });
        
        console.log(`\nSplit complete! Files saved in '${outputDir}' directory`);
        
    } catch (error) {
        console.error('Error splitting CSV:', error.message);
    }
}

// Main execution
if (require.main === module) {
    const inputFile = process.argv[2] || '14JUL_email_Harv_Input_Websites - Sheet1.csv';
    const chunkSize = parseInt(process.argv[3]) || 1500;
    const outputDir = process.argv[4] || 'output';
    
    console.log(`Splitting ${inputFile} into chunks of ${chunkSize} rows...`);
    splitCSV(inputFile, chunkSize, outputDir);
}

module.exports = splitCSV; 