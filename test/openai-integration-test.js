/**
 * OpenAI Integration Test
 * 
 * This test validates the OpenAI website finding functionality
 * including CSV processing, API integration, and result generation.
 */

const fs = require('fs');
const path = require('path');
const { processLeadGeneration } = require('../services/generateLeads/createJob');
const { processOpenAIJob, getOpenAIWorkerStats } = require('../services/generateLeads/workers/openaiWorker/openaiWorker');
const { makeOpenAIRequest, DEFAULT_PROMPT } = require('../services/generateLeads/OpenAI/getOpenAIRequest');
const prisma = require('../database/prisma/getPrismaClient');

// Test data
const testSellerData = {
  sellerName: "Test Seller Inc",
  businessName: "Test Business Solutions",
  address: "123 Test Street, Test City, TC 12345",
  sellerId: "TEST123",
  sellerUrl: "https://amazon.com/seller/test123"
};

const testCsvData = `Seller Name,Business Name,Address,Seller ID,Seller URL,Search Priority
Test Seller Inc,Test Business Solutions,"123 Test Street, Test City, TC 12345",TEST123,https://amazon.com/seller/test123,SP1
Another Seller,Another Business,"456 Another St, Another City, AC 67890",TEST456,https://amazon.com/seller/test456,SP2
Third Seller,Third Business,"789 Third Ave, Third City, TC 11111",TEST789,https://amazon.com/seller/test789,SP3`;

/**
 * Create a test CSV file
 */
function createTestCsvFile() {
  const testDir = path.join(__dirname, 'temp');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  const csvPath = path.join(testDir, 'test-sellers.csv');
  fs.writeFileSync(csvPath, testCsvData);
  return csvPath;
}

/**
 * Clean up test files
 */
function cleanupTestFiles(csvPath) {
  if (fs.existsSync(csvPath)) {
    fs.unlinkSync(csvPath);
  }
}

/**
 * Test OpenAI request functionality
 */
async function testOpenAIRequest() {
  console.log('🧪 Testing OpenAI request functionality...');
  
  try {
    // Test with default prompt
    console.log('  - Testing with default prompt...');
    const result = await makeOpenAIRequest(testSellerData);
    
    console.log('  ✅ OpenAI request successful');
    console.log(`  📊 Input tokens: ${result.inputTokens}`);
    console.log(`  📊 Output tokens: ${result.outputTokens}`);
    console.log(`  💰 Total cost: $${result.totalCost.toFixed(4)}`);
    console.log(`  🎯 Success: ${result.response.success}`);
    console.log(`  🌐 Websites found: ${result.response.websites?.length || 0}`);
    
    return result;
  } catch (error) {
    console.error('  ❌ OpenAI request failed:', error.message);
    throw error;
  }
}

/**
 * Test CSV processing and job creation
 */
async function testCsvProcessing() {
  console.log('🧪 Testing CSV processing and job creation...');
  
  const csvPath = createTestCsvFile();
  
  try {
    console.log('  - Creating OpenAI job from CSV...');
    const job = await processLeadGeneration(
      csvPath,
      false, // useDomain
      "openai", // mode
      undefined,
      "test-openai-job.csv",
      "original" // searchPattern
    );
    
    console.log(`  ✅ Job created successfully with ID: ${job.id}`);
    
    // Verify leads were created
    const leads = await prisma.lead.findMany({
      where: { jobId: job.id }
    });
    
    console.log(`  📊 Created ${leads.length} leads`);
    
    // Verify job details
    const jobDetails = await prisma.leadJob.findUnique({
      where: { id: job.id },
      include: { leads: true }
    });
    
    console.log(`  📋 Job status: ${jobDetails.status}`);
    console.log(`  📋 Job mode: ${jobDetails.mode}`);
    
    return { job, leads };
  } catch (error) {
    console.error('  ❌ CSV processing failed:', error.message);
    throw error;
  } finally {
    cleanupTestFiles(csvPath);
  }
}

/**
 * Test OpenAI worker processing
 */
async function testWorkerProcessing(jobId) {
  console.log('🧪 Testing OpenAI worker processing...');
  
  try {
    console.log(`  - Processing job ${jobId}...`);
    const result = await processOpenAIJob(jobId);
    
    if (result) {
      console.log(`  ✅ Worker processing completed`);
      console.log(`  📊 Processed ${result.processedLeads} leads`);
      
      // Check usage data
      const usage = await prisma.openAI_Usage.findMany({
        where: { jobId: jobId }
      });
      
      console.log(`  📊 Usage records created: ${usage.length}`);
      
      const totalCost = usage.reduce((sum, u) => sum + u.totalCost, 0);
      const successCount = usage.filter(u => u.success).length;
      
      console.log(`  💰 Total cost: $${totalCost.toFixed(4)}`);
      console.log(`  🎯 Success rate: ${successCount}/${usage.length}`);
      
      return result;
    } else {
      console.log('  ⚠️ No leads to process');
      return null;
    }
  } catch (error) {
    console.error('  ❌ Worker processing failed:', error.message);
    throw error;
  }
}

/**
 * Test statistics functionality
 */
async function testStatistics() {
  console.log('🧪 Testing statistics functionality...');
  
  try {
    const stats = await getOpenAIWorkerStats();
    
    console.log('  ✅ Statistics retrieved successfully');
    console.log(`  📊 Total requests: ${stats.totalRequests}`);
    console.log(`  📊 Total input tokens: ${stats.totalInputTokens}`);
    console.log(`  📊 Total output tokens: ${stats.totalOutputTokens}`);
    console.log(`  💰 Total cost: $${stats.totalCost.toFixed(4)}`);
    console.log(`  💰 Average cost: $${stats.averageCost.toFixed(4)}`);
    console.log(`  🎯 Success rate: ${stats.successRate.toFixed(2)}%`);
    
    return stats;
  } catch (error) {
    console.error('  ❌ Statistics test failed:', error.message);
    throw error;
  }
}

/**
 * Test custom prompt functionality
 */
async function testCustomPrompt() {
  console.log('🧪 Testing custom prompt functionality...');
  
  const customPrompt = `You are a website finder. Find the official website for:
Company: {{sellerName}}
Business: {{businessName}}
Address: {{address}}

Return JSON: {"websites": ["site1.com"], "success": true, "reasoning": "Found official site"}`;

  try {
    console.log('  - Testing with custom prompt...');
    const result = await makeOpenAIRequest(testSellerData, customPrompt);
    
    console.log('  ✅ Custom prompt request successful');
    console.log(`  🎯 Success: ${result.response.success}`);
    console.log(`  🌐 Websites found: ${result.response.websites?.length || 0}`);
    
    return result;
  } catch (error) {
    console.error('  ❌ Custom prompt test failed:', error.message);
    throw error;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting OpenAI Integration Tests\n');
  
  try {
    // Test 1: Basic OpenAI request
    await testOpenAIRequest();
    console.log('');
    
    // Test 2: Custom prompt
    await testCustomPrompt();
    console.log('');
    
    // Test 3: CSV processing
    const { job } = await testCsvProcessing();
    console.log('');
    
    // Test 4: Worker processing
    await testWorkerProcessing(job.id);
    console.log('');
    
    // Test 5: Statistics
    await testStatistics();
    console.log('');
    
    console.log('🎉 All tests completed successfully!');
    
    // Clean up test job
    console.log('🧹 Cleaning up test data...');
    await prisma.openAI_Usage.deleteMany({
      where: { jobId: job.id }
    });
    await prisma.lead.deleteMany({
      where: { jobId: job.id }
    });
    await prisma.leadJob.delete({
      where: { id: job.id }
    });
    console.log('✅ Cleanup completed');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testOpenAIRequest,
  testCsvProcessing,
  testWorkerProcessing,
  testStatistics,
  testCustomPrompt,
  runAllTests
};
