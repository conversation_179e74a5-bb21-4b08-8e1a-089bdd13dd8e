const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const { adminAuth } = require("../middlewares/jwt");
const csvQueueManager = require("../utils/csvQueueManagement");
const csvScheduler = require("../services/csvProcessor/csvProcessingScheduler");
const { getProcessingSummary } = require("../services/csvProcessor/csvFileProcessor");
const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/");
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({ storage: storage });

/**
 * @swagger
 * /api/leadqueue/upload:
 *   post:
 *     summary: Upload CSV file to processing queue
 *     tags: [Lead Queue]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: CSV file to upload
 *               processType:
 *                 type: string
 *                 enum: [amazon_seller, amazon_prospect]
 *                 description: Type of data processing
 *               skipErrorRows:
 *                 type: string
 *                 enum: [true, false]
 *                 description: Whether to skip rows with errors
 *               allowBlankRows:
 *                 type: string
 *                 enum: [true, false]
 *                 description: Whether to allow blank rows
 *               source:
 *                 type: string
 *                 description: Source identifier for the data
 *     responses:
 *       200:
 *         description: CSV uploaded to queue successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CSV uploaded to queue successfully"
 *                 csvDataId:
 *                   type: integer
 *                   example: 123
 *                 queuePosition:
 *                   type: integer
 *                   example: 1
 *                 totalRows:
 *                   type: integer
 *                   example: 1000
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/upload", upload.single("csvFile"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No CSV file provided" });
    }
    console.log(req.file);

    const { processType, source, skipErrorRows, allowBlankRows, ...otherOptions } = req.body;

    if (!processType || !["amazon_prospect", "amazon_seller"].includes(processType)) {
      return res.status(400).json({ error: "Invalid or missing process type" });
    }

    const fileInfo = {
      filePath: req.file.path,
      originalName: req.file.originalname,
    };

    // Prepare options object
    const options = {
      skipErrorRows: skipErrorRows === "true" || skipErrorRows === true,
      allowBlankRows: allowBlankRows === "true" || allowBlankRows === true,
      ...otherOptions
    };

    const csvData = await csvQueueManager.addToQueue(
      fileInfo,
      processType,
      req.user?.email || "unknown",
      source || null,
      options
    );

    const queueStats = await csvScheduler.getQueueStats();
    const queuePosition = queueStats.statusCounts.PENDING || 0;

    res.json({
      success: true,
      message: "CSV uploaded to queue successfully",
      csvDataId: csvData.id,
      queuePosition: queuePosition,
      totalRows: csvData.total_rows,
    });

  } catch (error) {
    console.error("Error uploading CSV to queue:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue:
 *   get:
 *     summary: Get list of queued CSV files
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, PROCESSING, COMPLETED, FAILED, PAUSED]
 *         description: Filter by status
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [amazon_seller, amazon_prospect]
 *         description: Filter by process type
 *     responses:
 *       200:
 *         description: List of queued CSV files
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/CsvData'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue", async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      type
    } = req.query;

    console.log("Queue request params:", { page, limit, status, type });

    const filters = {};
    if (status) filters.status = status;
    if (type) filters.process_type = type;

    console.log("Applied filters:", filters);

    const result = await csvQueueManager.getQueueList({
      page: parseInt(page),
      limit: parseInt(limit),
      status: filters.status,
      processType: filters.process_type
    });

    console.log(`Returning ${result.data.length} CSV files out of ${result.pagination.total} total`);

    res.json(result);
  } catch (error) {
    console.error("Error fetching queue:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/scheduler/status:
 *   get:
 *     summary: Get scheduler status
 *     tags: [Lead Queue]
 *     responses:
 *       200:
 *         description: Scheduler status information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     isRunning:
 *                       type: boolean
 *                     currentJob:
 *                       type: object
 *                       nullable: true
 *                     queueStats:
 *                       type: object
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/scheduler/status" , async (req, res) => {
  try {
    const schedulerStatus = csvScheduler.getStatus();
    const queueStats = await csvScheduler.getQueueStats();

    res.json({
      success: true,
      data: {
        ...schedulerStatus,
        queueStats
      }
    });
  } catch (error) {
    console.error("Error fetching scheduler status:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/scheduler/start:
 *   post:
 *     summary: Start the CSV processing scheduler
 *     tags: [Lead Queue]
 *     responses:
 *       200:
 *         description: Scheduler started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Scheduler started successfully"
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/scheduler/start" , async (req, res) => {
  try {
    csvScheduler.start();

    res.json({
      success: true,
      message: "Scheduler started successfully"
    });
  } catch (error) {
    console.error("Error starting scheduler:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/scheduler/stop:
 *   post:
 *     summary: Stop the CSV processing scheduler
 *     tags: [Lead Queue]
 *     responses:
 *       200:
 *         description: Scheduler stopped successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Scheduler stopped successfully"
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/scheduler/stop" , async (req, res) => {
  try {
    csvScheduler.stop();

    res.json({
      success: true,
      message: "Scheduler stopped successfully"
    });
  } catch (error) {
    console.error("Error stopping scheduler:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/status:
 *   get:
 *     summary: Get processing status for a specific CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: Processing status information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalRows:
 *                       type: integer
 *                     processedRows:
 *                       type: integer
 *                     errorRows:
 *                       type: integer
 *                     pendingRows:
 *                       type: integer
 *                     progressPercentage:
 *                       type: number
 *                     successRate:
 *                       type: number
 *                     status:
 *                       type: string
 *                     isCompleted:
 *                       type: boolean
 *                     errorStatus:
 *                       type: boolean
 *                     errorMessage:
 *                       type: string
 *                     processedUntil:
 *                       type: integer
 *                     retryCount:
 *                       type: integer
 *                     maxRetries:
 *                       type: integer
 *                     lastRetryAt:
 *                       type: string
 *                       format: date-time
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/:csvDataId/status", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const stats = await csvQueueManager.getProcessingStats(csvDataId);

    if (!stats) {
      return res.status(404).json({ error: "CSV data not found" });
    }

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("Error fetching CSV status:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/statistics:
 *   get:
 *     summary: Get detailed statistics for a specific CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: Detailed CSV statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     csvInfo:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         fileName:
 *                           type: string
 *                         processType:
 *                           type: string
 *                         status:
 *                           type: string
 *                         totalRows:
 *                           type: integer
 *                     statistics:
 *                       type: object
 *                       properties:
 *                         totalRows:
 *                           type: integer
 *                         processedRows:
 *                           type: integer
 *                         errorRows:
 *                           type: integer
 *                         pendingRows:
 *                           type: integer
 *                         progressPercentage:
 *                           type: number
 *                         successRate:
 *                           type: number
 *                     uniqueErrors:
 *                       type: array
 *                       items:
 *                         type: string
 *                     errorCount:
 *                       type: integer
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/:csvDataId/statistics", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    
    // Get CSV data and statistics in parallel
    const [csvData, stats, uniqueErrors] = await Promise.all([
      prisma.csvData.findUnique({
        where: { id: csvDataId },
        select: {
          id: true,
          file_name: true,
          process_type: true,
          status: true,
          total_rows: true,
          createdAt: true,
          updatedAt: true,
        }
      }),
      csvQueueManager.getProcessingStats(csvDataId),
      csvQueueManager.getUniqueErrors(csvDataId)
    ]);

    if (!csvData || !stats) {
      return res.status(404).json({ error: "CSV data not found" });
    }

    res.json({
      success: true,
      data: {
        csvInfo: {
          id: csvData.id,
          fileName: csvData.file_name,
          processType: csvData.process_type,
          status: csvData.status,
          totalRows: csvData.total_rows,
          createdAt: csvData.createdAt,
          updatedAt: csvData.updatedAt,
        },
        statistics: stats,
        uniqueErrors: uniqueErrors,
        errorCount: uniqueErrors.length,
      }
    });
  } catch (error) {
    console.error("Error fetching CSV statistics:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/process:
 *   post:
 *     summary: Manually process a specific CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: Processing started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CSV processing started"
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/:csvDataId/process", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const result = await csvScheduler.processSpecificCSV(csvDataId);

    if (result.success) {
      res.json({
        success: true,
        message: "CSV processing started successfully"
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error("Error processing CSV:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/retry:
 *   post:
 *     summary: Retry processing failed rows for a CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *       - in: query
 *         name: maxRetries
 *         schema:
 *           type: integer
 *           default: 3
 *         description: Maximum number of retries
 *     responses:
 *       200:
 *         description: Retry operation completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Retry operation completed"
 *                 data:
 *                   type: object
 *                   properties:
 *                     retriedRows:
 *                       type: integer
 *                       description: Number of rows reset for retry
 *                     totalFailedRows:
 *                       type: integer
 *                       description: Total number of failed rows
 *                     csvInfo:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         fileName:
 *                           type: string
 *                         status:
 *                           type: string
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/:csvDataId/retry", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const maxRetries = parseInt(req.query.maxRetries) || 3;

    const result = await csvQueueManager.retryFailedRows(csvDataId, maxRetries);

    res.json({
      success: true,
      message: result.message || "Retry operation completed",
      data: result
    });
  } catch (error) {
    console.error("Error retrying CSV:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/reset:
 *   post:
 *     summary: Reset CSV data and data logs for complete reprocessing
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: CSV reset for reprocessing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully reset CSV for reprocessing"
 *                 data:
 *                   type: object
 *                   properties:
 *                     csvId:
 *                       type: integer
 *                       description: CSV data ID
 *                     fileName:
 *                       type: string
 *                       description: CSV file name
 *                     resetLogsCount:
 *                       type: integer
 *                       description: Number of data log rows reset
 *                     previousStatus:
 *                       type: string
 *                       description: Previous CSV status
 *                     newStatus:
 *                       type: string
 *                       description: New CSV status (PENDING)
 *                     totalRows:
 *                       type: integer
 *                       description: Total number of rows in CSV
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/:csvDataId/reset", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);

    const result = await csvQueueManager.resetCsvForReprocessing(csvDataId);

    res.json({
      success: true,
      message: result.message || "CSV reset for reprocessing",
      data: result.data
    });
  } catch (error) {
    console.error("Error resetting CSV:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}:
 *   delete:
 *     summary: Delete a CSV from the queue
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: CSV deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CSV deleted successfully"
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.delete("/api/leadqueue/:csvDataId", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    
    const result = await csvQueueManager.deleteFromQueue(csvDataId);

    res.json({
      success: true,
      message: result.message || "CSV deleted successfully",
      data: result
    });
  } catch (error) {
    console.error("Error deleting CSV:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/bulk-delete:
 *   post:
 *     summary: Bulk delete CSVs from the queue
 *     tags: [Lead Queue]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of CSV data IDs to delete
 *     responses:
 *       200:
 *         description: Bulk delete summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     deleted:
 *                       type: integer
 *                     failed:
 *                       type: integer
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           error:
 *                             type: string
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/bulk-delete", async (req, res) => {
  try {
    const { ids } = req.body || {};

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, error: "'ids' (non-empty array) is required" });
    }

    let deleted = 0;
    const errors = [];
    for (const id of ids) {
        const csvDataId = parseInt(id);
        if (Number.isNaN(csvDataId)) {
          errors.push({ id, error: "Invalid id" });
          continue;
        }
        try {
          const result = await csvQueueManager.deleteFromQueue(csvDataId);
          deleted += 1;
          console.log(`Bulk delete: ${result.message}`);
        } catch (e) {
          errors.push({ id: csvDataId, error: e?.message || "Delete failed" });
        }
      }

    const failed = errors.length;

    return res.json({
      success: true,
      message: `Bulk delete completed. Deleted: ${deleted}, Failed: ${failed}`,
      data: { deleted, failed, errors }
    });
  } catch (error) {
    console.error("Error bulk deleting CSVs:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/pause:
 *   post:
 *     summary: Pause processing for a specific CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: CSV processing paused
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CSV processing paused"
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/:csvDataId/pause", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    await csvQueueManager.pauseProcessing(csvDataId);

    res.json({
      success: true,
      message: "CSV processing paused"
    });
  } catch (error) {
    console.error("Error pausing CSV:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/resume:
 *   post:
 *     summary: Resume processing for a specific CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *     responses:
 *       200:
 *         description: CSV processing resumed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "CSV processing resumed"
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.post("/api/leadqueue/:csvDataId/resume", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    await csvQueueManager.resumeProcessing(csvDataId);

    res.json({
      success: true,
      message: "CSV processing resumed"
    });
  } catch (error) {
    console.error("Error resuming CSV:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/data:
 *   get:
 *     summary: Get processed data for a CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Processed data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/:csvDataId/data", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 100;

    const result = await csvQueueManager.getProcessedData(csvDataId, page, limit);

    res.json(result);
  } catch (error) {
    console.error("Error fetching processed data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/errors:
 *   get:
 *     summary: Get error rows for a CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Error rows
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/:csvDataId/errors", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 100;

    const result = await csvQueueManager.getErrorRows(csvDataId, page, limit);

    res.json(result);
  } catch (error) {
    console.error("Error fetching error rows:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/table-data:
 *   get:
 *     summary: Get paginated table data for a CSV with transformed data and errors
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter data
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, processed, errors]
 *           default: all
 *         description: Type of data to show
 *     responses:
 *       200:
 *         description: Paginated table data with transformed information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     csvInfo:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         fileName:
 *                           type: string
 *                         processType:
 *                           type: string
 *                         status:
 *                           type: string
 *                         totalRows:
 *                           type: integer
 *                     tableData:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           rowNumber:
 *                             type: integer
 *                           processed:
 *                             type: boolean
 *                           errorStatus:
 *                             type: boolean
 *                           errorMessage:
 *                             type: string
 *                           originalData:
 *                             type: object
 *                           transformedData:
 *                             type: object
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalRows:
 *                           type: integer
 *                         processedRows:
 *                           type: integer
 *                         errorRows:
 *                           type: integer
 *                         processedCount:
 *                           type: integer
 *                         errorCount:
 *                           type: integer
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/:csvDataId/table-data", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || "";
    const type = req.query.type || "all";

    const result = await csvQueueManager.getTableData(csvDataId, page, limit, search, type);

    res.json(result);
  } catch (error) {
    console.error("Error fetching table data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/leadqueue/{csvDataId}/export:
 *   get:
 *     summary: Export processed data or errors for a CSV
 *     tags: [Lead Queue]
 *     parameters:
 *       - in: path
 *         name: csvDataId
 *         required: true
 *         schema:
 *           type: integer
 *         description: CSV data ID
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, json]
 *           default: csv
 *         description: Export format
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [processed, errors]
 *           default: processed
 *         description: Type of data to export
 *     responses:
 *       200:
 *         description: Export file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: CSV data not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/leadqueue/:csvDataId/export", async (req, res) => {
  try {
    const csvDataId = parseInt(req.params.csvDataId);
    const format = req.query.format || 'csv';
    const type = req.query.type || 'processed';

    const result = await csvQueueManager.exportData(csvDataId, format, type);

    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${type}-${csvDataId}.csv"`);
      res.send(result);
    } else {
      res.json(result);
    }
  } catch (error) {
    console.error("Error exporting data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
