const express = require('express');
const router = express.Router();
const MXRecordChecker = require('../services/MXRecordChecker');
const mxScheduler = require('../services/MXRecordChecker/scheduler');

/**
 * MX Record API Routes
 * 
 * This service processes email domains from Prospect and AmazonProspect tables
 * to determine email providers and store MX record information.
 * 
 * Updated to work with prospect tables instead of Email table.
 */

/**
 * Get MX record processing statistics
 */
router.get('/stats', async (req, res) => {
  try {
    const checker = new MXRecordChecker();
    const stats = await checker.getProcessingStats();
    
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting MX record stats:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get scheduler status
 */
router.get('/scheduler/status', async (req, res) => {
  try {
    const status = mxScheduler.getStatus();
    
    // Add helpful messages based on status
    let message = '';
    if (!status.enabled) {
      message = `Scheduler is disabled (NODE_ENV: ${status.nodeEnv}). Set NODE_ENV to production, staging, or development to enable.`;
    } else if (status.isScheduled) {
      message = 'Scheduler is active and running as scheduled.';
    } else {
      message = 'Scheduler is enabled but not currently scheduled.';
    }
    
    res.json({
      success: true,
      data: status,
      message,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting scheduler status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Start the MX record scheduler
 */
router.post('/scheduler/start', async (req, res) => {
  try {
    mxScheduler.start();
    
    res.json({
      success: true,
      message: 'MX Record scheduler started',
      data: mxScheduler.getStatus(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error starting scheduler:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Stop the MX record scheduler
 */
router.post('/scheduler/stop', async (req, res) => {
  try {
    mxScheduler.stop();
    
    res.json({
      success: true,
      message: 'MX Record scheduler stopped',
      data: mxScheduler.getStatus(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error stopping scheduler:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Run MX record check immediately for all prospects
 */
router.post('/run', async (req, res) => {
  try {
    // Don't await this as it might take a long time
    mxScheduler.runNow().catch(error => {
      console.error('Error in manual MX record run:', error);
    });
    
    res.json({
      success: true,
      message: 'MX Record check started for all prospects',
      data: mxScheduler.getStatus(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error starting manual MX record run:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Health check endpoint
 */
router.get('/health', async (req, res) => {
  try {
    const health = await mxScheduler.healthCheck();
    
    res.status(health.healthy ? 200 : 503).json({
      success: health.healthy,
      data: health,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in health check:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get prospect records that need MX processing
 */
router.get('/pending', async (req, res) => {
  try {
    const checker = new MXRecordChecker();
    const prospects = await checker.getRecordsToProcess();
    
    const summary = {
      totalProspects: prospects.length,
      uniqueDomains: new Set(prospects.map(p => p.domain).filter(Boolean)).size,
      byTableType: prospects.reduce((acc, p) => {
        acc[p.tableType] = (acc[p.tableType] || 0) + 1;
        return acc;
      }, {})
    };
    
    res.json({
      success: true,
      data: {
        summary,
        prospects: req.query.details === 'true' ? prospects : undefined
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting pending prospect records:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Process a specific domain manually
 */
router.post('/domain/:domain', async (req, res) => {
  try {
    const { domain } = req.params;
    const checker = new MXRecordChecker();
    
    // Validate domain format
    if (!domain || !/^[a-zA-Z0-9][a-zA-Z0-9-_.]+\.[a-zA-Z]{2,}$/.test(domain)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid domain format'
      });
    }
    
    // Query MX records for the domain
    const dnsResult = await checker.queryMXRecords(domain);
    
    if (dnsResult.Status !== 0) {
      return res.status(404).json({
        success: false,
        error: `DNS query failed for ${domain}: Status ${dnsResult.Status}`
      });
    }
    
    const mxAnswers = dnsResult.Answer ? dnsResult.Answer.filter(a => a.type === 15) : [];
    const mxRecords = mxAnswers.map(record => ({
      priority: record.data ? parseInt(record.data.split(' ')[0]) : 0,
      exchange: record.data ? record.data.split(' ')[1] : ''
    }));
    
    const { provider, records } = checker.processMXRecords(mxRecords);
    
    res.json({
      success: true,
      data: {
        domain,
        provider,
        mxRecords: records,
        rawDnsResult: dnsResult
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error processing domain:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router; 