const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient"); // Adjust the path as necessary
const { adminAuth } = require("../middlewares/jwt");
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const { processCSV } = require("../utils/csvParser");
const converter = require("json-2-csv");

/**
 * @swagger
 * /api/jobs/{clientId}:
 *   get:
 *     summary: Get all jobs for a specific user
 *     tags: [Jobs(Email Harvesting From Seller Page)]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: The client ID
 *     responses:
 *       200:
 *         description: List of jobs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 1
 *                   fileName:
 *                     type: string
 *                     example: "seller_data.csv"
 *                   csvStatus:
 *                     type: string
 *                     example: "completed"
 *                   qualifiedLeads:
 *                     type: integer
 *                     example: 15
 *                   totalLeads:
 *                     type: integer
 *                     example: 25
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T14:30:00Z"
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T15:45:00Z"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get("/api/jobs/:clientId", adminAuth, async (req, res) => {
  try {
    const jobs = await prisma.job.findMany({
      where: { userId: parseInt(req.params.clientId) },
      include: {
        OutputData: true,
      },
    });
    // Send all data to the client
    const response = jobs.map((job) => ({
      id: job.id,
      fileName: job.name,
      csvStatus: job.status,
      qualifiedLeads: job.OutputData.filter(
        (data) => data.mailData && data.mailData.length > 0
      ).length,
      totalLeads: job.OutputData.length,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
    }));

    res.status(200).json(response);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching jobs:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/jobs/download-input-csv/{jobId}:
 *   get:
 *     summary: Download input CSV file for a specific job
 *     tags: [Jobs(Email Harvesting From Seller Page)]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *     responses:
 *       200:
 *         description: CSV file containing the original input data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               "sellerDetails.name","sellerDetails.website","sellerDetails.amazon_seller_id"
 *               "Amazon Seller 1","https://example1.com","A1B2C3D4E5"
 *               "Amazon Seller 2","https://example2.com","F6G7H8I9J0"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="input_data_42.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Job not found"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.get(
  "/api/jobs/download-input-csv/:jobId",
  adminAuth,
  async (req, res) => {
    try {
      const jobId = parseInt(req.params.jobId);
      const job = await prisma.job.findUnique({ where: { id: jobId } });
      if (!job) {
        return res.status(404).json({ error: "Job not found" });
      }
      // Check if the user is authorized to access this job
      // if (job.clientId !== req.user.userId && req.user.userType !== "admin") {
      //   return res.status(403).json({ error: "Unauthorized" });
      // };
      const inputCSV = await prisma.outputData.findMany({
        where: {
          jobId,
        },
        select: {
          sellerDetails: true,
        },
      });

      const csvContent = json2csv(inputCSV);

      // Set the filename for the CSV file
      const inputCsvFileName = `input_data_${job.id}.csv`;

      // Send the CSV file in response
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${inputCsvFileName}`
      );
      res.setHeader("Content-Type", "text/csv");
      res.status(200).send(csvContent);
    } catch (error) {
      console.error("Error fetching output data:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/jobs/{clientId}:
 *   post:
 *     summary: Create a new job by uploading CSV file or processing single company data
 *     tags: [Jobs(Email Harvesting From Seller Page)]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: The client ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file with the following structure:
 *                   - "Name" or "Seller Name": Name of the seller (required)
 *                   - "Seller URL" or "seller_url": URL of the seller's website
 *                   - "Amazon Seller ID" or "amazon_seller_id": Amazon seller ID
 *
 *                   Example CSV:
 *                   Name,Seller URL,Amazon Seller ID
 *                   Acme Inc,https://acme.com,A1B2C3D4E5
 *                   XYZ Corp,https://xyz-corp.com,F6G7H8I9J0
 *                   [Download input CSV](/examples/input_job.csv)
 *               companyName:
 *                 type: string
 *                 example: "Acme Inc"
 *                 description: Name of the company (required for single company processing)
 *               productUrl:
 *                 type: string
 *                 example: "https://amazon.com/dp/B07XYZ123"
 *                 description: URL of the best selling product
 *               storeFrontLink:
 *                 type: string
 *                 example: "https://amazon.com/shops/acmeinc"
 *                 description: Link to the seller's storefront
 *     responses:
 *       200:
 *         description: Job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "File uploaded successfully"
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "No file or product URL provided"
 *                 status:
 *                   type: string
 *                   example: "Fields missing. Please provide company name, website, and first name"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
  "/api/jobs/:clientId",
  adminAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      const userId = req.params.clientId;
      let job;

      if (req.file) {
        const csvFilePath = req.file.path;
        const originalFileName = req.file.originalname;

        job = await prisma.job.create({
          data: {
            userId: parseInt(userId),
            status: "pending",
            name: originalFileName,
          },
        });
        processCSV(csvFilePath, parseInt(userId), job);

        res.json({ message: "File uploaded successfully" });
      } else if (req.body.companyName) {
        try {
          const row = {
            "Seller Name": req.body.companyName || "",
            "Best Selling Product URL": req.body.productUrl || "",
            "Seller Storefront Link": req.body.storeFrontLink || "",
          };
          // const companyName = req.body.companyName;
          // const website = req.body.website;
          const clientId = parseInt(userId);
          // const firstName = req.body.firstName;
          // const productUrl = req.body.productUrl;
          if (
            !row["Seller Name"] &&
            (!row["Best Selling Product URL"] || !row["Seller Storefront Link"])
          ) {
            console.log("Fields missing", { row });
            res.status(400).json({
              status:
                "Fields missing. Please provide company name, website, and first name",
            });
          }
          job = await prisma.job.create({
            data: {
              clientId: parseInt(userId),
              status: "pending",
              name: req.body.companyName + "-singlejob",
            },
          });
          await processJsonData(row, clientId, job);
        } catch (error) {
          console.error("Error Stack:", error.stack);
          console.error("Error posting job:", error.message);
          res.status(500).json({ error: "Internal server error" });
        }

        // Process the product URL (e.g., generate PDF, etc.)

        res.json({ message: "Product URL processed successfully" });
      } else {
        res.status(400).json({ error: "No file or product URL provided" });
      }
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error posting job:", error.message);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/jobs/download-csv/{jobId}:
 *   get:
 *     summary: Download job data as CSV
 *     tags: [Jobs(Email Harvesting From Seller Page)]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *     responses:
 *       200:
 *         description: CSV file containing combined input and scraped data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               "Name","Seller URL","Amazon Seller ID","contactNumber","sellerEmail","sellerAddress","sellerPincode","inputStatus"
 *               "Acme Inc","https://acme.com","A1B2C3D4E5","123-456-7890","<EMAIL>","123 Main St, City, State","12345","completed"
 *               "XYZ Corp","https://xyz-corp.com","F6G7H8I9J0","987-654-3210","<EMAIL>","456 Oak Ave, Town, State","67890","completed"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="job_data_42.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       404:
 *         description: Job or data not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Job not found"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get("/api/jobs/download-csv/:jobId", adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.jobId);
    console.log("Downloading CSV for Job ID:", jobId);

    // Find the Job with the given ID
    const job = await prisma.job.findUnique({ where: { id: jobId } });
    if (!job) {
      return res.status(404).json({ error: "Job not found" });
    }

    // Fetch output data including the original inputData (JSON) and new fields
    const outputData = await prisma.outputData.findMany({
      where: {
        jobId,
      },
    });

    if (!outputData.length) {
      return res.status(404).json({ error: "No data found for this job" });
    }

    // Combine inputData (original) and new scraped fields
    const combinedData = outputData.map((item) => {
      const inputData = item.inputData; // Parse the inputData JSON

      return {
        ...inputData, // Include all original input fields
        sellerUrl: item.sellerUrl,
        contactNumber: item.contactNumber, // Scraped data
        sellerEmail: item.sellerEmail, // Scraped data
        sellerAddress: item.sellerAddress, // Scraped data
        sellerPincode: item.sellerPincode, // Scraped data
        inputStatus: item.inputStatus, // Scraped data status
      };
    });

    // Convert the combined data to CSV format
    const csvContent = converter.json2csv(combinedData);

    // Set the filename for the CSV file
    const analyzableCsvFileName = `job_data_${job.id}.csv`;

    // Send the CSV file in response
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${analyzableCsvFileName}`
    );
    res.setHeader("Content-Type", "text/csv");
    res.status(200).send(csvContent);
  } catch (error) {
    console.error("Error fetching output data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
