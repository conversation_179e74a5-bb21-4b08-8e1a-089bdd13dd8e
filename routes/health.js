const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient");
const fs = require("fs");
const path = require("path");
const os = require("os");

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint for Azure App Service
 *     description: |
 *       Comprehensive health check endpoint that verifies the status of various application components.
 *       Returns HTTP 200 if all systems are healthy, HTTP 503 if any critical component is unhealthy.
 *       
 *       This endpoint checks:
 *       - Database connectivity (Prisma/PostgreSQL)
 *       - File system access
 *       - Memory usage
 *       - Application uptime
 *       - Environment configuration
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Application is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "healthy"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2024-01-15T10:30:00.000Z"
 *                 uptime:
 *                   type: number
 *                   description: Application uptime in seconds
 *                   example: 3600
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 *                 environment:
 *                   type: string
 *                   example: "production"
 *                 checks:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           example: "healthy"
 *                         responseTime:
 *                           type: number
 *                           description: Database response time in milliseconds
 *                           example: 45
 *                     filesystem:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           example: "healthy"
 *                         tempDir:
 *                           type: boolean
 *                           example: true
 *                     memory:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                           example: "healthy"
 *                         usage:
 *                           type: object
 *                           properties:
 *                             used:
 *                               type: number
 *                               description: Used memory in MB
 *                               example: 256
 *                             total:
 *                               type: number
 *                               description: Total memory in MB
 *                               example: 1024
 *                             percentage:
 *                               type: number
 *                               description: Memory usage percentage
 *                               example: 25
 *       503:
 *         description: Application is unhealthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "unhealthy"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Database connection failed", "High memory usage"]
 *                 checks:
 *                   type: object
 *                   description: Detailed status of each health check
 */
router.get("/health", async (req, res) => {
  const startTime = Date.now();
  const healthCheck = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || "1.0.0",
    environment: process.env.NODE_ENV || "development",
    checks: {},
    errors: []
  };

  try {
    // 1. Database Health Check
    const dbStartTime = Date.now();
    try {
      await prisma.$queryRaw`SELECT 1`;
      healthCheck.checks.database = {
        status: "healthy",
        responseTime: Date.now() - dbStartTime
      };
    } catch (dbError) {
      healthCheck.checks.database = {
        status: "unhealthy",
        error: dbError.message,
        responseTime: Date.now() - dbStartTime
      };
      healthCheck.errors.push("Database connection failed");
      healthCheck.status = "unhealthy";
    }

    // 2. File System Health Check
    try {
      const tempDir = os.tmpdir();
      const testFile = path.join(tempDir, `health-check-${Date.now()}.tmp`);
      
      // Test write access
      fs.writeFileSync(testFile, "health check test");
      
      // Test read access
      const content = fs.readFileSync(testFile, 'utf8');
      
      // Cleanup
      fs.unlinkSync(testFile);
      
      healthCheck.checks.filesystem = {
        status: "healthy",
        tempDir: true,
        writable: true,
        readable: true
      };
    } catch (fsError) {
      healthCheck.checks.filesystem = {
        status: "unhealthy",
        error: fsError.message,
        tempDir: false
      };
      healthCheck.errors.push("File system access failed");
      healthCheck.status = "unhealthy";
    }

    // 3. Memory Health Check
    const memUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryPercentage = (usedMemory / totalMemory) * 100;

    const memoryStatus = memoryPercentage > 90 ? "unhealthy" : "healthy";
    if (memoryStatus === "unhealthy") {
      healthCheck.errors.push("High memory usage");
      healthCheck.status = "unhealthy";
    }

    healthCheck.checks.memory = {
      status: memoryStatus,
      usage: {
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        systemUsed: Math.round(usedMemory / 1024 / 1024), // MB
        systemTotal: Math.round(totalMemory / 1024 / 1024), // MB
        systemPercentage: Math.round(memoryPercentage)
      }
    };

    // 4. Application Health Check
    healthCheck.checks.application = {
      status: "healthy",
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid
    };

    // 5. Environment Health Check
    const requiredEnvVars = ['DATABASE_URL'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length > 0) {
      healthCheck.checks.environment = {
        status: "unhealthy",
        missingVariables: missingEnvVars
      };
      healthCheck.errors.push(`Missing environment variables: ${missingEnvVars.join(', ')}`);
      healthCheck.status = "unhealthy";
    } else {
      healthCheck.checks.environment = {
        status: "healthy",
        configuredVariables: requiredEnvVars.length
      };
    }

    // Add response time
    healthCheck.responseTime = Date.now() - startTime;

    // Return appropriate status code
    const statusCode = healthCheck.status === "healthy" ? 200 : 503;
    res.status(statusCode).json(healthCheck);

  } catch (error) {
    // Catch-all error handler
    healthCheck.status = "unhealthy";
    healthCheck.errors.push(`Unexpected error: ${error.message}`);
    healthCheck.responseTime = Date.now() - startTime;
    
    res.status(503).json(healthCheck);
  }
});

/**
 * @swagger
 * /health/simple:
 *   get:
 *     summary: Simple health check endpoint
 *     description: |
 *       Lightweight health check that only verifies basic application status.
 *       Suitable for load balancers and monitoring systems that need fast responses.
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Application is running
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "ok"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 */
router.get("/health/simple", (req, res) => {
  res.status(200).json({
    status: "ok",
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

/**
 * @swagger
 * /health/ready:
 *   get:
 *     summary: Readiness probe endpoint
 *     description: |
 *       Kubernetes-style readiness probe that checks if the application is ready to serve traffic.
 *       This endpoint verifies that all critical dependencies are available.
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Application is ready to serve traffic
 *       503:
 *         description: Application is not ready
 */
router.get("/health/ready", async (req, res) => {
  try {
    // Check database connectivity
    await prisma.$queryRaw`SELECT 1`;
    
    res.status(200).json({
      status: "ready",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: "not ready",
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * @swagger
 * /health/live:
 *   get:
 *     summary: Liveness probe endpoint
 *     description: |
 *       Kubernetes-style liveness probe that checks if the application is alive.
 *       This is a simple endpoint that always returns 200 if the process is running.
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Application is alive
 */
router.get("/health/live", (req, res) => {
  res.status(200).json({
    status: "alive",
    timestamp: new Date().toISOString(),
    pid: process.pid
  });
});

module.exports = router;
