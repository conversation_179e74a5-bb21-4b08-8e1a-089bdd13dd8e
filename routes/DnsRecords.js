const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { processCsvData } = require('../services/DnsRecords/dnsProcessor');
const prisma = require("../database/prisma/getPrismaClient");
const { adminAuth } = require("../middlewares/jwt");

const router = express.Router();

// Create uploads and exports directories if they don't exist
const uploadsDir = path.join(__dirname, '../uploads');
// const exportsDir = path.join(__dirname, '../exports');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Simple multer configuration
const upload = multer({ dest: "uploads/" });

// Helper function to update job in database
async function updateJob(jobId, updateData) {
  return await prisma.dNSJob.update({
    where: { id: jobId },
    data: {
      ...updateData,
    }
  });
}

/**
 * @swagger
 * /api/dnsRecords:
 *   post:
 *     summary: Process domain DNS records from CSV file
 *     description: |
 *       Uploads a CSV file containing domains to be analyzed for DNS records like MX, SPF, DKIM, DMARC, and PTR.
 *       Also follows HTTP redirects to detect final redirect location. Returns a job ID that can be used to track
 *       progress and download results.
 *       **Input CSV Example**: [DNS_Records_Sample_InputCSV](/examples/DNS_Records_Sample_InputCSV.csv)
 *     tags: [DNS Records]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: CSV file containing domains to process (first column should contain domain names)
 *             required:
 *               - csvFile
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               csvData:
 *                 type: string
 *                 description: Raw CSV data as string (first column should contain domain names)
 *             required:
 *               - csvData
 *     responses:
 *       201:
 *         description: Job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 jobId:
 *                   type: integer
 *                   example: 42
 *                 message:
 *                   type: string
 *                   example: Job created successfully
 *       400:
 *         description: No CSV data provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: No CSV data provided
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Server error
 *                 error:
 *                   type: string
 *                   example: Error message details
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post('/api/dnsRecords', adminAuth, upload.single('csvFile'), async (req, res) => {
  try {
    let csvData;
    let originalFilename = 'processDomains-input.csv';
    let inputFilePath = null;
    
    if (req.file) {
      // File was uploaded with multer
      inputFilePath = req.file.path;
      csvData = fs.readFileSync(inputFilePath, 'utf8');
      originalFilename = req.file.originalname;
    } else if (req.body.csvData) {
      // Direct input from request body
      csvData = req.body.csvData;
      
      // Save direct input to a file in uploads directory
      const timestamp = Date.now();
      inputFilePath = path.join(uploadsDir, `${timestamp}-${originalFilename}`);
      fs.writeFileSync(inputFilePath, csvData);
    } else {
      return res.status(400).json({
        success: false,
        message: 'No CSV data provided'
      });
    }
    
    // Create new job in database
    const job = await prisma.dNSJob.create({
      data: {
        originalFilename,
        status: 'pending',
        inputFilePath,
      }
    });
    
    // Process the CSV data asynchronously
    processCsvData(csvData, job.id, updateJob)
      .catch(err => console.error('Error processing job:', err));
    
    return res.status(201).json({
      success: true,
      jobId: job.id,
      message: 'Job created successfully'
    });
  } catch (error) {
    console.error('Error creating job:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/dnsRecords:
 *   get:
 *     summary: Get all DNS processing jobs
 *     description: Retrieves a list of all DNS processing jobs ordered by most recent first.
 *     tags: [DNS Records]
 *     responses:
 *       200:
 *         description: List of all DNS jobs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 count:
 *                   type: integer
 *                   example: 5
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 42
 *                       originalFilename:
 *                         type: string
 *                         example: domains.csv
 *                       status:
 *                         type: string
 *                         example: completed
 *                         enum: [pending, processing, completed, failed]
 *                       progress:
 *                         type: integer
 *                         example: 100
 *                       totalDomains:
 *                         type: integer
 *                         example: 150
 *                       processedDomains:
 *                         type: integer
 *                         example: 150
 *                       outputPath:
 *                         type: string
 *                         example: exports/domainProcessed_42_2025-04-15T14-30-00-000Z.csv
 *                       error:
 *                         type: string
 *                         example: null
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-04-15T14:30:00.000Z
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-04-15T14:45:00.000Z
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Server error
 *                 error:
 *                   type: string
 *                   example: Error message details
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.get('/api/dnsRecords', adminAuth, async (req, res) => {
  try {
    const jobs = await prisma.dNSJob.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return res.status(200).json({
      success: true,
      count: jobs.length,
      data: jobs
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/dnsRecords/{id}:
 *   get:
 *     summary: Get DNS processing job by ID
 *     description: Retrieves details for a specific DNS processing job by its ID.
 *     tags: [DNS Records]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *     responses:
 *       200:
 *         description: Job details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 42
 *                     originalFilename:
 *                       type: string
 *                       example: domains.csv
 *                     status:
 *                       type: string
 *                       example: completed
 *                       enum: [pending, processing, completed, failed]
 *                     progress:
 *                       type: integer
 *                       example: 100
 *                     totalDomains:
 *                       type: integer
 *                       example: 150
 *                     processedDomains:
 *                       type: integer
 *                       example: 150
 *                     outputPath:
 *                       type: string
 *                       example: exports/domainProcessed_42_2025-04-15T14-30-00-000Z.csv
 *                     error:
 *                       type: string
 *                       example: null
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-04-15T14:30:00.000Z
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-04-15T14:45:00.000Z
 *       400:
 *         description: Invalid job ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid job ID
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Job not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Server error
 *                 error:
 *                   type: string
 *                   example: Error message details
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.get('/api/dnsRecords/:id', adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id, 10);
    
    if (isNaN(jobId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid job ID'
      });
    }

    const job = await prisma.dNSJob.findUnique({
      where: {
        id: jobId
      }
    });
    
    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: job
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/dnsRecords/download/{id}:
 *   get:
 *     summary: Download processed DNS records CSV
 *     description: |
 *       Downloads the processed CSV file for a completed DNS processing job.
 *       The CSV contains the original domain data plus DNS record information.
 *       **Output CSV Example**: [DNS_Records_Sample_InputCSV](/examples/DNS_Records_Sample_OutputCSV.csv)
 *     tags: [DNS Records]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *     responses:
 *       200:
 *         description: CSV file containing processed DNS records
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               DomainName,OriginalData1,OriginalData2,Final Redirect Location,MX,SPF,DKIM,DMARC,PTR,Last Check At
 *               example.com,data1,data2,https://www.example.com,mx.example.com (10),v=spf1 include:_spf.example.com ~all,selector1: v=DKIM1; k=rsa; p=MIIBIj...,_dmarc.example.com: v=DMARC1; p=reject; rua=mailto:<EMAIL>,*********: example-host.example.com,4/15/2025 10:30:00 AM
 *               example.org,data1,data2,https://www.example.org,N/A,N/A,N/A,N/A,N/A,4/15/2025 10:30:01 AM
 *       400:
 *         description: Invalid job ID or job not completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Job is not completed yet
 *       404:
 *         description: Job or output file not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Output file not found
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Server error
 *                 error:
 *                   type: string
 *                   example: Error message details
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.get('/api/dnsRecords/download/:id', adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id, 10);

    if (isNaN(jobId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid job ID'
      });
    }

    const job = await prisma.dNSJob.findUnique({
      where: {
        id: jobId
      }
    });
    
    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }
    
    if (job.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Job is not completed yet'
      });
    }
    
    if (!job.outputPath || !fs.existsSync(job.outputPath)) {
      return res.status(404).json({
        success: false,
        message: 'Output file not found'
      });
    }
    
    // Send file as download
    res.download(job.outputPath);
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

module.exports = router;