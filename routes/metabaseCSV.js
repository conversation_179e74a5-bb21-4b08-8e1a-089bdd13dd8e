const express = require("express");
const router = express.Router();
const { adminAuth } = require("../middlewares/jwt");
const {
  deleteAllUploadCsvTables,
  deleteIndividualUploadCsvTable,
  listUploadCsvTables,
} = require("../utils/metabaseCsv");

/**
 * @swagger
 * /api/metabase/upload-csv-tables:
 *   get:
 *     summary: List all upload CSV tables
 *     description: |
 *       Retrieves a list of all tables with the prefix "uploadcsv_public_" from the default schema (public2).
 *       This endpoint is useful for monitoring and verifying tables before deletion.
 *       The schema is fixed to 'public2' and cannot be customized via query parameters.
 *     tags: [Metabase]
 *     responses:
 *       200:
 *         description: List of upload CSV tables
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Found 5 tables with prefix 'uploadcsv_public_'"
 *                 count:
 *                   type: integer
 *                   example: 5
 *                 tables:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       index:
 *                         type: integer
 *                         example: 1
 *                       table_name:
 *                         type: string
 *                         example: "uploadcsv_public_company_data_20231015"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error listing tables"
 *                 details:
 *                   type: string
 *                   example: "Database connection failed"
 */
router.get("/api/metabase/upload-csv-tables", adminAuth, async (req, res) => {
  try {
    const tables = await listUploadCsvTables();

    // Add safety check for undefined/null tables
    if (!tables || !Array.isArray(tables)) {
      console.warn("listUploadCsvTables returned:", tables);
      return res.status(200).json({
        success: true,
        message: "No tables found or error retrieving tables",
        count: 0,
        tables: [],
      });
    }

    const response = {
      success: true,
      message: `Found ${tables.length} tables with prefix "uploadcsv_public_"`,
      count: tables.length,
      tables: tables.map((table, index) => ({
        index: index + 1,
        table_name: table.table_name,
      })),
    };

    console.log(`API response: Listed ${tables.length} tables`);
    res.status(200).json(response);
  } catch (error) {
    console.error("API Error listing upload CSV tables:", error);
    res.status(500).json({
      success: false,
      error: "Error listing tables",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/metabase/delete-csv-tables/{tableName}:
 *   delete:
 *     summary: Delete individual upload CSV table
 *     description: |
 *       Deletes a specific table by name from the public2 schema.
 *       This operation is irreversible and should be used with caution.
 *
 *       **Warning**: This will permanently delete the specified table and its data.
 *     tags: [Metabase]
 *     parameters:
 *       - in: path
 *         name: tableName
 *         schema:
 *           type: string
 *         required: true
 *         description: The exact name of the table to delete
 *         example: "uploadcsv_public_company_data_20231015"
 *       - in: query
 *         name: confirm
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'false'
 *         required: false
 *         description: Confirmation parameter (must be 'true' to proceed)
 *     responses:
 *       200:
 *         description: Table deleted successfully or not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully deleted table \"uploadcsv_public_test\" from schema \"public2\""
 *                 deletedTable:
 *                   type: string
 *                   nullable: true
 *                   example: "uploadcsv_public_test"
 *                 error:
 *                   type: string
 *                   nullable: true
 *                   example: null
 *       400:
 *         description: Bad request - missing confirmation or invalid table name
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Confirmation required"
 *                 message:
 *                   type: string
 *                   example: "Please add '?confirm=true' to the request to confirm deletion"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error deleting table"
 *                 details:
 *                   type: string
 *                   example: "Database connection failed"
 */
router.delete(
  "/api/metabase/delete-csv-tables/:tableName",
  adminAuth,
  async (req, res) => {
    try {
      const { tableName } = req.params;
      const confirm = req.query.confirm || "false"; // Default to 'false'

      // Validate table name
      if (!tableName || tableName.trim() === "") {
        return res.status(400).json({
          success: false,
          error: "Invalid table name",
          message: "Table name is required and cannot be empty",
        });
      }

      // Safety check - require explicit confirmation
      if (confirm !== "true") {
        return res.status(400).json({
          success: false,
          error: "Confirmation required",
          message:
            "Please add '?confirm=true' to the request to confirm deletion",
        });
      }

      console.log(`API request to delete individual table: ${tableName}`);

      const result = await deleteIndividualUploadCsvTable(tableName);

      // Return the result from the function (which already includes success status)
      console.log(
        `API response for table ${tableName}:`,
        result.success ? "Success" : "Failed"
      );
      res.status(200).json(result);
    } catch (error) {
      console.error("API Error deleting individual upload CSV table:", error);
      res.status(500).json({
        success: false,
        error: "Error deleting table",
        details: error.message,
      });
    }
  }
);

/**
 * @swagger
 * /api/metabase/delete-csv-tables:
 *   delete:
 *     summary: Delete all upload CSV tables
 *     description: |
 *       Deletes all tables with the prefix "uploadcsv_public_" from the public2 schema.
 *       This operation is irreversible and should be used with caution.
 *
 *       **Warning**: This will permanently delete all matching tables and their data.
 *     tags: [Metabase]
 *     parameters:
 *       - in: query
 *         name: confirm
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *           default: 'false'
 *         required: false
 *         description: Confirmation parameter (must be 'true' to proceed)
 *     responses:
 *       200:
 *         description: Tables deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully deleted 5 tables"
 *                 deletedCount:
 *                   type: integer
 *                   example: 5
 *                 deletedTables:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["uploadcsv_public_table1", "uploadcsv_public_table2"]
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       table:
 *                         type: string
 *                       error:
 *                         type: string
 *                   example: []
 *       400:
 *         description: Bad request - missing confirmation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Confirmation required"
 *                 message:
 *                   type: string
 *                   example: "Please add '?confirm=true' to the request to confirm deletion"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error deleting tables"
 *                 details:
 *                   type: string
 *                   example: "Database connection failed"
 */
router.delete(
  "/api/metabase/delete-csv-tables",
  adminAuth,
  async (req, res) => {
    try {
      const confirm = req.query.confirm || "false"; // Default to 'false'

      // Safety check - require explicit confirmation
      if (confirm !== "true") {
        return res.status(400).json({
          success: false,
          error: "Confirmation required",
          message:
            "Please add '?confirm=true' to the request to confirm deletion",
        });
      }

      console.log(`API request to delete all upload CSV tables`);

      const result = await deleteAllUploadCsvTables();

      const response = {
        success: true,
        message: `Successfully deleted ${result.deletedCount} tables`,
        deletedCount: result.deletedCount,
        deletedTables: result.deletedTables,
        errors: result.errors,
      };

      console.log(`API response: Deleted ${result.deletedCount} tables`);
      res.status(200).json(response);
    } catch (error) {
      console.error("API Error deleting upload CSV tables:", error);
      res.status(500).json({
        success: false,
        error: "Error deleting tables",
        details: error.message,
      });
    }
  }
);

module.exports = router;
