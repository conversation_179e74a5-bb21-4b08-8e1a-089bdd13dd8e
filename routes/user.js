const express = require("express");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const prisma = require("../database/prisma/getPrismaClient");

const router = express.Router();

const JWT_SECRET = process.env.JWT_SECRET;
const { userAuth } = require("../middlewares/jwt");

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 1
 *         name:
 *           type: string
 *           example: "<PERSON>e"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2023-10-15T14:30:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           example: "2023-10-15T14:30:00Z"
 */

/**
 * @swagger
 * /api/signup:
 *   post:
 *     summary: Register a new user
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *                 example: "John Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "securePassword123"
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User created successfully"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post("/api/signup", async (req, res) => {
  console.log("New User Signup Request", req.body);
  const { name, email, password } = req.body;
  const hashedPassword = await bcrypt.hash(password, 10);

  try {
    // give me a random 6 digit number
    const number = Math.floor(100000 + Math.random() * 900000);
    await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
      },
    });

    res.status(201).json({ message: "User created successfully" });
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error creating user:", error.message);

    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/login:
 *   post:
 *     summary: Login user
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "securePassword123"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *         headers:
 *           Authorization:
 *             schema:
 *               type: string
 *             description: JWT token in the format "Bearer {token}"
 *             example: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *           Set-Cookie:
 *             schema:
 *               type: string
 *             description: HTTP-only cookie containing the JWT token
 *             example: "token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly"
 *       401:
 *         description: Invalid email or password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid email or password"
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.post("/api/login", async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (!user || !(await bcrypt.compare(password, user.password))) {
      return res.status(401).json({ error: "Invalid email or password" });
    }

    const accessToken = jwt.sign({ userId: user.id }, JWT_SECRET, {
      expiresIn: "7d",
    });
    res.setHeader("Authorization", `Bearer ${accessToken}`);
    res.setHeader("Set-Cookie", `token=${accessToken}; HttpOnly`);

    res.status(200).json(user);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error logging in user:", error.message);

    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get("/api/me", userAuth, async (req, res) => {
  const userId = req.user.userId;

  try {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!user) {
      await prisma.requestLog.update({
        where: {
          id: rqObject.id,
        },
        data: {
          response: { error: "User not found" },
          statusCode: 404,
          updatedAt: new Date(),
        },
      });

      return res.status(404).json({ error: "User not found" });
    }

    const { password, ...rest } = user;

    res.status(200).json(rest);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching user:", error.message);

    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/user/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logged out successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Logged out successfully"
 *         headers:
 *           Set-Cookie:
 *             schema:
 *               type: string
 *             description: Clears the token cookie
 *             example: "token=; HttpOnly; Max-Age=0"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.post("/api/user/logout", userAuth, async (req, res) => {
  try {
    res.setHeader("Set-Cookie", "token=; HttpOnly; Max-Age=0");
    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Error logging out user:", error.message);
    Sentry.captureException(`Error logging out user: ${error.message}`);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
