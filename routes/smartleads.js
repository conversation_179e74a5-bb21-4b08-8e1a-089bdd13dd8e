const express = require("express");
const prisma = require("../database/prisma/getPrismaClient");
const router = express.Router();

router.post("/api/lead-cateogry-updation", async (req, res) => {
  try {
    const data = req.body;

    // console.log("Received Data:", data);

    try {
      const updatedLeads = await prisma.smartLead_Lead.updateMany({
        where: {
          leadId: `${data.lead_id}`,
          campaign: {
            campaignId: data.campaign_id,
          },
        },
        data: {
          lead_category_id: data.lead_category.new_id,
        },
      });

      if (updatedLeads.count === 0) {
        console.log("No leads found to update.");
        res.status(404).send("No leads found with the provided criteria.");
        return;
      }

      console.log(`Updated Leads Count: ${updatedLeads.count} ID: ${data.lead_id}`);
      res.status(200).send(`Updated ${updatedLeads.count} lead(s) category`);
    } catch (error) {
      console.error("Error updating lead category:", error);
      res.status(500).send("Internal Server Error");
    }
  } catch (error) {
    console.error("Error handling request:", error);
    res.status(500).send("Internal Server Error");
  }
});


module.exports = router;
