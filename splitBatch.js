const fs = require("fs");
const path = require("path");
const csv = require("csvtojson");
const { Parser } = require("json2csv");

async function splitCsvFile(inputFilePath, outputDir, rowsPerFile = 1000) {
  try {
    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Read the CSV file
    const jsonArray = await csv().fromFile(inputFilePath);
    const totalRows = jsonArray.length;
    const totalFiles = Math.ceil(totalRows / rowsPerFile);

    console.log(`Total rows: ${totalRows}`);
    console.log(
      `Splitting into ${totalFiles} files with ${rowsPerFile} rows each`
    );

    // Get the headers from the first row
    const headers = Object.keys(jsonArray[0]);
    const parser = new Parser({ fields: headers });

    // Split the data into chunks and write to separate files
    for (let i = 0; i < totalFiles; i++) {
      const start = i * rowsPerFile;
      const end = Math.min(start + rowsPerFile, totalRows);
      const chunk = jsonArray.slice(start, end);

      const csvData = parser.parse(chunk);
      const outputFilePath = path.join(
        outputDir,
        `part_${i + 1}_of_${totalFiles}.csv`
      );

      fs.writeFileSync(outputFilePath, csvData);
      console.log(`Created file: ${outputFilePath} with ${chunk.length} rows`);
    }

    console.log("CSV splitting completed successfully!");
  } catch (error) {
    console.error("Error splitting CSV file:", error);
  }
}

// Get the input file path from command line arguments or use default
const inputFilePath = process.argv[2] || path.join(__dirname, "./13Aug.csv");
const outputDir = process.argv[3] || path.join(__dirname, "split_output");
const rowsPerFile = parseInt(process.argv[4]) || 500;

splitCsvFile(inputFilePath, outputDir, rowsPerFile);  
